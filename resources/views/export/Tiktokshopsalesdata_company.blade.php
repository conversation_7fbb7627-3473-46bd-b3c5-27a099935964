@foreach ($data as $name_page => $dataloop)
    @php
            $count_colspan = count($item);
            $n = 20;
            $promotion_item = [];
            $total_orders = 0;
            $total_sales = 0;
            $total_cost = 0;
            $total_box_cost = 0;
            $total_ad_cost = 0;
            $total_net_profit = 0;
            $total_item_sup = array_fill_keys(array_column($item, 'name'), 0);
            foreach ($dataloop as $order) {
                foreach ($order['sale_order_lines'] as $line) {
                    $promotion_name = $line['item']['name'];
                    if (!isset($promotion_item[$promotion_name])) {
                        $promotion_item[$promotion_name] = [
                            'sku' => $line['item']['sku'],
                            'orders' => 0,
                            'items_sup' => [],
                            'sales_total' => 0,
                            'cost_total' => 0,
                            'box_cost' => 0,
                            'ad_cost' => 5,
                            'net_profit' => 0,
                        ];
                    }
                    $promotion_item[$promotion_name]['orders']++;
                    $promotion_item[$promotion_name]['sales_total'] += $line['total'];
                    $total_units = 0;
                    foreach ($line['item']['main_item_line'] as $mainItem) {
                        $item_name_sup = $mainItem['item']['name'];
                        if (!isset($promotion_item[$promotion_name]['items_sup'][$item_name_sup])) {
                            $promotion_item[$promotion_name]['items_sup'][$item_name_sup] = 0;
                        }
                        $promotion_item[$promotion_name]['items_sup'][$item_name_sup] += $mainItem['qty'];
                        $promotion_item[$promotion_name]['cost_total'] += $mainItem['item']['unit_cost'] * $mainItem['qty'];
                        $total_item_sup[$item_name_sup] += $mainItem['qty'];
                        $total_units += $mainItem['qty'];
                    }
                    $promotion_item[$promotion_name]['box_cost'] = $promotion_item[$promotion_name]['orders'] * 5;
                    $promotion_item[$promotion_name]['net_profit'] = $promotion_item[$promotion_name]['sales_total'] - $promotion_item[$promotion_name]['cost_total'] - $promotion_item[$promotion_name]['box_cost'] - $promotion_item[$promotion_name]['ad_cost'];
                }
            }
            foreach ($promotion_item as $promotion) {
                $total_orders += $promotion['orders'];
                $total_sales += $promotion['sales_total'];
                $total_cost += $promotion['cost_total'];
                $total_box_cost += $promotion['box_cost'];
                $total_ad_cost += $promotion['ad_cost'];
                $total_net_profit += $promotion['net_profit'];
            }
            $total_net = ($total_sales * 0.03) + ($total_sales * 0.04) + ($total_sales * 0.03);
            $profit = $total_net_profit - $total_net;

    @endphp

    <table>
        <tr>
            <td colspan="16" rowspan="3" style="border: 2px solid black; text-align: center; background-color: #d9d9d9;" >Tik Tok Shop {{ $name_page }}</td>
        </tr>
        <tr></tr>
        <tr></tr>
        <tr>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #f6b26b; width: 315px;">ชื่อสินค้า</td>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #f6b26b; width: 140px;">SKU</td>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #f6b26b; width: 100px;">จำนวนออเดอร์</td>
            <td colspan="{{$count_colspan}}" style="border: 2px solid black; text-align: center; background-color: #ffd966; width: 100px;">จำนวนกระปุก</td>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #ffd966; width: 100px;">ยอดขาย</td>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #a2c4c9; width: 100px;">ต้นทุนสินค้า</td>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #a2c4c9; width: 100px;">ค่ากล่อง</td>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #a2c4c9; width: 100px;">ค่าแอท</td>
            <td rowspan="2" style="border: 2px solid black; text-align: center; background-color: #ffff00; width: 200px;">กำไรสุทธิหลังหักต้นทุนสินค้า</td>
        </tr>
        <tr>
            {{-- <td style="border: 2px solid black; text-align: center; background-color: #4285f4; width: 100px;">โปรแอค</td>
            <td style="border: 2px solid black; text-align: center; background-color: #ffff00; width: 100px;">Tomiko</td>
            <td style="border: 2px solid black; text-align: center; background-color: #00ff00; width: 100px;">Tomiko พลัส</td>
            <td style="border: 2px solid black; text-align: center; background-color: #ff0000; width: 100px;">น้ำชงเล็ก</td>
            <td style="border: 2px solid black; text-align: center; background-color: #fff2cc; width: 100px;">น้ำชงใหญ่</td>
            <td style="border: 2px solid black; text-align: center; background-color: #fff2cc; width: 100px;">น้ำชงใหญ่พลัส</td>
            <td style="border: 2px solid black; text-align: center; background-color: #fff2cc; width: 100px;">ยูอาร์ม่วง</td>
            <td style="border: 2px solid black; text-align: center; background-color: #fff2cc; width: 100px;">เอซีแดง</td> --}}
        @foreach ($item as $itemloop)
            <td style="border: 2px solid black; text-align: center; background-color: #fff2cc; width: 100px;">{{$itemloop['name']}}</td>
        @endforeach
        </tr>

        @foreach ($promotion_item as $promotion_name => $promotion)
            @php
                $n--
            @endphp
            <tr>
                <td style="border: 2px solid black; ">{{ $promotion_name }}</td>
                <td style="border: 2px solid black; text-align: center;">{{ $promotion['sku'] }}</td>
                <td style="border: 2px solid black; text-align: center;">{{ $promotion['orders'] }}</td>
                @foreach ($item as $itemloop)
                    @php
                        $item_name_sup = $itemloop['name'];
                        $totalQty = $promotion['items_sup'][$item_name_sup] ?? '';
                        $bg_color = $totalQty == '' ? '#b7b7b7' : '#fff2cc';
                    @endphp
                    <td style="border: 2px solid black; text-align: center; background-color: {{ $bg_color }}; width: 100px;">{{ $totalQty }}</td>
                @endforeach
                <td style="border: 2px solid black; text-align: center; background-color: #ffd966;">{{ $promotion['sales_total'] }}</td>
                <td style="border: 2px solid black; text-align: center; background-color: #a2c4c9;">{{ $promotion['cost_total'] }}</td>
                <td style="border: 2px solid black; text-align: center; background-color: #a2c4c9;">{{ $promotion['box_cost'] }}</td>
                <td style="border: 2px solid black; text-align: center; background-color: #a2c4c9;">{{ $promotion['ad_cost'] }}</td>
                <td style="border: 2px solid black; text-align: center; background-color: #ffff00;">{{ $promotion['net_profit'] }}</td>
            </tr>
        @endforeach
        @for ($i=0; $i<$n; $i++)
            <tr>
                <td style="border: 2px solid black;"></td>
                <td style="border: 2px solid black;"></td>
                <td style="border: 2px solid black;"></td>
                @foreach ($item as $itemloop)
                    <td style="border: 2px solid black; text-align: center; background-color: #b7b7b7;"></td>
                @endforeach
                <td style="border: 2px solid black; text-align: center; background-color: #ffd966;"></td>
                <td style="border: 2px solid black; text-align: center; background-color: #a2c4c9;"></td>
                <td style="border: 2px solid black; text-align: center; background-color: #a2c4c9;"></td>
                <td style="border: 2px solid black; text-align: center; background-color: #a2c4c9;"></td>
                <td style="border: 2px solid black; text-align: center; background-color: #ffff00;"></td>
            </tr>
        @endfor

        <!-- Sum -->
        <tr>
            <td colspan="2" style="border: 2px solid black; text-align: center; font-weight: bold; background-color: #d9d9d9;">Total</td>
            <td style="border: 2px solid black; text-align: center; font-weight: bold;">{{ $total_orders }}</td>
            @foreach ($item as $itemloop)
                @php
                    $item_name_sup = $itemloop['name'];
                    $totalQty = $total_item_sup[$item_name_sup] ?? 0;
                    $bg_color = $totalQty == 0 ? '#b7b7b7' : '#fff2cc';
                @endphp
                <td style="border: 2px solid black; text-align: center; background-color: {{ $bg_color }}; font-weight: bold;">{{ $totalQty }}</td>
            @endforeach
            <td style="border: 2px solid black; text-align: center; font-weight: bold; background-color: #ffd966;">{{ $total_sales }}</td>
            <td style="border: 2px solid black; text-align: center; font-weight: bold; background-color: #a2c4c9;">{{ $total_cost }}</td>
            <td style="border: 2px solid black; text-align: center; font-weight: bold; background-color: #a2c4c9;">{{ $total_box_cost }}</td>
            <td style="border: 2px solid black; text-align: center; font-weight: bold; background-color: #a2c4c9;">{{ $total_ad_cost }}</td>
            <td style="border: 2px solid black; text-align: center; font-weight: bold; background-color: #ffff00;">{{ $total_net_profit }}</td>
        </tr>
        <tr></tr>
        <tr></tr>
        <tr></tr>

        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ข้อมูล จาก File Report</td>
            <td colspan="3" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">อัตราค่าคอม</td>
            <td style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">tiktok 1 </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ยอดรวมทั้งหมด</td>
            <td colspan="3" style="border: 2px solid black; background-color: #d9ead3;">1.00</td>
            <td style="border: 2px solid black; background-color: #d9ead3;">{{ $total_sales }}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ค่าธรรมเนียมคำสั่งซื้อ ผ่านภารกิจไม่เสีย 3%</td>
            <td colspan="3" style="border: 2px solid black; background-color: #f4cccc;">0.03</td>
            <td style="border: 2px solid black; background-color: #f4cccc;">{{ $total_sales *3/100}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ค่าคอมมิชชั่น TikTok Shop</td>
            <td colspan="3" style="border: 2px solid black; background-color: #f4cccc;">0.04</td>
            <td style="border: 2px solid black; background-color: #f4cccc;">{{ $total_sales *4/100}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ยอดรวมค่าจัดส่งที่ร้านค้าจ่ายจริง</td>
            <td colspan="3" style="border: 2px solid black; background-color: #f4cccc;">0.00</td>
            <td style="border: 2px solid black; background-color: #f4cccc;"></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ค่าคอมมิชชั่นพันธมิตร</td>
            <td colspan="3" style="border: 2px solid black; background-color: #f4cccc;">0.00</td>
            <td style="border: 2px solid black; background-color: #f4cccc;"></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ค่าธรรมเนียมการบริการ SFP</td>
            <td colspan="3" style="border: 2px solid black; background-color: #f4cccc;">0.03</td>
            <td style="border: 2px solid black; background-color: #f4cccc;">{{ $total_sales *3/100}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;"></td>
            <td colspan="3" style="border: 2px solid black; background-color: #f4cccc;"></td>
            <td style="border: 2px solid black; background-color: #f4cccc;"></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="6" style="border: 2px solid black; font-weight: bold; background-color: #a4c2f4;">ยอดรวมสุทธิ</td>
            <td colspan="3" style="border: 2px solid black; background-color: #ea9999;">0.09</td>
            <td style="border: 2px solid black; background-color: #00ff00;">{{$total_net}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td colspan="4" style="border: 2px solid black; background-color: #ffff00;">กำไรสุทธิหลังหักต้นทุนสินค้า</td>
            <td style="border: 2px solid black; background-color: #ffff00;">{{ $total_net_profit }}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td style="border: 2px solid black; background-color: #00ff00;">กำไร</td>
            <td style="border: 2px solid black; background-color: #00ff00;">{{ $profit }}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </table>
@endforeach
