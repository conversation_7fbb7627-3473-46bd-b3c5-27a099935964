@php
   use Carbon\Carbon;

    $startDate = Carbon::parse($month)->startOfMonth()->format('d/m/Y');
    $endDate = Carbon::parse($month)->endOfMonth()->format('d/m/Y');

@endphp

<table>
    <tr>
        <td></td>
    </tr>
    <tr>
        <td></td>
    </tr>
    <tr>
        <td></td>
        <td colspan="12" ></td>
        <td colspan="8" style="border: 2px solid black; text-align: center; background-color: #e06666;"><b>สรุปยอดขายTik Tok Shop</b></td>
        <td colspan="14" ></td>
    </tr>
    <tr>
        <td></td>
        <td colspan="12" style="border-top: 3px solid black; border-left: 3px solid black;"></td>
        <td colspan="8" style="border: 2px solid black; text-align: center; background-color: #e06666;"><b>ประจำรอบ {{ $startDate }} - {{ $endDate }}</b></td>
        <td colspan="14" style="border-top: 3px solid black; border-right: 3px solid black;"></td>
    </tr>
    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #e06666; width: 150px;" ><b>วันที่</b></td>
        @for ($day=1;$day <= 31;$day++)
            <td style="border: 2px solid black; text-align: center; background-color: #f3f3f3; width: 100px;"><b>{{$day}}</b></td>
        @endfor
        <td style="border: 2px solid black; text-align: center; background-color: #ffff00;"><b>รวม/เดือน</b></td>
        <td style="border: 2px solid black; text-align: center; background-color: #f3f3f3; color: red;" ><b>deff</b></td>
    </tr>

    {{-- -------------------------------------------------------------------------------------------------------------------------- --}}

    @php
        $totalOrders = 0;       //จำนวนออเดอร์
        $totalSales = 0.0;      //ยอดขาย
        $totalCosts = 0.0;      //ต้นทุนสินค้า
        $totalBoxCosts = 0.0;   //ค่ากล่อง
        $totalAds = 0.0;        //ค่า ads
        $totalNetProfit = 0.0;  //กำไรสุทธิ
    @endphp

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>จำนวนออเดอร์</b></td>
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $orderCount = isset($data[$day]) ? count($data[$day]) : '-';
                if (is_numeric($orderCount)) {
                    $totalOrders += $orderCount; //นับตาม array
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ $orderCount }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ $totalOrders }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;"></td>
    </tr>

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ยอดขาย</b></td>
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $daySales = 0;
                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $daySales += $order['total'];   //เอา total มารวมตาม order
                    }
                } else {
                    $daySales = '-';
                }
                if (is_numeric($daySales)) {
                    $totalSales += $daySales;   //รวมผลรวม total มาใส่ช่องท้าย
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ $daySales !== '-' ? number_format($daySales, 2) : '-' }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalSales, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;"></td>
    </tr>
    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ต้นทุนสินค้า</b></td>
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dayCosts = 0;
                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        foreach ($order['sale_order_lines'] as $line) {
                            foreach ($line['item']['main_item_line'] as $mainItem) {
                                $dayCosts += $mainItem['item']['unit_cost'] * $mainItem['qty'];
                            }
                        }
                    }
                } else {
                    $dayCosts = '-';
                }
                if (is_numeric($dayCosts)) {
                    $totalCosts += $dayCosts;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ $dayCosts !== '-' ? number_format($dayCosts, 2) : '-' }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalCosts, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;"></td>
    </tr>

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่ากล่อง</b></td>
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dayBoxCosts = isset($data[$day]) ? count($data[$day]) * 5 : '-';
                if (is_numeric($dayBoxCosts)) {
                    $totalBoxCosts += $dayBoxCosts;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ $dayBoxCosts !== '-' ? number_format($dayBoxCosts, 2) : '-' }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalBoxCosts, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;"></td>
    </tr>

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่า Ads</b></td>
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dayAds = '-'; // fix ไว้ก่อน
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ $dayAds }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ '-' }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;"></td>
    </tr>

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>กำไรสุทธิ</b></td>
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $daySales = 0;
                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $daySales += $order['total'];   //เอา total มารวมตาม order
                    }
                } else {
                    $daySales = 0;
                }
                // isset($data[$day]) ? array_sum(array_column($data[$day], 'total')) : 0;
                $dayCosts = 0;
                $dayBoxCosts = isset($data[$day]) ? count($data[$day]) * 5 : 0;
                $dayAds = 0; // fix
                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        foreach ($order['sale_order_lines'] as $line) {
                            foreach ($line['item']['main_item_line'] as $mainItem) {
                                $dayCosts += $mainItem['item']['unit_cost'] * $mainItem['qty'];
                            }
                        }
                    }
                }
                $dayNetProfit = $daySales - ($dayCosts + $dayBoxCosts + $dayAds);
                $totalNetProfit += $dayNetProfit;
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($dayNetProfit, 2) }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalNetProfit, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;"></td>
    </tr>

    {{-- -------------------------------------------------------------------------------------------------------------------------- --}}

    @foreach ($item as $index => $product)
        <tr>
            @if ($index == 0)
                <td rowspan="{{ count($item) + 2 }}" style="border: 2px solid black; text-align: center; background-color: #5b9bd5;">สินค้า</td>
            @endif
            <td style="border: 2px solid black; text-align: center; background-color: #5b9bd5;"><b>{{ $product['name'] }}</b></td>
            @php
                $productTotal = 0;
            @endphp
            @for ($i = 1; $i <= 31; $i++)
                @php
                    $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                    $dailyCount = 0;

                    if (isset($data[$day])) {
                        foreach ($data[$day] as $order) {
                            foreach ($order['sale_order_lines'] as $line) {
                                foreach ($line['item']['main_item_line'] as $mainItem) {
                                    if ($mainItem['item']['id'] == $product['id']) {
                                        $dailyCount += $mainItem['qty'];
                                    }
                                }
                            }
                        }
                    } else {
                        $dailyCount = '-';
                    }

                    if (is_numeric($dailyCount)) {
                        $productTotal += $dailyCount;
                    }
                @endphp
                <td style="border: 2px solid black; text-align: right; background-color: #f3f3f3;">{{ $dailyCount }}</td>
            @endfor
            <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ $productTotal }}</td>
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ $productTotal }}</td>
        </tr>
    @endforeach

    @for ($i=0; $i<2; $i++)
        <tr>
            <td style="border: 2px solid black; text-align: center; background-color: #5b9bd5;"><b></b></td>
            @for ($j = 1; $j <= 31; $j++)
                <td style="border: 2px solid black; text-align: right; background-color: #f3f3f3;"></td>
            @endfor
            <td style="border: 2px solid black; text-align: right; background-color: #e69138;"></td>
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;"></td>
        </tr>
    @endfor

    {{-- -------------------------------------------------------------------------------------------------------------------------- --}}

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าธรรมเนียมคำสั่งซื้อ ผ่านภารกิจไม่เสีย 3%</b></td>
        @php
            $totalFee = 0;
        @endphp
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dailyFee = 0;

                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $dailyFee += $order['total'] * 0.03;
                    }
                } else {
                    $dailyFee = '-';
                }

                if (is_numeric($dailyFee)) {
                    $totalFee += $dailyFee;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ is_numeric($dailyFee) ? number_format($dailyFee, 2) : $dailyFee }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalFee, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ number_format($totalFee, 2) }}</td>
    </tr>
    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าคอมมิชชั่น TikTok Shop</b></td>
        @php
            $totalCommission = 0;
        @endphp
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dailyCommission = 0;

                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $dailyCommission += $order['total'] * 0.04;
                    }
                } else {
                    $dailyCommission = '-';
                }

                if (is_numeric($dailyCommission)) {
                    $totalCommission += $dailyCommission;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ is_numeric($dailyCommission) ? number_format($dailyCommission, 2) : $dailyCommission }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalCommission, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ number_format($totalCommission, 2) }}</td>
    </tr>
    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ยอดรวมค่าจัดส่งที่ร้านค้าจ่ายจริง</b></td>
        @php
            $totalShipping = 0;
        @endphp
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dailyShipping = 0;

                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $dailyShipping += $order['total'] * 0.00; // ค่อยมาปรับ
                    }
                } else {
                    $dailyShipping = '-';
                }

                if (is_numeric($dailyShipping)) {
                    $totalShipping += $dailyShipping;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ is_numeric($dailyShipping) ? number_format($dailyShipping, 2) : $dailyShipping }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalShipping, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ number_format($totalShipping, 2) }}</td>
    </tr>

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าคอมมิชชั่นพันธมิตร</b></td>
        @php
            $totalPartnerCommission = 0;
        @endphp
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dailyPartnerCommission = 0;

                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $dailyPartnerCommission += $order['total'] * 0.00; // ค่อยมาปรับ
                    }
                } else {
                    $dailyPartnerCommission = '-';
                }

                if (is_numeric($dailyPartnerCommission)) {
                    $totalPartnerCommission += $dailyPartnerCommission;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ is_numeric($dailyPartnerCommission) ? number_format($dailyPartnerCommission, 2) : $dailyPartnerCommission }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalPartnerCommission, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ number_format($totalPartnerCommission, 2) }}</td>
    </tr>

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าธรรมเนียมการบริการ SFP</b></td>
        @php
            $totalSFPFee = 0;
        @endphp
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dailySFPFee = 0;

                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $dailySFPFee += $order['total'] * 0.03;
                    }
                } else {
                    $dailySFPFee = '-';
                }

                if (is_numeric($dailySFPFee)) {
                    $totalSFPFee += $dailySFPFee;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ is_numeric($dailySFPFee) ? number_format($dailySFPFee, 2) : $dailySFPFee }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalSFPFee, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ number_format($totalSFPFee, 2) }}</td>
    </tr>

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ภาษี Vat 7%</b></td>
        @php
            $totalVat = 0;
        @endphp
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dailyVat = 0;

                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $dailyVat += $order['total'] * 0.07;
                    }
                } else {
                    $dailyVat = '-';
                }

                if (is_numeric($dailyVat)) {
                    $totalVat += $dailyVat;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ is_numeric($dailyVat) ? number_format($dailyVat, 2) : $dailyVat }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($totalVat, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ number_format($totalVat, 2) }}</td>
    </tr>

    <tr>
        <td colspan="2" style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>กำไรสุทธิ หลังหักต้นทุนสินค้า+ภาษี</b></td>
        @php
            $netProfitTotal = 0;
        @endphp
        @for ($i = 1; $i <= 31; $i++)
            @php
                $day = str_pad($i, 2, '0', STR_PAD_LEFT);
                $dayNetProfit = 0;

                if (isset($data[$day])) {
                    foreach ($data[$day] as $order) {
                        $orderTotal = $order['total'];
                        $orderCost = 0;
                        foreach ($order['sale_order_lines'] as $line) {
                            foreach ($line['item']['main_item_line'] as $mainItem) {
                                $orderCost += $mainItem['item']['unit_cost'] * $mainItem['qty'];
                            }
                        }
                        // $orderCost = $order['sale_order_lines'][0]['item']['unit_cost'];
                        $orderBoxCost = count($order['sale_order_lines']) * 5;
                        $orderAdsCost = 0; //fix ใว้ก่อน

                        $orderFee = $orderTotal * 0.03;
                        $orderCommission = $orderTotal * 0.04;
                        $orderShipping = $orderTotal * 0.00;
                        $orderPartnerCommission = $orderTotal * 0.00;
                        $orderSFPFee = $orderTotal * 0.03;
                        $orderVat = $orderTotal * 0.07;

                        $dayNetProfit += $orderTotal - (($orderCost + $orderBoxCost + $orderAdsCost) + ($orderFee + $orderCommission + $orderShipping + $orderPartnerCommission + $orderSFPFee + $orderVat));
                    }
                } else {
                    $dayNetProfit = '-';
                }

                if (is_numeric($dayNetProfit)) {
                    $netProfitTotal += $dayNetProfit;
                }
            @endphp
            <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ is_numeric($dayNetProfit) ? number_format($dayNetProfit, 2) : $dayNetProfit }}</td>
        @endfor
        <td style="border: 2px solid black; text-align: right; background-color: #e69138;">{{ number_format($netProfitTotal, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc; color: red;">{{ number_format($netProfitTotal, 2) }}</td>
    </tr>

    {{-- -------------------------------------------------------------------------------------------------------------------------- --}}

    <tr></tr>
    <tr>
        <td></td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;">จำนวนออเดอร์</td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;">ยอดขาย</td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;">ต้นทุน</td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;">ค่ากล่อง</td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;">ค่า Ads</td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #38761d;">กำไร สุทธิ</td>
        @foreach ($item as $index => $product)
            <td rowspan="3"style="border: 2px solid black; text-align: center; background-color: #5b9bd5;">{{$product['name']}}</td>
        @endforeach
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าธรรมเนียมคำสั่งซื้อ ผ่านภารกิจไม่เสีย 3%</b></td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าคอมมิชชั่น TikTok Shop</b></td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ยอดรวมค่าจัดส่งที่ร้านค้าจ่ายจริง</b></td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าคอมมิชชั่นพันธมิตร</b></td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ค่าธรรมเนียมการบริการ SFP</b></td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffe599;"><b>ภาษี Vat 7%</b></td>
        <td rowspan="3" style="border: 2px solid black; text-align: center; background-color: #ffff00;"><b>กำไรสุทธิ หลังหักต้นทุนสินค้า+ภาษี</b></td>
    </tr>
    <tr></tr>
    <tr></tr>

    @php
        $totalOrders = 0;
        $totalSales = 0;
        $totalCost = 0;
        $totalBoxCost = 0;
        $totalAds = 0;
        $totalNetProfit = 0;
        $totalFee = 0;
        $totalCommission = 0;
        $totalShipping = 0;
        $totalPartnerCommission = 0;
        $totalSFPFee = 0;
        $totalVat = 0;
        $productTotals = [];
        // Calculate the totals for each day
        for ($i = 1; $i <= 31; $i++) {
            $day = str_pad($i, 2, '0', STR_PAD_LEFT); //การจับกับ array ที่ group ตามวัน

            if (isset($data[$day])) {
                foreach ($data[$day] as $order) {
                    $totalOrders++;
                    $totalSales += $order['total'];
                    foreach ($order['sale_order_lines'] as $line) {
                        foreach ($line['item']['main_item_line'] as $mainItem) {
                            $totalCost += $mainItem['item']['unit_cost'] * $mainItem['qty'];
                        }
                    }
                    $totalBoxCost += count($order['sale_order_lines']) * 5;
                    $totalAds += 0; // รอปรับ

                    $orderTotal = $order['total'];
                    $orderCost = 0;
                    foreach ($order['sale_order_lines'] as $line) {
                        foreach ($line['item']['main_item_line'] as $mainItem) {
                            $orderCost += $mainItem['item']['unit_cost'] * $mainItem['qty'];
                        }
                    }
                    $orderBoxCost = count($order['sale_order_lines']) * 5;
                    $orderAdsCost = 0; // รอปรับ

                    $orderFee = $orderTotal * 0.03;
                    $orderCommission = $orderTotal * 0.04;
                    $orderShipping = $orderTotal * 0.00; // รอปรับ
                    $orderPartnerCommission = $orderTotal * 0.00; // รอปรับ
                    $orderSFPFee = $orderTotal * 0.03;
                    $orderVat = $orderTotal * 0.07;

                    $totalFee += $orderFee;
                    $totalCommission += $orderCommission;
                    $totalShipping += $orderShipping;
                    $totalPartnerCommission += $orderPartnerCommission;
                    $totalSFPFee += $orderSFPFee;
                    $totalVat += $orderVat;

                    $dayNetProfit = $orderTotal - ($orderCost + $orderBoxCost + $orderAdsCost);
                    $totalNetProfit += $dayNetProfit;


                    foreach ($order['sale_order_lines'] as $line) {
                        foreach ($line['item']['main_item_line'] as $mainItem) {
                            $itemName = $mainItem['item']['name'];
                            if (!isset($productTotals[$itemName])) {
                                $productTotals[$itemName] = 0;
                            }
                            $productTotals[$itemName] += $mainItem['qty'];
                        }
                    }
                }
            }
        }
    @endphp

    <tr>
        <td></td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalOrders, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalSales, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalCost, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalBoxCost, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalAds, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalNetProfit, 2) }}</td>
        @foreach ($item as $index => $product)
            <td style="border: 2px solid black; text-align: right; background-color: #5b9bd5;">{{ number_format($productTotals[$product['name']] ?? 0, 2) }}</td>
        @endforeach
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalFee, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalCommission, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalShipping, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalPartnerCommission, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalSFPFee, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalVat, 2) }}</td>
        <td style="border: 2px solid black; text-align: right; background-color: #fff2cc;">{{ number_format($totalNetProfit - $totalFee - $totalCommission - $totalShipping - $totalPartnerCommission - $totalSFPFee - $totalVat, 2) }}</td>
    </tr>

</table>
