<?php

return [
    /**
     * Messaging / Bot.
     */
    'bot' => [
        'channel_token' => env('LINE_BOT_CHANNEL_TOKEN'),
        'channel_secret' => env('LINE_BOT_CHANNEL_SECRET'),
        'path' => env('LINE_BOT_WEBHOOK_PATH', 'line/webhook'),
        'route' => env('LINE_BOT_WEBHOOK_ROUTE', 'line.webhook'),
        'domain' => env('LINE_BOT_WEBHOOK_DOMAIN'),
        'middleware' => env('LINE_BOT_WEBHOOK_MIDDLEWARE', 'throttle'),
    ],

    /**
     * LINE Login.
     */
    'login' => [
        'client_id' => env('LINE_LOGIN_CLIENT_ID'),
        'client_secret' => env('LINE_LOGIN_CLIENT_SECRET'),
        'redirect' => env('LINE_LOGIN_REDIRECT'),
    ],

    /**
     * LINE Notify.
     */
    'notify' => [
        'client_id' => env('LINE_NOTIFY_CLIENT_ID'),
        'client_secret' => env('LINE_NOTIFY_CLIENT_SECRET'),
        'redirect' => env('LINE_NOTIFY_REDIRECT'),
        'personal_access_token' => env('LINE_NOTIFY_PERSONAL_ACCESS_TOKEN'),
    ],

    /**
     * LINE Liff.
     */
    'liff' => [
        'line_liff_warning_view'   => env('LINE_LIFF_WARNING_VIEW'),
        'line_liff_warning_list'   => env('LINE_LIFF_WARNING_LIST'),
        'line_liff_warning_approve' => env('LINE_LIFF_WARNING_APPROVE'),
        'line_liff_check_in'       => env('LINE_LIFF_CHECK_IN'),
        'line_liff_get_leave'      => env('LINE_LIFF_GET_LEAVE'),
        'line_liff_get_ot'         => env('LINE_LIFF_GET_OT'),
        'line_liff_level_list'     => env('LINE_LIFF_LEVEL_LIST'),
        'line_liff_ot_list'        => env('LINE_LIFF_OT_LIST'),
        'line_liff_leave_approve'  => env('LINE_LIFF_LEAVE_APPROVE'),
        'line_liff_ot_approve'     => env('LINE_LIFF_OT_APPROVE'),
        'line_liff_user_profile'   => env('LINE_LIFF_USER_PROFILE'),
    ],

];
