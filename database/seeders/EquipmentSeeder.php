<?php

namespace Database\Seeders;

use App\Models\EquipmentCategory;
use App\Models\Equipment;
use Illuminate\Database\Seeder;

class EquipmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create Equipment Categories
        $categories = [
            [
                'name' => 'คอมพิวเตอร์และอุปกรณ์ IT',
                'description' => 'อุปกรณ์คอมพิวเตอร์ แล็ปท็อป และอุปกรณ์เทคโนโลยีสารสนเทศ',
                'status' => true,
                'create_by' => 1
            ],
            [
                'name' => 'อุปกรณ์สำนักงาน',
                'description' => 'อุปกรณ์สำนักงานทั่วไป เช่น เครื่องถ่ายเอกสาร เครื่องพิมพ์',
                'status' => true,
                'create_by' => 1
            ],
            [
                'name' => 'อุปกรณ์การประชุม',
                'description' => 'อุปกรณ์สำหรับการประชุม เช่น โปรเจคเตอร์ ไมโครโฟน',
                'status' => true,
                'create_by' => 1
            ],
            [
                'name' => 'เครื่องมือและอุปกรณ์ช่าง',
                'description' => 'เครื่องมือช่างและอุปกรณ์ซ่อมบำรุง',
                'status' => true,
                'create_by' => 1
            ]
        ];

        foreach ($categories as $category) {
            EquipmentCategory::create($category);
        }

        // Get created categories
        $itCategory = EquipmentCategory::where('name', 'คอมพิวเตอร์และอุปกรณ์ IT')->first();
        $officeCategory = EquipmentCategory::where('name', 'อุปกรณ์สำนักงาน')->first();
        $meetingCategory = EquipmentCategory::where('name', 'อุปกรณ์การประชุม')->first();
        $toolsCategory = EquipmentCategory::where('name', 'เครื่องมือและอุปกรณ์ช่าง')->first();

        // Create Equipment Items
        $equipments = [
            // IT Equipment
            [
                'code' => 'IT001',
                'name' => 'Laptop Dell Inspiron 15',
                'description' => 'แล็ปท็อป Dell Inspiron 15 3000 Series',
                'brand' => 'Dell',
                'model' => 'Inspiron 15 3000',
                'serial_number' => '*********',
                'purchase_price' => 25000.00,
                'purchase_date' => '2023-01-15',
                'condition' => 'good',
                'status' => 'available',
                'location' => 'IT Storage Room',
                'category_id' => $itCategory->id,
                'branch_id' => 1,
                'is_active' => true,
                'create_by' => 1
            ],
            [
                'code' => 'IT002',
                'name' => 'iPad Pro 11 inch',
                'description' => 'iPad Pro 11 นิ้ว สำหรับงานนำเสนอ',
                'brand' => 'Apple',
                'model' => 'iPad Pro 11',
                'serial_number' => '*********',
                'purchase_price' => 35000.00,
                'purchase_date' => '2023-02-20',
                'condition' => 'good',
                'status' => 'available',
                'location' => 'IT Storage Room',
                'category_id' => $itCategory->id,
                'branch_id' => 1,
                'is_active' => true,
                'create_by' => 1
            ],
            // Office Equipment
            [
                'code' => 'OF001',
                'name' => 'เครื่องพิมพ์ Canon PIXMA',
                'description' => 'เครื่องพิมพ์อิงค์เจ็ท Canon PIXMA G3010',
                'brand' => 'Canon',
                'model' => 'PIXMA G3010',
                'serial_number' => 'CN2023001',
                'purchase_price' => 8500.00,
                'purchase_date' => '2023-03-10',
                'condition' => 'good',
                'status' => 'available',
                'location' => 'Office Floor 2',
                'category_id' => $officeCategory->id,
                'branch_id' => 1,
                'is_active' => true,
                'create_by' => 1
            ],
            [
                'code' => 'OF002',
                'name' => 'เครื่องแสกนเอกสาร',
                'description' => 'เครื่องสแกนเอกสาร Epson WorkForce ES-50',
                'brand' => 'Epson',
                'model' => 'WorkForce ES-50',
                'serial_number' => 'EP2023001',
                'purchase_price' => 4500.00,
                'purchase_date' => '2023-04-05',
                'condition' => 'good',
                'status' => 'available',
                'location' => 'Office Floor 2',
                'category_id' => $officeCategory->id,
                'branch_id' => 1,
                'is_active' => true,
                'create_by' => 1
            ],
            // Meeting Equipment
            [
                'code' => 'MT001',
                'name' => 'โปรเจคเตอร์ Epson EB-X41',
                'description' => 'โปรเจคเตอร์ Epson EB-X41 3600 Lumens',
                'brand' => 'Epson',
                'model' => 'EB-X41',
                'serial_number' => 'EP2023002',
                'purchase_price' => 18000.00,
                'purchase_date' => '2023-05-15',
                'condition' => 'good',
                'status' => 'available',
                'location' => 'Meeting Room A',
                'category_id' => $meetingCategory->id,
                'branch_id' => 1,
                'is_active' => true,
                'create_by' => 1
            ],
            [
                'code' => 'MT002',
                'name' => 'ไมโครโฟนไร้สาย',
                'description' => 'ไมโครโฟนไร้สาย Shure SM58',
                'brand' => 'Shure',
                'model' => 'SM58',
                'serial_number' => 'SH2023001',
                'purchase_price' => 12000.00,
                'purchase_date' => '2023-06-20',
                'condition' => 'good',
                'status' => 'available',
                'location' => 'Meeting Room B',
                'category_id' => $meetingCategory->id,
                'branch_id' => 1,
                'is_active' => true,
                'create_by' => 1
            ],
            // Tools
            [
                'code' => 'TL001',
                'name' => 'สว่านไฟฟ้า Makita',
                'description' => 'สว่านไฟฟ้า Makita HP2050H 20mm',
                'brand' => 'Makita',
                'model' => 'HP2050H',
                'serial_number' => 'MK2023001',
                'purchase_price' => 3500.00,
                'purchase_date' => '2023-07-10',
                'condition' => 'good',
                'status' => 'available',
                'location' => 'Maintenance Room',
                'category_id' => $toolsCategory->id,
                'branch_id' => 1,
                'is_active' => true,
                'create_by' => 1
            ]
        ];

        foreach ($equipments as $equipment) {
            Equipment::create($equipment);
        }
    }
}
