<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEquipmentBorrowingItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('equipment_borrowing_items', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('borrowing_id');
            $table->foreign('borrowing_id')->references('id')->on('equipment_borrowings')->onDelete('cascade');

            $table->unsignedBigInteger('equipment_id');
            $table->foreign('equipment_id')->references('id')->on('equipments')->onDelete('cascade');

            $table->integer('quantity')->default(1); // จำนวนที่ยืม
            $table->string('condition_before')->nullable(); // สภาพก่อนยืม
            $table->string('condition_after')->nullable(); // สภาพหลังคืน
            $table->text('notes')->nullable(); // หมายเหตุเฉพาะรายการ
            $table->boolean('is_returned')->default(false); // สถานะการคืน
            $table->datetime('returned_at')->nullable(); // วันเวลาที่คืน

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('equipment_borrowing_items');
    }
}
