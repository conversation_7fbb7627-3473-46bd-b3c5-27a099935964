<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEquipmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('equipments', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique(); // รหัสอุปกรณ์
            $table->string('name'); // ชื่ออุปกรณ์
            $table->text('description')->nullable(); // คำอธิบาย
            $table->string('brand')->nullable(); // ยี่ห้อ
            $table->string('model')->nullable(); // รุ่น
            $table->string('serial_number')->nullable(); // หมายเลขเครื่อง
            $table->decimal('purchase_price', 12, 2)->nullable(); // ราคาซื้อ
            $table->date('purchase_date')->nullable(); // วันที่ซื้อ
            $table->string('condition')->default('good'); // สภาพ: good, fair, poor
            $table->enum('status', ['available', 'borrowed', 'maintenance', 'damaged', 'retired'])->default('available'); // สถานะ
            $table->string('location')->nullable(); // ตำแหน่งที่เก็บ
            $table->string('image')->nullable(); // รูปภาพ

            $table->unsignedBigInteger('category_id')->nullable();
            $table->foreign('category_id')->references('id')->on('equipment_categories')->onDelete('set null');

            $table->integer('branch_id')->nullable()->unsigned()->index();
            $table->foreign('branch_id')->references('id')->on('branch')->onDelete('cascade');

            $table->boolean('is_active')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('equipments');
    }
}
