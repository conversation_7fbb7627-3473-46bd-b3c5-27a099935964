<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateLoanPoliciesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_policies', function (Blueprint $table) {
            $table->id();

            $table->string('name')->nullable();
            $table->double('interest_rate_pa', 5, 2)->default(12.00);
            $table->double('dti_default_pct', 5, 2)->default(20.00);
            $table->double('net_min_amount', 14, 2)->default(7000.00);
            $table->double('open_new_after_paid_pct', 5, 2)->default(50.00);
            $table->double('max_total_amount', 14, 2)->default(10000000.00);
            $table->boolean('allow_multi_active')->default(false);
            $table->enum('interest_mode', ['flat_amortized', 'flat_balloon'])->default('flat_amortized');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);

            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        DB::table('loan_policies')->insert([
            'name' => 'Default Policy',
            'interest_rate_pa' => 12.00,
            'dti_default_pct' => 20.00,
            'net_min_amount' => 7000.00,
            'open_new_after_paid_pct' => 50.00,
            'allow_multi_active' => false,
            'interest_mode' => 'flat_amortized',
            'is_active' => true,
            'is_default' => true,
            'max_total_amount' => 10000000.00,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_policies');
    }
}
