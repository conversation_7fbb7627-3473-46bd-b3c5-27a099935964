<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loans', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->bigInteger('loan_application_id')->nullable()->unsigned()->index();
            $table->foreign('loan_application_id')->references('id')->on('loan_applications')->onDelete('cascade');

            $table->double('approved_amount', 14, 2)->default(0.00);
            $table->integer('term_months')->default(0);
            $table->double('interest_rate_pa', 5, 2)->default(0.00);
            $table->date('start_cycle')->nullable();
            $table->enum('interest_mode', ['flat_amortized', 'flat_balloon'])->default('flat_amortized');
            $table->enum('status', ['active', 'overdue', 'closed'])->default('active');
            $table->double('outstanding_principal', 14, 2)->default(0.00);

            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loans');
    }
}
