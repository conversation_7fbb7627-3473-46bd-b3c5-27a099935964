<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_schedules', function (Blueprint $table) {
            $table->id();

            $table->bigInteger('loan_id')->nullable()->unsigned()->index();
            $table->foreign('loan_id')->references('id')->on('loans')->onDelete('cascade');

            $table->integer('installment_no')->default(0);
            $table->date('due_date')->nullable();
            $table->double('principal_due', 14, 2)->default(0.00);
            $table->double('interest_due', 14, 2)->default(0.00);
            $table->double('total_due', 14, 2)->default(0.00);
            $table->enum('status', ['due', 'overdue', 'partial', 'paid', 'moratorium'])->default('due');

            // - due = ถึงกำหนดชำระ
            // - paid = จ่ายแล้วครบ
            // - partial = จ่ายบางส่วน
            // - overdue = ค้างชำระ
            // - moratorium = พักการจ่าย

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_schedules');
    }
}
