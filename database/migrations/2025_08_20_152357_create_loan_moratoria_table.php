<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanMoratoriaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_moratoria', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->bigInteger('loan_id')->nullable()->unsigned()->index();
            $table->foreign('loan_id')->references('id')->on('loans')->onDelete('cascade');

            $table->integer('start_installment')->default(0);
            $table->integer('months')->default(0);
            $table->enum('interest_accrual_mode', ['none', 'accrue'])->default('none'); //none = ไม่คิดดอก / accrue = คิดดอกเพิ่ม
            $table->double('calculated_extra_interest', 14, 2)->default(0.00);

            $table->integer('approved_by')->nullable()->unsigned()->index();
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('cascade');

            $table->timestamp('approved_at')->nullable();
            $table->text('reason')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_moratoria');
    }
}
