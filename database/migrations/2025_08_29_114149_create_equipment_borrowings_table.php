<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEquipmentBorrowingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('equipment_borrowings', function (Blueprint $table) {
            $table->id();
            $table->string('borrowing_code')->unique(); // รหัสการยืม

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->integer('branch_id')->nullable()->unsigned()->index();
            $table->foreign('branch_id')->references('id')->on('branch')->onDelete('cascade');

            $table->date('borrow_date'); // วันที่ยืม
            $table->date('expected_return_date'); // วันที่คาดว่าจะคืน
            $table->date('actual_return_date')->nullable(); // วันที่คืนจริง
            $table->enum('status', ['pending', 'approved', 'borrowed', 'returned', 'overdue', 'cancelled'])->default('pending');
            $table->text('purpose')->nullable(); // วัตถุประสงค์การยืม
            $table->text('notes')->nullable(); // หมายเหตุ
            $table->text('return_notes')->nullable(); // หมายเหตุการคืน

            $table->integer('approved_by')->nullable()->unsigned()->index();
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            $table->datetime('approved_at')->nullable();

            $table->integer('returned_by')->nullable()->unsigned()->index();
            $table->foreign('returned_by')->references('id')->on('users')->onDelete('set null');
            $table->datetime('returned_at')->nullable();

            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('equipment_borrowings');
    }
}
