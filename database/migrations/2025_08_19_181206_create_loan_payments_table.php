<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_payments', function (Blueprint $table) {
            $table->id();

            $table->bigInteger('loan_id')->nullable()->unsigned()->index();
            $table->foreign('loan_id')->references('id')->on('loans')->onDelete('cascade');

            $table->integer('installment_no')->default(0);
            $table->date('paid_date')->nullable();
            $table->double('principal_paid', 14, 2)->default(0.00);
            $table->double('interest_paid', 14, 2)->default(0.00);
            $table->double('total_paid', 14, 2)->default(0.00);
            $table->string('payroll_ref')->nullable();
            $table->enum('method', ['payroll', 'manual'])->default('payroll');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_payments');
    }
}
