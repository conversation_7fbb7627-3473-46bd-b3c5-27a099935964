<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanApplicationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_applications', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->integer('approver_id')->nullable()->unsigned()->index();
            $table->foreign('approver_id')->references('id')->on('users')->onDelete('cascade');

            $table->double('request_amount', 14, 2)->default(0.00);
            $table->integer('term_months')->default(0);
            $table->integer('payments_per_month')->default(1);
            $table->enum('interest_mode', ['flat_amortized', 'flat_balloon'])->default('flat_amortized');
            $table->text('purpose')->nullable();
            $table->enum('status', ['draft', 'pending', 'approved', 'rejected', 'converted'])->default('draft');
            $table->text('reason')->nullable();

            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_applications');
    }
}
