<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalePageContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_page_content', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('sale_page_id')->nullable()->unsigned()->index();
            $table->foreign('sale_page_id')->references('id')->on('sale_page')->onDelete('cascade');

            $table->enum('type', [ 'text', 'image', 'video', 'countdown', 'social', 'marketplace', 'progress', 'button'])->charset('utf8')->default('image');


            $table->string('content')->nullable();
            $table->string('url')->nullable();

            //progress
            $table->string('progress_percent')->nullable();

            //button
            $table->enum('button_type', ['shop', 'link'])->charset('utf8')->nullable();
            $table->string('button_color')->nullable();

            $table->boolean('status')->default(1);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_page_content');
    }
}
