<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeaveTableDatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('leave_table_date', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('leave_table_id')->nullable()->unsigned()->index();
            $table->foreign('leave_table_id')->references('id')->on('leave_tables')->onDelete('cascade');

            $table->date('date')->nullable();
            $table->string('time_start')->nullable();
            $table->string('time_end')->nullable();

            $table->boolean('status')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('leave_table_date');
    }
}
