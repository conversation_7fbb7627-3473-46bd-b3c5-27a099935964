<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWorkShiftTimesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('work_shift_time', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('work_shift_id')->nullable()->unsigned()->index();
            $table->foreign('work_shift_id')->references('id')->on('work_shift')->onDelete('cascade');

            $table->string('day', 255)->charset('utf8');
            $table->string('time_in', 255)->charset('utf8');
            $table->string('time_out', 255)->charset('utf8');

            $table->boolean('status')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('work_shift_time');
    }
}
