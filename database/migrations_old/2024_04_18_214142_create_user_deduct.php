<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserDeduct extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_deduct', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->integer('deduct_type_id')->nullable()->unsigned()->index();
            $table->foreign('deduct_type_id')->references('id')->on('deduct_types')->onDelete('cascade');

            $table->integer('deduct_paid_id')->nullable()->unsigned()->index();
            $table->foreign('deduct_paid_id')->references('id')->on('deduct_paids')->onDelete('cascade');

            $table->date('date')->nullable();

            $table->double('price', 10, 2)->default(0.00);

            $table->boolean('status')->default(1);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_deduct');
    }
}
