<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserAttandancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_attendance', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->integer('zkt_time_id')->nullable();
            $table->date('date')->nullable();
            $table->enum('type', ['normal', 'late', 'miss', 'leave', 'off'])->charset('utf8')->default('normal');

            $table->integer('leave_table_id')->nullable()->unsigned()->index();
            $table->foreign('leave_table_id')->references('id')->on('leave_tables')->onDelete('cascade');

            $table->boolean('status')->default(1);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_attendance');
    }
}
