<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserIncome extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_income', function (Blueprint $table) {
            $table->id();


            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->integer('income_types_id')->nullable()->unsigned()->index();
            $table->foreign('income_types_id')->references('id')->on('income_types')->onDelete('cascade');

            $table->integer('income_paids_id')->nullable()->unsigned()->index();
            $table->foreign('income_paids_id')->references('id')->on('income_paids')->onDelete('cascade');

            $table->date('date')->nullable();

            $table->double('price', 10, 2)->default(0.00);

            $table->boolean('status')->default(1);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_income');
    }
}
