<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePayrollsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payroll', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('round')->default(0);
            $table->string('year', 255)->charset('utf8');
            $table->string('month', 255)->charset('utf8');

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->double('salary', 10, 2)->default(0.00);
            $table->double('ot', 10, 2)->default(0.00);
            $table->double('salary_withdraw', 10, 2)->default(0.00);
            $table->double('total_income', 10, 2)->default(0.00);
            $table->double('total_deduct', 10, 2)->default(0.00);
            $table->double('total', 10, 2)->default(0.00);

            $table->string('slip', 255)->charset('utf8');
            $table->boolean('pay_status')->default(0);

            $table->boolean('status')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payroll');
    }
}
