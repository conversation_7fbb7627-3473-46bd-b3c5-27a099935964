<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBondLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bond_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->year('year');
            $table->tinyInteger('month'); 
            $table->integer('total_amount'); // เงินประกันที่ต้องจ่ายทั้งหมด 
            $table->integer('deduct_amount'); // จำนวนที่หักในเดือนนี้
            $table->integer('balance'); // เหลือเท่าไหร่ หลังหักแล้ว
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bond_logs');
    }
}
