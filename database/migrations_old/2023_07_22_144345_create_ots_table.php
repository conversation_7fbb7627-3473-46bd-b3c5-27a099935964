<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOtsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ot', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('user_id')->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->integer('ot_type_id')->unsigned()->index();
            $table->foreign('ot_type_id')->references('id')->on('ot_type')->onDelete('cascade');

            $table->date('date')->nullable();
            $table->string('time_start')->nullable();
            $table->string('time_end')->nullable();
            $table->double('qty', 10, 2)->default(0.00);
            $table->integer('qty_hour')->default(0);

            $table->enum('status', ['open', 'process', 'approved', 'cancel'])->charset('utf8')->default('open');
            $table->string('status_by', 100)->charset('utf8')->nullable();
            $table->timestamp('status_at')->nullable();
            $table->text('remark')->charset('utf8')->nullable();

            $table->integer('hr_id')->unsigned()->index();
            $table->foreign('hr_id')->references('id')->on('users')->onDelete('cascade');

            $table->integer('head_id')->unsigned()->index();
            $table->foreign('head_id')->references('id')->on('users')->onDelete('cascade');

            $table->string('head_by', 100)->charset('utf8')->nullable();
            $table->timestamp('head_at')->nullable();
            $table->text('head_remark')->charset('utf8')->nullable();


            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ot');
    }
}
