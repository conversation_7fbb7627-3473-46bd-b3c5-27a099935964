<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalePagePomotionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_page_promotion', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('sale_page_id')->nullable()->unsigned()->index();
            $table->foreign('sale_page_id')->references('id')->on('sale_page')->onDelete('cascade');

            $table->integer('item_id')->nullable()->unsigned()->index();
            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');

            $table->string('name')->nullable();
            $table->integer('qty')->default(0);
            $table->double('price', 10, 2)->default(0.00);

            $table->boolean('status')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_page_promotion');
    }
}
