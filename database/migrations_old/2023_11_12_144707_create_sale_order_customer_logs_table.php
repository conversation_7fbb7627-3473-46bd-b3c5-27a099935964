<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderCustomerLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_customer_log', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('sale_order_id')->nullable()->unsigned()->index();
            $table->foreign('sale_order_id')->references('id')->on('sale_order')->onDelete('cascade');

            $table->string('old_name')->charset('utf8')->nullable();
            $table->string('old_telephone')->charset('utf8')->nullable();
            $table->string('old_email')->charset('utf8')->nullable();
            $table->string('old_address')->charset('utf8')->nullable();

            $table->string('name')->charset('utf8')->nullable();
            $table->string('telephone')->charset('utf8')->nullable();
            $table->string('email')->charset('utf8')->nullable();
            $table->string('address')->charset('utf8')->nullable();

            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_customer_log');
    }
}
