<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserYellowCardLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_yellow_card', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // $table->date('date')->nullable();
            $table->double('qty', 10, 2)->default(0.00);

            $table->integer('leave_table_id')->nullable()->unsigned()->index();
            $table->foreign('leave_table_id')->references('id')->on('leave_tables')->onDelete('cascade');

            $table->integer('user_attendance_id')->nullable()->unsigned()->index();
            $table->foreign('user_attendance_id')->references('id')->on('user_attendance')->onDelete('cascade');

            $table->boolean('status')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_yellow_card');
    }
}
