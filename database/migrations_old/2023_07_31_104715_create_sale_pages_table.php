<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalePagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_page', function (Blueprint $table) {
            $table->increments('id');

            $table->string('name')->nullable();
            $table->string('url')->nullable();
            $table->string('thankyou_url')->nullable();

            $table->integer('item_id')->nullable()->unsigned()->index();
            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');


            $table->boolean('transfer')->default(0);
            $table->boolean('cod')->default(0);

            $table->integer('delivery_by_id')->nullable()->unsigned()->index();
            $table->foreign('delivery_by_id')->references('id')->on('delivered_by')->onDelete('cascade');

            $table->double('shipping_price', 10, 2)->default(0.00);
            $table->double('cod_price_surcharge', 10, 2)->default(0.00);

            $table->integer('bank_id')->unsigned()->index()->nullable();
            $table->foreign('bank_id')->references('id')->on('banks')->onDelete('cascade');


            //oto
            $table->boolean('oto')->default(0);
            $table->enum('oto_type', ['free', 'bump'])->charset('utf8')->nullable();
            $table->integer('oto_item_id')->nullable()->unsigned()->index();
            $table->foreign('oto_item_id')->references('id')->on('item')->onDelete('cascade');
            $table->double('oto_price', 10, 2)->default(0.00);
            $table->string('oto_title')->nullable();
            $table->text('oto_detail')->nullable();
            //

            $table->integer('count_order')->default(0);

            $table->boolean('status')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_page');
    }
}
