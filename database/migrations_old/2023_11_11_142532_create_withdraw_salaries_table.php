<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWithdrawSalariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('withdraw_salary', function (Blueprint $table) {
            $table->increments('id');

            $table->date('date')->nullable();

            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->string('month_withdraw')->nullable();
            $table->text('objective')->charset('utf8')->nullable();
            $table->double('qty', 10, 2)->default(0.00);


            $table->enum('status', ['open', 'approved', 'cancel'])->charset('utf8')->default('open');
            $table->string('status_by', 100)->charset('utf8')->nullable();
            $table->timestamp('status_at')->nullable();
            $table->text('remark')->charset('utf8')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('withdraw_salary');
    }
}
