<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGradeYellowCardsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('grade_yellow_card', function (Blueprint $table) {
            $table->increments('id');

            $table->string('name')->nullable();
            $table->integer('qty_start')->default(0);
            $table->integer('qty_end')->default(0);
            $table->double('deduct', 10, 2)->default(0.00);
            $table->double('rate_up_salary', 10, 2)->default(0.00);

            $table->boolean('status')->default(1);
            $table->string('create_by', 100)->charset('utf8')->nullable();
            $table->string('update_by', 100)->charset('utf8')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('grade_yellow_card');
    }
}
