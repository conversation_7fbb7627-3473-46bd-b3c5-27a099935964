<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWarningsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('warnings', function (Blueprint $table) {
            $table->id();
            $table->integer('branch_id')->nullable()->unsigned()->index();
            $table->integer('user_id')->nullable()->unsigned()->index();

            $table->date('date');
            $table->string('title'); // หัวข้อการตักเตือน
            $table->text('description')->nullable();
            $table->string('punishment')->nullable();
            $table->enum('status', ['open', 'approved', 'finish'])->default('open');
            $table->integer('approved_by')->nullable()->unsigned()->index(); // หัวหน้าที่อนุมัติ
            $table->date('approved_at')->nullable(); // วันที่อนุมัติ
            $table->integer('acknowledged_by')->nullable()->unsigned()->index(); // คนที่รับทราบ
            $table->date('acknowledged_at')->nullable(); // วันที่รับทราบ
            $table->timestamps();

            $table->foreign('branch_id')->references('id')->on('branch')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('acknowledged_by')->references('id')->on('users')->onDelete('set null');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('warnings');
    }
}
