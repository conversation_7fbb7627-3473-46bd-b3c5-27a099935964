<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmployeeDepositsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_deposits', function (Blueprint $table) {
            $table->id();
            $table->integer('branch_id')->nullable()->unsigned()->index();
            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->date('date'); 
            $table->decimal('amount', 12, 2);
            $table->decimal('total_amount', 10, 2)->default(0);
            $table->text('description')->nullable(); // หมายเหตุ
            $table->enum('type', ['deposit', 'withdraw'])->default('deposit');
            $table->timestamps();

            $table->foreign('branch_id')->references('id')->on('branch')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_deposits');
    }
}
