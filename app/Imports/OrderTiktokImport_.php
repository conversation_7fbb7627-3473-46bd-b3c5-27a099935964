<?php

namespace App\Imports;

use App\Models\Bank;
use App\Models\Customer;
use App\Models\CustomerLine;
use App\Models\Delivered_by;
use App\Models\Item;
use App\Models\Item_trans;
use App\Models\Sale_order;
use App\Models\Sale_order_line;
use App\Models\WorkTelesale;
use DateTime;
use Exception;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithStartRow;

class OrderTiktokImport_ implements ToModel, WithHeadingRow, WithStartRow
{
    protected $loginBy;
    protected $page_id;

    public function __construct($loginBy,$page_id)
    {
        $this->loginBy = $loginBy;
        $this->page_id = $page_id;
    }

    public function model(array $row)
    {
        $userId = $this->loginBy->id;
        $page_id = $this->page_id;
        $order_id = $row['order_id'];
        $order_status = $row['order_status'];
        $order_substatus = $row['order_substatus'];
        $cancellation_return_type = $row['cancelationreturn_type'];
        $normal_or_preorder = $row['normal_or_pre_order'];
        $sku_id = $row['sku_id'];
        $seller_sku = $row['seller_sku'];
        $product_name = $row['product_name'];
        $variation = $row['variation'];
        $quantity = $row['quantity'];
        $sku_quantity_of_return = $row['sku_quantity_of_return'];
        $sku_unit_original_price = $row['sku_unit_original_price'];
        $sku_subtotal_before_discount = $row['sku_subtotal_before_discount'];
        $sku_platform_discount = $row['sku_platform_discount'];
        $sku_seller_discount = $row['sku_seller_discount'];
        $sku_subtotal_after_discount = $row['sku_subtotal_after_discount'];
        $shipping_fee_after_discount = $row['shipping_fee_after_discount'];
        $original_shipping_fee = $row['original_shipping_fee'];
        $shipping_fee_seller_discount = $row['shipping_fee_seller_discount'];
        $shipping_fee_platform_discount = $row['shipping_fee_platform_discount'];
        $payment_platform_discount = $row['payment_platform_discount'];
        $taxes = $row['taxes'];
        $small_order_fee = $row['small_order_fee'];
        $order_amount = $row['order_amount'];
        $order_refund_amount = $row['order_refund_amount'];
        $created_time = $row['created_time'];
        $paid_time = $row['paid_time'];
        $rts_time = $row['rts_time'];
        $shipped_time = $row['shipped_time'];
        $delivered_time = $row['delivered_time'];
        $cancelled_time = $row['cancelled_time'];
        $cancel_by = $row['cancel_by'];
        $cancel_reason = $row['cancel_reason'];
        $fulfillment_type = $row['fulfillment_type'];
        $warehouse_name = $row['warehouse_name'];
        $tracking_id = $row['tracking_id'];
        $delivery_option = $row['delivery_option'];
        $shipping_provider_name = $row['shipping_provider_name'];
        $buyer_message = $row['buyer_message'];
        $buyer_username = $row['buyer_username'];
        $recipient = $row['recipient'];
        $phone = $row['phone'];
        $zipcode = $row['zipcode'];
        $country = $row['country'];
        $province = $row['province'];
        $district = $row['district'];
        $detail_address = $row['detail_address'];
        $additional_address_information = $row['additional_address_information'];
        $payment_method = $row['payment_method'];
        $weight = $row['weightkg'];
        $product_category = $row['product_category'];
        $package_id = $row['package_id'];
        $seller_note = $row['seller_note'];
        $checked_status = $row['checked_status'];
        $checked_marked_by = $row['checked_marked_by'];


        $shipping_price = (double) str_replace(['THB ', ','], '', $sku_subtotal_before_discount);
        $main_discount = (double) str_replace(['THB ', ','], '', $sku_seller_discount);
        $cod_price_surcharge = (double) str_replace(['THB ', ','], '', $shipping_fee_after_discount);
        $vat = (double) str_replace(['THB ', ','], '', $taxes);
        $total = (double) str_replace(['THB ', ','], '', $order_amount);

        $payment_qty = 0;
        if ($order_status === 'Canceled') {
            $status = 'failed';
        }elseif($payment_method === 'เก็บเงินปลายทาง'){
            $status = 'order';
        }else{
            $status = 'paid';
        }

        if( $payment_method === 'เก็บเงินปลายทาง'){
            $payment_method_type = 'COD';

        }else{
            $payment_method_type = 'transfer';
            $payment_qty = $total;
            // $bank = Bank::where('name', $payment_method)->first();
            // $bank_id = $bank;
        }

        $delivery = null;
        if($shipping_provider_name === 'Flash Express Thailand'){
            $delivery = 1;
        }
        // elseif(){
        //     $delivery = 2;
        // }

        $customer = Customer::where('phone', $phone)
                            ->where('name', $buyer_username)
                            ->first();
        if (!$customer) {
            $customer = Customer::create([
                'phone' => $phone,
                'name' => $buyer_username
            ]);
            CustomerLine::create([
                'customer_id' => $customer->id,
                'address' => $country.' '.$province.' '.$district.' '.$detail_address,
            ]);
        }

        $dateString = $row['created_time'];
        $dateObject = DateTime::createFromFormat('d/m/Y H:i:s', $dateString);
        $formattedDate = $dateObject ? $dateObject->format('Y-m-d') : null;

        $item = Item::where('sku',$seller_sku)->first();
        if (!$item) {
            throw new Exception('ไม่พบข้อมูลสินค้ารหัสนี้ : ' . $seller_sku);
        }

        //stock Count
        if($item){
            $stockCount = $this->getStockCount($item->id);
            if (abs($quantity) > $stockCount) {
                throw new Exception('สินค้ารหัสนี้ : ' . $seller_sku . ' มีจำนวนไม่เพียงพอ กรุณาดำเนินการรับสินค้าเข้าคลังก่อนดำเนินการ');
            }
        }

        DB::beginTransaction();

        try {

            $Sale_order = Sale_order::Create(
                [
                    'customer_id' => $customer->id,
                    'delivery_by_id' => $delivery,
                    // 'sale_id' => $sale_id,
                    'page_id' => $page_id,
                    'date_time' => $formattedDate,
                    'order_id' => $order_id,
                    'description' => null,
                    'name' => $customer->name,
                    'telephone' => $phone,
                    'email' => null,
                    'address' => $detail_address,
                    'shipping_price' => $shipping_price,
                    'cod_price_surcharge' => $cod_price_surcharge,
                    'main_discount' => $main_discount,
                    'vat' => $vat,
                    'total' => $total,
                    'channal' => 'tiktok',
                    'channal_remark' => null,
                    'payment_type' => $payment_method_type,
                    'status' => $status,
                    'payment_qty' => $payment_qty,
                    'track_no' => $tracking_id,
                    'create_by' => $userId,
                ]
            );

            Sale_order_line::Create(
                [
                    'sale_order_id' => $Sale_order->id,
                    'item_id' => $item->id,
                    'item_name' => $item->name,
                    'qty' => $quantity,
                    'unit_price' => $shipping_price,
                    'discount' => $main_discount,
                    'total' => $total,
                    'create_by' => $userId,
                ]
            );

            Item_trans::Create([
                'item_id' => $item->id,
                'sale_order_id' => $Sale_order->id,
                'customer_id' => $customer->id,
                'date' => $formattedDate,
                'stock' => $stockCount,
                'qty' => -$quantity,
                'balance' => $stockCount-$quantity,
                'location_1_id' => $item->location_id,
                'operation' => 'booking',
                'type' => 'Withdraw',
                'status' => 1,
                'create_by' => $userId,
            ]);

            WorkTelesale::create([
                'date' => $formattedDate,
                'sale_order_id' => $Sale_order->id,
                'customer_id' => $customer->id,
                'status' => 'open',
                'create_by' => $userId,
            ]);

            DB::commit();

        }catch (\Throwable $e) {

            DB::rollback();
            throw new Exception('นำเข้าข้อมูลผิดพลาด : ' . $e->getMessage());
        }

        // dump($Sale_order);

        return $Sale_order;
    }

    public function startRow(): int
    {
        return 3;
    }

    private function getStockCount($item_id)
    {

        $QtyItem = Item_trans::where('item_id', $item_id);
        $qtyItem = $QtyItem->where('status', 1)
            ->sum('qty');

        return intval($qtyItem);
    }
}
