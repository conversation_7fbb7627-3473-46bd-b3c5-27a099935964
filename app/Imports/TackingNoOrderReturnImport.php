<?php

namespace App\Imports;

use App\Models\Sale_order;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;

class TackingNoOrderReturnImport implements ToModel, WithStartRow, SkipsOnError
{
    use Importable, SkipsErrors;

    /**
     * @return int
     */
    public function StartRow(): int
    {
        return 3;
    }

    /**
     * @param array $row
     *
     * @return Sale_order|null
     */
    public function model(array $row)
    {

        return new Sale_order([
            //
        ]);
    }
}
