<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ot_time_log extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'ot_time_log';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function ot()
    {
        return $this->belongsTo(Ot::class);
    }
}
