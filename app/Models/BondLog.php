<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BondLog extends Model
{
    use HasFactory;

    protected $table = 'bond_logs';
    
    protected $fillable = [
        'user_id',
        'year',
        'month',
        'total_amount',
        'deduct_amount',
        'balance',
        'type'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
