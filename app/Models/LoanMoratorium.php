<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoanMoratorium extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'approved_at' => 'datetime',
        'calculated_extra_interest' => 'decimal:2',
    ];

    /**
     * Get the loan that owns the moratorium.
     */
    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the user who approved the moratorium.
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }
}
