<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Work_shift_time extends Model
{
    use HasFactory;
    // use SoftDeletes;

    protected $table = 'work_shift_time';
    protected $softDelete = true;

    // protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    //////////////////////////////////////// relation //////////////////////////////////////
    public  function work_shift()
    {
        return $this->belongsTo(Work_shift::class);
    }

    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }
    public function users()
    {
        return $this->hasMany(User::class);
    }
}
