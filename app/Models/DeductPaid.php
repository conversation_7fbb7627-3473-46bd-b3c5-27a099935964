<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeductPaid extends Model
{
    use HasFactory;

    protected $table = 'deduct_paids';

    public function getTableColumns()
    {
        return $this->getConnection()->getSchemaBuilder()->getColumnListing($this->getTable());
    }

    public function payroll()
    {
        return $this->belongsTo(Payroll::class);
    }


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function deduct_type()
    {
        return $this->belongsTo(DeductType::class);
    }
}
