<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserFiles extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'user_files';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    public function user()
    {
        return $this->hasMany(User::class, 'user_id');
    }
}
