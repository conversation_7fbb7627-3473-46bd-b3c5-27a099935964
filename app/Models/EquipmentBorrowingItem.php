<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EquipmentBorrowingItem extends Model
{
    use HasFactory;

    protected $table = 'equipment_borrowing_items';

    protected $fillable = [
        'borrowing_id',
        'equipment_id',
        'quantity',
        'condition_before',
        'condition_after',
        'notes',
        'is_returned',
        'returned_at'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'is_returned' => 'boolean',
        'returned_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function borrowing()
    {
        return $this->belongsTo(EquipmentBorrowing::class, 'borrowing_id');
    }

    public function equipment()
    {
        return $this->belongsTo(Equipment::class, 'equipment_id');
    }

    // Scopes
    public function scopeReturned($query)
    {
        return $query->where('is_returned', true);
    }

    public function scopeNotReturned($query)
    {
        return $query->where('is_returned', false);
    }

    // Methods
    public function markAsReturned($conditionAfter = null, $notes = null)
    {
        $this->update([
            'is_returned' => true,
            'returned_at' => now(),
            'condition_after' => $conditionAfter,
            'notes' => $notes
        ]);
    }
}
