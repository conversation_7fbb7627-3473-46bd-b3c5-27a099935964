<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tambon extends Model
{
    use HasFactory;

    protected $table = 'tambon';

    public $timestamps = false;

    public function getTableColumns()
    {
        return $this->getConnection()->getSchemaBuilder()->getColumnListing($this->getTable());
    }

    public function amphure() {
        return $this->belongsTo(Amphure::class, 'amphure_id', 'id');
    }
}
