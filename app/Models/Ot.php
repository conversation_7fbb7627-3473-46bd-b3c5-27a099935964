<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ot extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'ot';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function ot_type()
    {
        return $this->belongsTo(Ot_type::class);
    }

    public function user_status()
    {
        return $this->belongsTo(User::class, 'status_by', 'user_id');
    }


    public function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }

    public function user_head()
    {
        return $this->belongsTo(User::class, 'head_id', 'id');
    }

    public function user_hr()
    {
        return $this->belongsTo(User::class, 'hr_id', 'id');
    }


    public function ot_time_logs()
    {
        return $this->hasMany(Ot_time_log::class);
    }
}
