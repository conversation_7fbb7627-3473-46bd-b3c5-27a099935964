<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Leave_permission extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'leave_permissions';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    public  function user_create ()
    {
        return $this->belongsTo(User::class,'create_by','user_id');
    }

    public function leave_type()
    {
        return $this->belongsTo(LeaveType::class,);
    }
}
