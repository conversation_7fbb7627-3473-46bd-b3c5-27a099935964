<?php

namespace App\Models;

use App\Console\Commands\UserLeavePermission;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LeaveType extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'leave_types';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];


    public function user_leave_permissions()
    {
        return $this->hasMany(UserLeavePermission::class);
    }
}
