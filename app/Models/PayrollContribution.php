<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PayrollContribution extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'payroll_contribution';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    public function payroll()
    {
        return $this->belongsTo(Payroll::class,);
    }

    public function user()
    {
        return $this->belongsTo(User::class,);
    }


    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }
}
