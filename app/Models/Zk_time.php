<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Zk_time extends Model
{
    use HasFactory;
    // use SoftDeletes;
    // protected $connection = 'zk_mysql';
    // protected $table = 'zkt_time_firstin_lastout';
    protected $table = 'zkt_time';
    // protected $softDelete = true;

    public function user()
    {
        return $this->belongsTo(User::class, 'personnel_id', 'personnel_id');
    }
}
