<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'users';
    protected $softDelete = true;

    protected $hidden = ['password', 'deleted_at'];

    protected $appends = ['fullname'];

    //////////////////////////////////////// format //////////////////////////////////////

    public function getTableColumns()
    {
        return $this->getConnection()->getSchemaBuilder()->getColumnListing($this->getTable());
    }


    public function getImageAttribute($value)
    {
        return ($value ? url($value) : null);
    }

    public function getImageSignatureAttribute($value)
    {
        return ($value ? url($value) : null);
    }

    // สร้าง Accessor สำหรับ full_name
    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }


    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    //////////////////////////////////////// relation //////////////////////////////////////

    public function permission()
    {
        return $this->belongsTo(Permission::class);
    }
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }

    // public function affiliation()
    // {
    //     return $this->belongsTo(Affiliation::class, 'affiliation_id', 'affiliation_id');
    // }


    //prefix
    public function position()
    {
        //return $this->belongsTo(Position::class, 'position_id', 'position_id');
        //return $this->hasOne(Position::class,'id','id');
        return $this->belongsTo(Position::class,);
    }

    public function zk_times()
    {
        return $this->hasMany(Zk_time::class, 'personnel_id', 'personnel_id');
    }
    //     public function prefix_type()
    //     {
    //         return $this->belongsTo(PrefixType::class, 'prefix_type_id', 'prefix_type_id');
    //     }

    //     public function prefix()
    //     {
    //         return $this->belongsTo(Prefix::class, 'prefix_id', 'prefix_id');
    //     }

    public  function work_shift()
    {
        return $this->belongsTo(Work_shift::class);
    }

    public function head()
    {
        return $this->belongsTo(User::class, 'head_id', 'id');
    }


    public function user_atten()
    {
        return $this->belongsTo(User_attendance::class, 'user_id', 'type');
    }

    public function user_files()
    {
        return $this->hasMany(UserFiles::class, 'user_id', 'user_id');
    }

    public function user_leave_permissions()
    {
        return $this->hasMany(User_leave_permission::class);
    }

    public function userIncomes()
    {
        return $this->hasMany(UserIncome::class);
    }

    public function userDeducts()
    {
        return $this->hasMany(UserDeduct::class);
    }

    public function bondLogs()
    {
        return $this->hasMany(BondLog::class, 'user_id');
    }

    public function warnings()
    {
        return $this->hasMany(Warning::class, 'user_id');
    }

    public function workShiftGroup(){
        return $this->belongsTo(WorkShiftGroup::class, 'work_shift_group_id');
    }

    public function employeeDeposits()
    {
        return $this->hasMany(EmployeeDeposit::class, 'user_id');
    }

    public function loans()
    {
        return $this->hasMany(Loan::class, 'user_id');
    }

    public function loanApplications()
    {
        return $this->hasMany(LoanApplication::class, 'user_id');
    }

    public function loanMoratoriums()
    {
        return $this->hasMany(LoanMoratorium::class, 'user_id');
    }

    public function loanPolicyPersonalLimit()
    {
        return $this->hasOne(LoanPolicyPersonalLimit::class, 'user_id');
    }
}
