<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Loan extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $softDelete = true;

    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function loan_schedules()
    {
        return $this->hasMany(LoanSchedule::class);
    }

    public function loan_application()
    {
        return $this->belongsTo(LoanApplication::class);
    }

    public function loan_moratoriums()
    {
        return $this->hasMany(LoanMoratorium::class);
    }

    public function loan_payments()
    {
        return $this->hasMany(LoanPayment::class);
    }
}
