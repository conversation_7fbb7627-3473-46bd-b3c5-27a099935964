<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Leave_table_date extends Model
{
    use HasFactory;
    // use SoftDeletes;
    protected $table = 'leave_table_date';
    // protected $softDelete = true;
    protected $hidden = ['deleted_at'];


    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }

    public  function leave_table()
    {
        return $this->belongsTo(Leave_table::class,);
    }
}
