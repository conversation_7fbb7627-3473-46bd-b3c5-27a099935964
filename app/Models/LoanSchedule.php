<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

enum LoanScheduleStatus:string {
    case DUE = 'due';
    case OVERDUE = 'overdue';
    case PAID = 'paid';
    case MORATORIUM = 'moratorium';
}

class LoanSchedule extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }
}
