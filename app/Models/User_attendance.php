<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class User_attendance extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $table = 'user_attendance';
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];

    public function user()
    {
        return $this->belongsTo(User::class,);
    }

    public function zk_time()
    {
        return $this->belongsTo(Zk_time::class,'zkt_time_id','id');
    }

    public function leave_table()
    {
        return $this->belongsTo(Leave_table::class,);
    }



}
