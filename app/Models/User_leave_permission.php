<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class User_leave_permission extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'user_leave_permissions';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    public function getTableColumns()
    {
        return $this->getConnection()->getSchemaBuilder()->getColumnListing($this->getTable());
    }

    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class,);
    }

    public function leave_type()
    {
        return $this->belongsTo(LeaveType::class,);
    }
}
