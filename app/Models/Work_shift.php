<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Work_shift extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'work_shift';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    //////////////////////////////////////// relation //////////////////////////////////////
    public function work_shift_times()
    {
        return $this->hasMany(Work_shift_time::class);
    }

    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }
}
