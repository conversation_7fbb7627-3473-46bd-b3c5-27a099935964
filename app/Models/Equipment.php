<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Equipment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'equipments';

    protected $fillable = [
        'code',
        'name',
        'description',
        'brand',
        'model',
        'serial_number',
        'purchase_price',
        'purchase_date',
        'condition',
        'status',
        'location',
        'image',
        'category_id',
        'branch_id',
        'is_active',
        'create_by',
        'update_by'
    ];

    protected $casts = [
        'purchase_price' => 'decimal:2',
        'purchase_date' => 'date',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(EquipmentCategory::class, 'category_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function borrowingItems()
    {
        return $this->hasMany(EquipmentBorrowingItem::class, 'equipment_id');
    }

    public function currentBorrowing()
    {
        return $this->hasOneThrough(
            EquipmentBorrowing::class,
            EquipmentBorrowingItem::class,
            'equipment_id',
            'id',
            'id',
            'borrowing_id'
        )->where('status', 'borrowed');
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available')->where('is_active', true);
    }

    public function scopeBorrowed($query)
    {
        return $query->where('status', 'borrowed');
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        return $this->image ? url($this->image) : null;
    }

    public function getIsAvailableAttribute()
    {
        return $this->status === 'available' && $this->is_active;
    }
}
