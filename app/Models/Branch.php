<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Branch extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'branch';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    //////////////////////////////////////// relation //////////////////////////////////////
    public  function company ()
    {
        return $this->belongsTo(Company::class);
    }

    public  function user_create ()
    {
        return $this->belongsTo(User::class,'create_by','user_id');
    }
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function positions()
    {
        return $this->hasMany(Position::class);
    }

    public function work_shifts()
    {
        return $this->hasMany(Work_shift::class);
    }

    public function holidays()
    {
        return $this->hasMany(Holiday::class);
    }
    
    public function payrolls()
    {
        return $this->hasMany(Payroll::class);
    }
}
