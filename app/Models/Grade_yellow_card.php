<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Grade_yellow_card extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $table = 'grade_yellow_card';
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];


    public  function user_create ()
    {
        return $this->belongsTo(User::class,'create_by','user_id');
    }

}
