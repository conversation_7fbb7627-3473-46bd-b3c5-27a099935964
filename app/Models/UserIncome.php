<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserIncome extends Model
{
    use HasFactory;

    protected $table = 'user_income';

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function income_type()
    {
        return $this->belongsTo(IncomeType::class, 'income_types_id', 'id');
    }
}
