<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class EquipmentBorrowing extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'equipment_borrowings';

    protected $fillable = [
        'borrowing_code',
        'user_id',
        'branch_id',
        'borrow_date',
        'expected_return_date',
        'actual_return_date',
        'status',
        'purpose',
        'notes',
        'return_notes',
        'approved_by',
        'approved_at',
        'returned_by',
        'returned_at',
        'create_by',
        'update_by'
    ];

    protected $casts = [
        'borrow_date' => 'date',
        'expected_return_date' => 'date',
        'actual_return_date' => 'date',
        'approved_at' => 'datetime',
        'returned_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function returnedBy()
    {
        return $this->belongsTo(User::class, 'returned_by');
    }

    public function borrowingItems()
    {
        return $this->hasMany(EquipmentBorrowingItem::class, 'borrowing_id');
    }

    public function equipments()
    {
        return $this->belongsToMany(Equipment::class, 'equipment_borrowing_items', 'borrowing_id', 'equipment_id')
                    ->withPivot('quantity', 'condition_before', 'condition_after', 'notes', 'is_returned', 'returned_at')
                    ->withTimestamps();
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeBorrowed($query)
    {
        return $query->where('status', 'borrowed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function($q) {
                        $q->where('status', 'borrowed')
                          ->where('expected_return_date', '<', Carbon::now()->toDateString());
                    });
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    // Accessors
    public function getIsOverdueAttribute()
    {
        return $this->status === 'borrowed' &&
               $this->expected_return_date &&
               Carbon::parse($this->expected_return_date)->isPast();
    }

    public function getDaysOverdueAttribute()
    {
        if (!$this->is_overdue) {
            return 0;
        }

        return Carbon::parse($this->expected_return_date)->diffInDays(Carbon::now());
    }

    public function getTotalItemsAttribute()
    {
        return $this->borrowingItems->sum('quantity');
    }

    // Methods
    public function generateBorrowingCode()
    {
        $prefix = 'BRW';
        $date = Carbon::now()->format('Ymd');
        $lastBorrowing = static::whereDate('created_at', Carbon::today())
                              ->orderBy('id', 'desc')
                              ->first();

        $sequence = $lastBorrowing ? (int)substr($lastBorrowing->borrowing_code, -3) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    public function approve($approvedBy)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => Carbon::now()
        ]);
    }

    public function markAsBorrowed()
    {
        $this->update(['status' => 'borrowed']);

        // Update equipment status
        foreach ($this->borrowingItems as $item) {
            $item->equipment->update(['status' => 'borrowed']);
        }
    }

    public function returnEquipment($returnedBy, $returnNotes = null)
    {
        $this->update([
            'status' => 'returned',
            'actual_return_date' => Carbon::now()->toDateString(),
            'returned_by' => $returnedBy,
            'returned_at' => Carbon::now(),
            'return_notes' => $returnNotes
        ]);

        // Update equipment status and borrowing items
        foreach ($this->borrowingItems as $item) {
            $item->update([
                'is_returned' => true,
                'returned_at' => Carbon::now()
            ]);
            $item->equipment->update(['status' => 'available']);
        }
    }
}
