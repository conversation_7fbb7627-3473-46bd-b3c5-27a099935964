<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Warning extends Model
{
    use HasFactory;

    protected $table = 'warnings';

    protected $fillable = [
        'branch_id',
        'user_id',
        'date',
        'title',
        'description',
        'status',
        'approved_by',
        'acknowledged_by'
    ];

    public function user()
    {
        return $this->belongsTo(User::class , 'user_id', 'id');
    }

    public function approver() {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function acknowledger() {
        return $this->belongsTo(User::class, 'acknowledged_by');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }
    
}
