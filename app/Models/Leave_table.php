<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Leave_table extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'leave_tables';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class,);
    }

    public function leave_type()
    {
        return $this->belongsTo(LeaveType::class,);
    }

    public function user_status()
    {
        return $this->belongsTo(User::class, 'status_by', 'user_id');
    }

    public function leave_table_dates()
    {
        return $this->hasMany(Leave_table_date::class,);
    }


    public function user_head()
    {
        return $this->belongsTo(User::class, 'head_id', 'id');
    }

    public function user_hr()
    {
        return $this->belongsTo(User::class, 'hr_id', 'id');
    }
}
