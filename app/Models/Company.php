<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'company';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    public function getImageAttribute($value)
    {
        return ($value ? url($value) : null);
    }

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    //////////////////////////////////////// relation //////////////////////////////////////
    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }
}
