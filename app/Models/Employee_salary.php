<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Employee_salary extends Model
{
    use HasFactory;

    use SoftDeletes;
    protected $table = 'employee_salaries';
    protected $fillable = [
        'user_id',
        'hire_date',
        'salary',
        'commission',
        'ot',
        'income',
        'deduct',
        'total',
        'slip',
        'create_by',
    ];

    protected $softDelete = true;
    protected $hidden = ['deleted_at'];


    public function getSlipAttribute($value)
    {
        return ($value ? url($value) : null);
    }


    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    public  function user_create()
    {
        return $this->belongsTo(User::class,'create_by','user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
