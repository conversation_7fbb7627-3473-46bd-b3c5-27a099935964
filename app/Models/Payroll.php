<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payroll extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'payroll';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    //////////////////////////////////////// format //////////////////////////////////////

    public function getSlipAttribute($value)
    {
        return ($value ? url($value) : null);
    }

    // protected function serializeDate(DateTimeInterface $date)
    // {
    //     return $date->format('d/m/Y H:i:s');
    // }

    public function payrollRound()
    {
        return $this->belongsTo(PayrollRound::class, 'round', 'round');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }

    public function incomePaids()
    {
        return $this->hasMany(IncomePaid::class);
    }

    public function deductPaids()
    {
        return $this->hasMany(DeductPaid::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }


    public function payrollContributions()
    {
        return $this->hasMany(PayrollContribution::class);
    }
}
