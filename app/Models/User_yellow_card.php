<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class User_yellow_card extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $table = 'user_yellow_card';
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];

    public function leave_table()
    {
        return $this->belongsTo(Leave_table::class,);
    }
}
