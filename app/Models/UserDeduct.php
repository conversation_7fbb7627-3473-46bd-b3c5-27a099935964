<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserDeduct extends Model
{
    use HasFactory;

    protected $table = 'user_deduct';


    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function deduct_type()
    {
        return $this->belongsTo(DeductType::class);
    }
}
