<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Withdraw_salary extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'withdraw_salary';
    protected $softDelete = true;

    protected $hidden = ['deleted_at'];

    public  function user_create()
    {
        return $this->belongsTo(User::class, 'create_by', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
