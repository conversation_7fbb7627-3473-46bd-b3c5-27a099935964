<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OrderKerryExport implements FromArray, WithHeadings
{
    protected $data;
    public function __construct(array $data)
    {
        $this->data = $data;

    }
    function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'Recipient Name',
            'Mobile No.',
            'Email',
            'Address #1',
            'Address #2',
            'Zip Code',
            'COD Amt (Baht)',
            'Remark',
            'Ref #1',
            'Ref #2',
            'Sender Ref',
            'Tracking No',
        ];
    }
}
