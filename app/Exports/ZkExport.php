<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ZkExport implements FromArray, WithHeadings
{
    protected $data;
    public function __construct(array $data)
    {
        $this->data = $data;
    }
    function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'รหัสสแกนนิ้ว',
            'ชื่อ',
            'นามสกุล',
            'วันที่',
            'เวลาเข้า',
            'เวลาออก',
            'เวลาพักเที่ยง',
            'เวลาเลิกพักเที่ยง',
        ];
    }
}
