<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;

class TiktokshopsalesExport implements WithMultipleSheets
{
    protected $dataday;
    protected $datapage;
    protected $item;
    protected $month;

    public function __construct(array $dataday,array $datapage, $item ,$month)
    {
        $this->dataday = $dataday;
        $this->datapage = $datapage;
        $this->item = $item;
        $this->month = $month;
    }

    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new TiktokshopsalesSummarySheet($this->datapage, $this->item, 'summary', $this->month); //ข้อมูลทั้งหมด

        foreach ($this->datapage as $pageName => $pageData) {
            $sheets[] = new TiktokshopsalesSummaryPage($pageData, $this->item, $pageName, $this->month); //แยกตาม page
        }

        foreach ($this->dataday as $day => $pages) {
            $sheets[] = new TiktokshopsalesDayExport($pages, $this->item, $day); //แยกตามวัน
        }
        return $sheets;
    }
}
class TiktokshopsalesSummarySheet implements FromView, WithTitle
{
    protected $data;
    protected $item;
    protected $pageName;
    protected $month;

    public function __construct(array $data, $item, $pageName, $month)
    {
        $this->data = $data;
        $this->item = $item;
        $this->pageName =  $pageName;
        $this->month = $month;
    }

    public function view(): View
    {
        $aggregatedData[] = [];

        foreach ($this->data as $pageData) {
            foreach ($pageData as $day => $orders) {
                if (!isset($aggregatedData[$day])) {
                    $aggregatedData[$day] = [];
                }
                $aggregatedData[$day] = array_merge($aggregatedData[$day], $orders);
            }
        }
        // dd($aggregatedData);
        return view('export.Tiktokshopsalesdata', [
            'data' => $aggregatedData,
            'item' => $this->item,
            'month' => $this->month
        ]);
    }

    public function title(): string
    {
        return $this->pageName;
    }
}
class TiktokshopsalesSummaryPage implements FromView, WithTitle
{
    protected $data;
    protected $item;
    protected $pageName;
    protected $month;

    public function __construct(array $data, $item, $pageName, $month)
    {
        $this->data = $data;
        $this->item = $item;
        $this->pageName =  $pageName;
        $this->month = $month;
    }

    public function view(): View
    {
        // Logic to prepare summary data goes here
        return view('export.Tiktokshopsalesdata_page', [
            'data' => $this->data,
            'item' => $this->item,
            'month' => $this->month,
        ]);
    }
    public function title(): string
    {
        return $this->pageName;
    }
}
class TiktokshopsalesDayExport implements FromView, WithTitle
{
    protected $data;
    protected $item;
    protected $day;

    public function __construct(array $data, $item, $day)
    {
        $this->data = $data;
        $this->item = $item;
        $this->day = $day;
    }

    public function view(): View
    {
        return view('export.Tiktokshopsalesdata_company', [
            'data' => $this->data,
            'item' => $this->item,
            'day' => $this->day,
        ]);
    }

    public function title(): string
    {
        return "Day $this->day";
    }
}
