<?php

namespace App\Exports;

use App\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ReportUpSaleExport implements FromArray , WithHeadings
{
    protected  $data;
    public function __construct(array $data)
   {
       $this->data = $data;

   }
    public function array(): array
   {
       return $this->data;
   }

   public function headings(): array
   {
       return [
        'รหัสเทเลเซลล์',
        'ชื่อเทเลเซลล์',
        'หมายเลขคำสั่งซื้อ',
        'วันที่คำสั่งซื้อ',
        'สถานะคำสั่งซ์้อ',
        'รหัสสินค้า',
        'ชื่อสินค้า' ,
        'sku',
        'จำนวน' ,
        'ราคา',
        'ส่วนลด',
        'ราคาสุทธิ' ,
        'ชื่อลูกค้า' ,
        'อีเมลล์' ,
        'หมายเลขโทรศัพท์',
        'ที่อยู่',
        'จังหวัด',
        'เขต' ,
        'แขวง',
        'รหัสไปรษณีย์',
        'หมายเลขโทรศัพท์ 2' ,
        'facebook',
       ];
   }
}
