<?php

namespace App\Exports;

use App\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SocialExport implements FromArray, WithHeadings
{
    protected  $data;
    public function __construct(array $data)
    {
        $this->data = $data;
    }
    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'เลขบัตรประชาชน',
            'คำนำหน้า',
            'ชื่อ',
            'สกุล',
            'ค่าจ้าง',
            'เงินสมทบ',
        ];
    }
}
