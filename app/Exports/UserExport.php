<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class UserExport implements FromCollection, WithHeadings
{
    public function collection()
    {
        return User::select([
            'users.id',
            'branch.name as branch_name',
            'permission.name as permission_name',
            'user_id',
            'prefix',
            'first_name',
            'last_name',
            'first_name_en',
            'last_name_en',
            'citizen_no',
            'department.name as department',
            'position.name as position',
            'nickname',
            'sex',
            'email',
            'phone_no',
            'birthday',
            'nationality',
            'home_address',
            'province',
            'district',
            'subdistrict',
            'zip_code',
            'register_date',
            'salary',
            'bank_name',
            'account_no',
            'account_name',
            'head_id',
        ])
            ->leftJoin('branch as branch', 'users.branch_id', '=', 'branch.id')
            ->left<PERSON>oin('permission as permission', 'users.permission_id', '=', 'permission.id')
            ->leftJoin('department as department', 'users.department_id', '=', 'department.id')
            ->leftJoin('position as position', 'users.position_id', '=', 'position.id')
            ->get()
            ->map(function ($user) {
                return [
                    $user->id,
                    $user->branch_name,
                    $user->permission_name,
                    $user->user_id,
                    $user->prefix,
                    $user->first_name,
                    $user->last_name,
                    $user->first_name_en,
                    $user->last_name_en,
                    $user->citizen_no,
                    $user->department,
                    $user->position,
                    $user->nickname,
                    $user->sex,
                    $user->email,
                    $user->phone_no,
                    $user->birthday,
                    $user->nationality,
                    $user->home_address,
                    $user->province,
                    $user->district,
                    $user->subdistrict,
                    $user->zip_code,
                    $user->register_date,
                    $user->salary,
                    $user->bank_name,
                    $user->account_no,
                    $user->account_name,
                    $user->head_id,
                ];
            });
    }

    public function headings(): array
    {
        return [
            'id',
            'branch_name',
            'permission_name',
            'user_id',
            'prefix',
            'first_name',
            'last_name',
            'first_name_en',
            'last_name_en',
            'citizen_no',
            'department',
            'position',
            'nickname',
            'sex',
            'email',
            'phone_no',
            'birthday',
            'nationality',
            'home_address',
            'province',
            'district',
            'subdistrict',
            'zip_code',
            'register_date',
            'salary',
            'bank_name',
            'account_no',
            'account_name',
            'head_id',
        ];
    }
}
