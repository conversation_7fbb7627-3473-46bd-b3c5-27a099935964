<?php

namespace App\Services;

use LINE\LINEBot;
use LINE\LINEBot\HTTPClient\CurlHTTPClient;
use LINE\LINEBot\MessageBuilder\TextMessageBuilder; // อาจไม่ใช้สำหรับ Rich Menu โดยตรง แต่เผื่อไว้
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LineRichMenuService
{
    protected $bot;
    protected $httpClient;

    public function __construct()
    {
        $this->httpClient = new CurlHTTPClient(config('line.bot.channel_token'));
        $this->bot = new LINEBot($this->httpClient, ['channelSecret' => config('line.bot.channel_secret')]);
    }

    /**
     * สร้าง Rich Menu ใหม่
     * @param array $richMenuData JSON structure of the rich menu
     * @return string richMenuId on success, null on failure
     */
    public function createRichMenu(array $richMenuData): ?string
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('line.bot.channel_token'),
                'Content-Type' => 'application/json',
            ])->post('https://api.line.me/v2/bot/richmenu', $richMenuData);

            if ($response->successful()) {
                $data = $response->json();
                return $data['richMenuId'];
            } else {
                Log::error('Failed to create rich menu: ' . $response->body());
                return null;
            }
        } catch (Exception $e) {
            Log::error('Exception while creating rich menu: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * อัปโหลดรูปภาพสำหรับ Rich Menu
     * @param string $richMenuId
     * @param string $imagePath Path to the image file
     * @param string $contentType MIME type of the image (e.g., image/jpeg, image/png)
     * @return bool
     */
    public function uploadRichMenuImage(string $richMenuId, string $imagePath, string $contentType): bool
    {
        try {
            $response = $this->bot->uploadRichMenuImage($richMenuId, $imagePath, $contentType);
            if ($response->isSucceeded()) {
                return true;
            } else {
                Log::error('Failed to upload rich menu image: ' . $response->getRawBody());
                return false;
            }
        } catch (Exception $e) {
            Log::error('Exception while uploading rich menu image: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * เชื่อมโยง Rich Menu กับผู้ใช้
     * @param string $userId
     * @param string $richMenuId
     * @return bool
     */
    public function linkRichMenuToUser(string $userId, string $richMenuId): bool
    {
        try {
            $response = $this->bot->linkRichMenu($userId, $richMenuId);
            if ($response->isSucceeded()) {
                return true;
            } else {
                Log::error('Failed to link rich menu to user ' . $userId . ': ' . $response->getRawBody());
                return false;
            }
        } catch (Exception $e) {
            Log::error('Exception while linking rich menu: ' . $e->getMessage());
            return false;
        }
    }

    public function linkRichMenuToUsers(array $userIds, string $richMenuId): bool
    {
        try {
            $response = $this->bot->bulkLinkRichMenu($userIds, $richMenuId);
            if ($response->isSucceeded()) {
                return true;
            } else {
                Log::error('Failed to link rich menu to users : ' . $response->getRawBody());
                return false;
            }
        } catch (Exception $e) {
            Log::error('Exception while linking rich menu: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * ดึงรายการ Rich Menu ทั้งหมด
     * @return array|null List of rich menus or null on failure
     */
    public function getRichMenuList(): ?array
    {
        try {
            $response = $this->bot->getRichMenuList();
            if ($response->isSucceeded()) {
                return $response->getJSONDecodedBody()['richmenus'] ?? [];
            } else {
                Log::error('Failed to get rich menu list: ' . $response->getRawBody());
                return null;
            }
        } catch (Exception $e) {
            Log::error('Exception while getting rich menu list: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * ลบ Rich Menu
     * @param string $richMenuId
     * @return bool
     */
    public function deleteRichMenu(string $richMenuId): bool
    {
        try {
            $response = $this->bot->deleteRichMenu($richMenuId);
            if ($response->isSucceeded()) {
                return true;
            } else {
                Log::error('Failed to delete rich menu ' . $richMenuId . ': ' . $response->getRawBody());
                return false;
            }
        } catch (Exception $e) {
            Log::error('Exception while deleting rich menu: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * ยกเลิกการเชื่อมโยง Rich Menu จากผู้ใช้
     * หากต้องการยกเลิกจาก Rich Menu เฉพาะ ให้ส่ง $richMenuId เข้าไป
     * หากไม่ระบุ $richMenuId จะเป็นการยกเลิก Rich Menu ทั้งหมดที่ผู้ใช้เชื่อมโยงไว้
     * (ซึ่งปกติแล้วผู้ใช้จะเชื่อมโยงได้แค่ 1 Rich Menu ต่อครั้ง)
     * @param string $userId
     * @return bool
     */
    public function unlinkRichMenuFromUser(string $userId): bool
    {
        try {
            $response = $this->bot->unlinkRichMenu($userId);
            if ($response->isSucceeded()) {
                return true;
            } else {
                Log::error('Failed to unlink rich menu from user ' . $userId . ': ' . $response->getRawBody());
                return false;
            }
        } catch (Exception $e) {
            Log::error('Exception while unlinking rich menu: ' . $e->getMessage());
            return false;
        }
    }
}
