<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use \Firebase\JWT\JWT;

class CheckJWT
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public $key = "gs_key";

    public function handle($request, Closure $next)
    {
        try {
            $header = $request->header('Authorization');
            $token = str_replace('Bearer ', '', $header);
            //dd($header );
            if (!$token) {
                return response()->json([
                    'code' => '401',
                    'status' => false,
                    'message' => 'Token Not Found',
                    'data' => [],
                ], 401);
            }

            $payload = JWT::decode($token, $this->key, array('HS256'));

            $company_id = null;
            $branch_id = null;
            $user_id = null;

            if ($payload->lun->permission->permission_view == 'all') {
                $company_id = null;
                $branch_id =  $payload->lun->branch->id;
                $user_id = null;

            }
            if ($payload->lun->permission->permission_view == 'company') {
                $company_id = $payload->lun->branch->company_id;
                $branch_id =  $payload->lun->branch->id;
                $user_id = null;

            }
            if ($payload->lun->permission->permission_view == 'branch') {
                $company_id = $payload->lun->branch->company_id;
                $branch_id = $payload->lun->branch->id;
                $user_id = null;

            }
            if ($payload->lun->permission->permission_view == 'user') {
                $company_id = $payload->lun->branch->company_id;
                $branch_id = $payload->lun->branch->id;
                $user_id = $payload->lun->user_id;
                $action_branch_id = $payload->lun->branch->id;
            }

            $request->request->add([
                'login_id' => $payload->aud,
                'login_by' => $payload->lun,
                'login_permission_view' => $payload->lun->permission->permission_view,
                'login_user_id' => $user_id,
                'login_branch_id' => $branch_id,
                'login_company_id' => $company_id,


            ]);
        } catch (\Firebase\JWT\ExpiredException $e) {
            return response()->json([
                'code' => '401',
                'status' => false,
                'message' => 'Token is expire',
                'data' => [],
            ], 401);
        } catch (Exception $e) {
            return response()->json([
                'code' => '401',
                'status' => false,
                'message' => 'Can not verify identity',
                'data' => [],
            ], 401);
        }

        return $next($request);
    }
}
