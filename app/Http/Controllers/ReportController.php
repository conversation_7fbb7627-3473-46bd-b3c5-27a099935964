<?php

namespace App\Http\Controllers;

use App\Exports\TiktokshopsalesExport;
use App\Models\BondLog;
use App\Models\Branch;
use App\Models\Commission;
use App\Models\Company;
use App\Models\Config;
use App\Models\Customer;
use App\Models\Grade_yellow_card;
use App\Models\Item;
use App\Models\Item_trans;
use App\Models\LeaveType;
use App\Models\Location;
use App\Models\Sale_order;
use App\Models\Sale_order_line;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\User_yellow_card;
use App\Models\UserIncome;
use App\Models\Employee_salary;
use App\Models\Page;
use App\Models\UserDeduct;
use App\Models\Work_times;
use App\Models\WorkTelesale;
use App\Models\Zk_time;
use App\Models\Ot;
use App\Models\Leave_table;
use App\Models\Work_shift;
use App\Models\Work_shift_time;
use App\Models\Zkt_time_firstin_lastout;
use App\Models\IncomePaid;
use App\Models\DeductPaid;
use App\Models\Holiday;
use App\Models\LoanPayment;
use App\Models\Payroll;
use App\Models\PayrollContribution;
use App\Models\PayrollContributionSetting;
use App\Models\PayrollRound;
use App\Models\WorkAds;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\CarbonPeriod;
use DateTime;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;


class ReportController extends Controller
{

    public function getDashboard()
    {

        // $Customer = Customer::where('status', true)->count();
        // $Item = Item::where('status', true)->count();
        // $Sale_order_return = Sale_order::where('date_time', 'like', '%' . date('Y') . '-%')
        //     ->where('status', 'return')
        //     ->sum('total');

        // $Sale_order_year = Sale_order::where('date_time', 'like', '%' . date('Y') . '-%')
        //     ->sum('total');

        // $today = Carbon::now()->toDateString();

        // //jobs_group_status
        // $SaleOrderByStatus = Sale_order::whereDate('created_at', $today)
        //     ->select('status', DB::raw('count(*) as total'))
        //     ->groupBy('status')
        //     ->get();

        // $monthlySales = Sale_order::select(
        //     DB::raw('YEAR(date_time) as year'),
        //     DB::raw('MONTH(date_time) as month'),
        //     DB::raw('SUM(total) as total_sales')
        // )
        //     // ->where('status', 'finish')
        //     ->groupBy(DB::raw('YEAR(date_time)'), DB::raw('MONTH(date_time)'))
        //     ->orderBy(DB::raw('YEAR(date_time)'), 'ASC')
        //     ->orderBy(DB::raw('MONTH(date_time)'), 'ASC')
        //     ->get();

        // $returnShipments = Sale_order::select(
        //     DB::raw('YEAR(date_time) as year'),
        //     DB::raw('MONTH(date_time) as month'),
        //     DB::raw('COUNT(*) as return_shipments')
        // )
        //     // ->where('status', 'return')
        //     ->groupBy(DB::raw('YEAR(date_time)'), DB::raw('MONTH(date_time)'))
        //     ->orderBy(DB::raw('YEAR(date_time)'), 'ASC')
        //     ->orderBy(DB::raw('MONTH(date_time)'), 'ASC')
        //     ->get();

        // $data = [
        //     'sum_customer' => $Customer,
        //     'sum_item' => $Item,
        //     'sum_order_return' => $Sale_order_return,
        //     'sum_order_year' => $Sale_order_year,

        //     'monthly_sales' => $monthlySales,
        //     'return_shipments' => $returnShipments,

        // ];

        // return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $data);
    }



    public function sort_array_multidim(array $array, $order_by)
    {
        //TODO -c flexibility -o tufanbarisyildirim : this error can be deleted if you want to sort as sql like "NULL LAST/FIRST" behavior.
        if (!is_array($array[0])) {
            throw new Exception('$array must be a multidimensional array!', E_USER_ERROR);
        }

        $columns = explode(',', $order_by);
        foreach ($columns as $col_dir) {
            if (preg_match('/(.*)([\s]+)(ASC|DESC)/is', $col_dir, $matches)) {
                if (!array_key_exists(trim($matches[1]), $array[0])) {
                    trigger_error('Unknown Column <b>' . trim($matches[1]) . '</b>', E_USER_NOTICE);
                } else {
                    if (isset($sorts[trim($matches[1])])) {
                        trigger_error('Redundand specified column name : <b>' . trim($matches[1] . '</b>'));
                    }

                    $sorts[trim($matches[1])] = 'SORT_' . strtoupper(trim($matches[3]));
                }
            } else {
                // throw new Exception("Incorrect syntax near : '{$col_dir}'",E_USER_ERROR);
            }
        }

        //TODO -c optimization -o tufanbarisyildirim : use array_* functions.
        $colarr = array();
        foreach ($sorts as $col => $order) {
            $colarr[$col] = array();
            foreach ($array as $k => $row) {
                $colarr[$col]['_' . $k] = strtolower($row[$col]);
            }
        }

        $multi_params = array();
        foreach ($sorts as $col => $order) {
            $multi_params[] = '$colarr[\'' . $col . '\']';
            $multi_params[] = $order;
        }

        $rum_params = implode(',', $multi_params);
        eval("array_multisort({$rum_params});");

        $sorted_array = array();
        foreach ($colarr as $col => $arr) {
            foreach ($arr as $k => $v) {
                $k = substr($k, 1);
                if (!isset($sorted_array[$k])) {
                    $sorted_array[$k] = $array[$k];
                }

                $sorted_array[$k][$col] = $array[$k][$col];
            }
        }

        return array_values($sorted_array);
    }



    public function ReportTimeAttendance(Request $request)
    {
        $login_branch_id = $request->login_branch_id;

        $date_start = $request->date_start;
        if ($date_start) {
            $date_start = $date_start . ' 00:00:00';
        }

        $date_end = $request->date_end;
        if ($date_end) {
            $date_end = $date_end . ' 23:59:59';
        }
        $personnel_id = $request->personnel_id;

        // if (!isset($personnel_id)) {
        //     return $this->returnErrorData('กรุณาเลือกพนักงาน', 404);
        // }
        $User = $this->TimeAttendance($date_start, $date_end, $personnel_id, $login_branch_id);

        return $this->returnSuccess('Successful', $User);
    }

    public function TimeAttendance($date_start, $date_end, $personnel_id, $branch_id)
    {

        if ($date_start) {
            $date_start = $date_start . ' 00:00:00';
        }

        if ($date_end) {
            $date_end = $date_end . ' 23:59:59';
        }


        $holidays = Holiday::pluck('date')->toArray();

        $user = Zk_time::select('personnel_id', DB::raw('DATE(time) as date'))
            ->with('user.position');

        if ($personnel_id) {
            $user->where('personnel_id', $personnel_id);
        }

        if ($branch_id) {
            $user->whereHas('user', function ($query) use ($branch_id) {
                $query->where('branch_id', $branch_id);
            });
        }

        if ($date_start && $date_end) {
            $user->where('time', '>=', $date_start);
            $user->where('time', '<=', $date_end);
        }

        $user->whereNotIn(DB::raw('DATE(time)'), $holidays);

        $User = $user->groupBy('personnel_id', 'date')
            ->get();

        for ($i = 0; $i < count($User); $i++) {

            $User[$i]->time_in = null;
            $User[$i]->time_out = null;
            $User[$i]->time_brake_in = null;
            $User[$i]->time_brake_out = null;
            $User[$i]->attendance = null;

            //get work shift time
            $strDate = date('D', strtotime($User[$i]->date));

            //check working day
            $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
            if ($User[$i]->work_shift_id) {
                $workTime->where('work_shift_id', $User[$i]->user->work_shift_id);
            }
            $WorkTime =  $workTime->first();


            //time_brake_in
            $zk_time_brake_start = Zk_time::with('user')
                ->where('personnel_id', $User[$i]->personnel_id)
                // ->where('time', 'like', '%' . $User[$i]->date . '%')
                // ->whereBetween('time', [$User[$i]->date . ' 11:55:00', $User[$i]->date . ' 14:00:00']) // หาตั้งแต่เวลา 12:00:00 เป็นต้นไป
                ->whereBetween('time', [$User[$i]->date . ' ' . $WorkTime->time_brake_in_start, $User[$i]->date . ' ' . $WorkTime->time_brake_in_end])
                ->orderby('time', 'asc')
                ->first();
            // dd($zk_time_brake_start);
            if ($zk_time_brake_start) {
                $User[$i]->time_brake_in = date('H:i:s', strtotime($zk_time_brake_start->time));
            }

            //time_brake_out
            $zk_time_brake_end = Zk_time::with('user')
                ->where('personnel_id', $User[$i]->personnel_id)
                // ->where('time', 'like', '%' . $User[$i]->date . '%')
                // ->whereBetween('time', [$User[$i]->date . ' 12:30:00', $User[$i]->date . ' 14:00:00']) // หาตั้งแต่เวลา 13:00:00 ถึงเป็นต้นไป
                ->whereBetween('time', [$User[$i]->date . ' ' . $WorkTime->time_brake_out_start, $User[$i]->date . ' ' . $WorkTime->time_brake_out_end])
                ->orderby('time', 'asc')
                ->first();

            if ($zk_time_brake_end) {
                $User[$i]->time_brake_out = date('H:i:s', strtotime($zk_time_brake_end->time));
            }

            //time_in
            $zk_time_in = Zk_time::with('user');

            $zk_time_in->where('personnel_id', $User[$i]->personnel_id);
            $zk_time_in->where('time', 'like', '%' . $User[$i]->date . '%');

            $Zk_time_in = $zk_time_in->orderby('time', 'asc')->first();

            if ($Zk_time_in) {
                $User[$i]->time_in = date('H:i:s', strtotime($Zk_time_in->time));
            }

            //time_out
            $zk_time_out = Zk_time::with('user');

            $zk_time_out->where('personnel_id', $User[$i]->personnel_id);
            $zk_time_out->where('time', 'like', '%' . $User[$i]->date . '%');

            $Zk_time_out = $zk_time_out->orderby('time', 'desc')->first();

            if ($Zk_time_out) {
                $User[$i]->time_out = date('H:i:s', strtotime($Zk_time_out->time));
            }

            //attendance
            if ($User[$i]->user) {
                $User_attendance = User_attendance::where('date', $User[$i]->date)
                    ->where('user_id', $User[$i]->user->id)
                    ->first();

                if ($User_attendance) {
                    $User[$i]->attendance = $User_attendance->type;
                }
            }

            //
        }

        return $User;
    }

    public function ReportUserYellowCard(Request $request)
    {

        $date_start = $request->date_start . ' 00:00:00';
        $date_end = $request->date_end . ' 23:23:59';

        $User = User::where('status', 1)->get();

        for ($i = 0; $i < count($User); $i++) {

            $LeaveType = LeaveType::get();

            $columns = [];
            $LeaveTypeYellowCard = [];
            $sumUser_yellow_card = 0;
            for ($j = 0; $j < count($LeaveType); $j++) {
                $columns[$j]['title'] = $LeaveType[$j]->name;

                $LeaveTypeId = $LeaveType[$j]->id;

                $user_yellow_card = User_yellow_card::where('user_id', $User[$i]->id)
                    ->WhereHas('leave_table', function ($query) use ($LeaveTypeId) {
                        $query->where('leave_type_id', $LeaveTypeId);
                    });

                if ($date_start && $date_end) {
                    $user_yellow_card->where('created_at', '>=', $date_start);
                    $user_yellow_card->where('created_at', '<=', $date_end);
                }

                $User_yellow_card = $user_yellow_card->sum('qty');

                $LeaveTypeYellowCard[] = [
                    'name' => $LeaveType[$j]->name,
                    'qty' => $User_yellow_card,
                ];

                $sumUser_yellow_card += $User_yellow_card;
            }

            $User[$i]->leave = $LeaveTypeYellowCard;

            $User[$i]->miss = 0;
            $User[$i]->late = 0;

            //qty
            $User[$i]->yellow_card = 48 + $sumUser_yellow_card;

            //
            $Grade_yellow_card = Grade_yellow_card::where('qty_start', '<=', $User[$i]->yellow_card)
                ->where('qty_end', '>=', $User[$i]->yellow_card)
                ->first();

            if ($Grade_yellow_card) {
                $User[$i]->grade = $Grade_yellow_card->name;
                $User[$i]->deduct = $Grade_yellow_card->deduct;
                $User[$i]->deduct_amount = $Grade_yellow_card->deduct * $User[$i]->yellow_card;
                $User[$i]->rate_up_salary = round($Grade_yellow_card->rate_up_salary, 2);
            } else {
                $User[$i]->grade = null;
                $User[$i]->deduct = null;
                $User[$i]->rate_up_salary = null;
            }
        }

        return response()->json([
            'code' => strval(200),
            'status' => true,
            'message' => 'Successful',
            'leave_colum' => $columns,
            'data' => $User,
        ], 200);
    }



    public function ReportSalaryUser(Request $request)
    {

        // DB::beginTransaction();

        try {

            $user_id = $request->user_id;
            $round = $request->round;
            $year = $request->year;
            $month = $request->month;

            $payrollQuery = Payroll::with(['payrollRound', 'user.branch.company', 'incomePaids.income_type', 'deductPaids.deduct_type'])
                ->where('user_id', $user_id)
                ->where('year', $year)
                ->where('month', $month);

            // ครึ่งเดือน
            if ($round) {
                $payrollQuery->where('round', $round);
            }

            $Payroll = $payrollQuery->first();

            if (!$Payroll) {
                return $this->returnErrorData('ไม่พบข้อมูล', 404);
            }


            $currentDate = date('Y-m-d');

            $date_start = date('Y-m-01', strtotime("$year-$month-01"));
            $date_end = date('Y-m-t', strtotime("$year-$month-01"));

            if ($round) {
                $PayrollRound =  PayrollRound::where('round', $round)->first();

                $start_day = $PayrollRound->start_day;
                $end_day = $PayrollRound->end_day;

                $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
                $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

            } else {

                // ไม่มี round = จ่ายเต็มเดือน
                $date_start = Carbon::create($year, $month, 1)->toDateString();         // วันที่ 1 ของเดือน
                $date_end = Carbon::create($year, $month)->endOfMonth()->toDateString(); // วันสุดท้ายของเดือน

            }

            // $calculatePerday = User_attendance::where('user_id', $user_id)
            //     ->whereIn('type', ['normal', 'late'])
            //     ->whereBetween('date', [$date_start, $date_end])
            //     ->count();

            // if ($Payroll->user->work_type == 'day') {
            //     $Payroll->dayPerWork = $calculatePerday;
            //     $Payroll->salary = $Payroll->user->salary * $calculatePerday;
            // } else {
            //     $Payroll->dayPerWork = $calculatePerday;
            //     $Payroll->salary = $Payroll->user->salary;
            // }


            // สายทั้งหมด
            $latetimein = User_attendance::where('user_id', $user_id)
                ->with('zk_time')
                ->where('type', '=', 'late')
                ->whereBetween('date', [$date_start, $date_end])
                ->get()
                ->toArray();

            // เวลาเข้างานของผู้ใช้
            $worktime = Work_shift_time::where('work_shift_id', $Payroll->user->work_shift_id)->first()->value('time_in');

            // เวลามาสายทั้งหมด
            $total_minOflate = 0;
            for ($j = 0; $j < count($latetimein); $j++) {
                $latetime =  date('H:i', strtotime($latetimein[$j]['zk_time']['time']));

                $total_minOflate += abs(strtotime($latetime) - strtotime($worktime)) / 60;
            }

            // ค่าแรงต่อนาที
            if ($Payroll->user->work_type == 'day') {
                $salaryPerMin = round(($Payroll->user->salary / 8) / 60, 2);
            } else {
                $salaryPerMin = round(($Payroll->user->salary / 30) / 8 / 60, 2);
            }

            $Payroll->total_late_mian =  $total_minOflate;
            $Payroll->total_late = ($total_minOflate * $salaryPerMin);


            // สถานะการมองเห็น
            $type = $request->type ?? 'all'; // all เอาทุกตัว


            $arrIncome = [];
            $incomeTotalPrice = 0;


            $incomes = IncomePaid::where('user_id', $user_id)
                ->where('year', $year)
                ->where('month', $month)
                ->with('income_type')
                ->when($round, function ($query) use ($round) {
                    $query->whereHas('payroll', function ($q) use ($round) {
                        $q->where('round', $round);
                    });
                })
                ->get();


            foreach ($incomes as $income) {
                $name = $income->description;
                $price = floatval($income->price);

                $incomeTotalPrice += $income->price;

                //  type = visible กับ false ให้ไม่แสดง
                if ($type === 'visible' && !$income->income_type->view_in_slip) {
                    continue;
                }

                // แสดงเฉพาะอันที่เป็น True หรือ type = all
                if (isset($arrIncome[$name])) { // ถ้ามีชื่อนี้อยู่แล้ว ให้รวมเงินเลย
                    $arrIncome[$name]['price'] += $price;
                } else {
                    $arrIncome[$name] = [
                        'income_type_name' => $name,
                        'price' => $price,
                    ];
                }
            }

            $arrIncomes = array_values($arrIncome);

            // dd($arrIncome, $user_id, $incomeTotalPrice);



            $arrDeduct = [];
            $deductTotalPrice = 0;

            $deducts = DeductPaid::where('user_id', $user_id)
                ->where('year', $year)
                ->where('month', $month)
                ->with('deduct_type')
                // ->when($round, function ($query) use ($round) {
                //     $query->whereHas('payroll', function ($q) use ($round) {
                //         $q->where('round', $round);
                //     });
                // })
                ->get();

            foreach ($deducts as $deduct) {
                $name = $deduct->description;
                $price = floatval($deduct->price);


                $deductTotalPrice += $deduct->price;

                //  type = visible กับ false ให้ไม่แสดง
                if ($type === 'visible' && !$deduct->deduct_type->view_in_slip) {
                    continue;
                }


                // แสดงเฉพาะอันที่เป็น True หรือ type = all
                if (isset($arrDeduct[$name])) { // ถ้ามีชื่อนี้อยู่แล้ว ให้รวมเงินเลย
                    $arrDeduct[$name]['price'] += $price;
                } else {
                    $arrDeduct[$name] = [
                        'deduct_type_name' => $name,
                        'price' => $price,
                    ];
                }
            }
            $arrDeducts = array_values($arrDeduct);

            // dd($arrDeducts, $user_id, $deductTotalPrice);


            //Check User has Deduct?
            $deduct_user = UserDeduct::select()
                ->where('user_id', $Payroll->user->id)
                ->first();

            if ($deduct_user) {
                $deduct_tax = UserDeduct::where('user_id', $Payroll->user->id)
                    ->where('deduct_type_id', 22)
                    ->first();

                if ($deduct_tax) {
                    $Payroll->total_tax = ($Payroll->user->salary * ($deduct_tax->rate / 100));
                } else {
                    $Payroll->total_tax = 0;
                }
                $deduct_insure = UserDeduct::where('user_id', $Payroll->user->id)
                    ->where('deduct_type_id', 21)
                    ->first();

                if ($deduct_insure) {
                    $Payroll->total_insure = ($Payroll->user->salary * ($deduct_insure->rate / 100));
                } else {
                    $Payroll->total_insure = 0;
                }
            }


            ////////////////////////////// เงินสะสม //////////////////////////////
            $sso_total = 0;
            $fund_total = 0;

            // เงินประกันสังคม
            $sso_total = PayrollContribution::where('user_id', $user_id)
                ->where('code_contribution', 'sso')
                ->sum('employee_total');

            // $sso_total = DeductPaid::where('user_id', $user_id)
            //     ->whereHas('deduct_type', function ($q) {
            //         $q->where('name', 'เงินประกันสังคม');
            //     })
            //     ->sum('price');


            // กองทุนสะสมของ user
            $fund_total = PayrollContribution::where('user_id', $user_id)
                ->where('code_contribution', 'pvd')
                ->sum('employee_total');



            // $fund_total = DeductPaid::where('user_id', $user_id)
            //     ->whereHas('deduct_type', function ($q) {
            //         $q->where('name', 'กองทุน');
            //     })
            //     ->sum('price');


            // dd($sso_total, $fund_total);

            ////////////////////////////////////////////////////////////

            //pay status
            $Employee_salary = Payroll::where('id', $Payroll->id)
                ->first();

            if ($Employee_salary) {
                $Payroll->pay_status = true;
            } else {
                $Payroll->pay_status = false;
            }


            $prefix = !empty($Payroll->user->prefix) ? $Payroll->user->prefix : null;
            $name = (!empty($Payroll->user->first_name) && !empty($Payroll->user->last_name)) ? $Payroll->user->first_name . ' ' . $Payroll->user->last_name : null;
            $position = !empty($Payroll->user->position->name) ? $Payroll->user->position->name : null;
            $email = !empty($Payroll->user->email) ? $Payroll->user->email : null;
            $branch = !empty($Payroll->user->branch->name) ? $Payroll->user->branch->name : null;
            $email = (!empty($Payroll->user->email) ? $Payroll->user->email : null);
            $account_no = (!empty($Payroll->user->account_no) ? $Payroll->user->account_no : null);
            $account_name = (!empty($Payroll->user->account_name) ? $Payroll->user->account_name : null);
            $bank_name = (!empty($Payroll->user->bank_name) ? $Payroll->user->bank_name : null);
            $salary = (!empty($Payroll->user->salary) ? number_format($Payroll->user->salary, 2)  : null);
            $account_no = (!empty($Payroll->user->account_no) ? $Payroll->user->account_no : null);
            $personnel_id = (!empty($Payroll->user->personnel_id) ? $Payroll->user->personnel_id : null);

            $period = $month . '/' . ($year + 543);
            $today = date("d/m/Y, H:i:s", strtotime("+543 years"));

            $Company = Company::where('id', $Payroll->user->branch->company_id)->first();
            // 116 ม.3 ตำบล ศรีษะทอง อำเภอนครชัยศรี นครปฐม 73120

            $roundName = null;
            if ($round) {
                $roundName =  'รอบที่ ' . ($Payroll->payrollRound->round) . '   ' . ($Payroll->payrollRound->name) . ' ' . $this->dateThaiAbb($year . '-' . $month);
            } else {
                $roundName =  $this->dateThaiAbb($year . '-' . $month);
            }

            // <div style="position: absolute; top: 24px; left: 29px; font-size: 17px;"> <img src="' . ($Company->image) . '" width="120px" height="auto"> </div>
            $content = '
                <div style="text-align: left; font-size: 24px; padding-left: 100px;"> <b>ใบแจ้งเงินเดือนและเงินเพิ่มอื่นๆ </div>
                <div style="text-align: left; font-size: 18px; padding-left: 100px;"><b>' . $Company->name . ' ' . $Company->address . '</div>

                <div style="position:absolute; top: 150; left: 60px; text-align : left; width: 550px; font-size: 18px;"> ชื่อ - นามสกุล : </div>
                <div style="position:absolute; top: 150; left: 155px; text-align : left; width: 550px; font-size: 18px;"> ' . $prefix . $name . ' </div>

                <div style="position:absolute; top: 180; left: 60px; text-align : left; width: 550px; font-size: 18px;"> ประจำเดือน : </div>
                <div style="position:absolute; top: 180; left: 155px; text-align : left; width: 550px; font-size: 18px;"> ' . $period . '</div>

                <div style="position:absolute; top: 210; left: 60px; text-align : left; width: 550px; font-size: 18px;"> โอนเงินเข้า : </div>
                <div style="position:absolute; top: 210; left: 155px; text-align : left; width: 550px; font-size: 18px;">' . $account_name . ' </div>

                <div style="position:absolute; top: 240; left: 60px; text-align : left; width: 550px; font-size: 18px;"> บัญชีเลขที่ : </div>
                <div style="position:absolute; top: 240; left: 155px; text-align : left; width: 550px; font-size: 18px;"> ' . $account_no . ' ( ' . $bank_name . ' )' . ' </div>



                <div style="position:absolute; top: 150; left: 430px; text-align : left; width: 250px; font-size: 18px;">ตำแหน่ง : </div>
                <div style="position:absolute; top: 150; left: 540px; text-align : left; width: 250px; font-size: 18px;"> ' . $position . ' </div>

                <div style="position:absolute; top: 180; left: 430px; text-align : left; width: 250px; font-size: 18px;"> รหัสพนักงาน : </div>
                <div style="position:absolute; top: 180; left: 540px; text-align : left; width: 250px; font-size: 18px;"> ' . $personnel_id . ' </div>

                <div style="position:absolute; top: 210; left: 430px; text-align : left; width: 250px; font-size: 18px;"> อัตราเงินเดือน/จ้าง : </div>
                <div style="position:absolute; top: 210; left: 540px; text-align : left; width: 250px; font-size: 18px;"> ' . $salary . ' </div>

                <div style="position:absolute; top: 240; left: 430px; text-align : left; width: 250px; font-size: 18px;"> รอบการจ่าย : </div>
                <div style="position:absolute; top: 240; left: 540px; text-align : left; width: 250px; font-size: 18px;"> ' .  $roundName . ' </div>

                <style>
                    table {
                        width: 325px;
                        border-collapse: collapse;
                        border: 1px solid black;
                    }
                    th {
                        border: 1px solid black;
                        font-size: 17px;
                    }
                    td {
                        padding: 4px;
                        border: 1px solid black;
                        font-size: 17px;
                    }
                </style>

                <div style="position:absolute; top: 275; left: 55px;">
                    <table>
                        <tr>
                            <th colspan="2"> รายการรับ </th>
                        </tr>
                        ';


            $_salary = 0.00;
            $_ot = 0.00;
            $_total_commission = 0.00;
            $_total = 0.00;

            if ($Payroll) {


                $payrollQueryAllMonth = Payroll::with(['payrollRound', 'user.branch.company', 'incomePaids.income_type', 'deductPaids.deduct_type'])
                    ->where('user_id', $user_id)
                    ->where('year', $year)
                    ->where('month', $month);

                // ครึ่งเดือน
                if ($round) {
                    $payrollQueryAllMonth->where('round', $round);
                }

                $PayrollAllMonth = $payrollQueryAllMonth->get();

                for ($i = 0; $i < count($PayrollAllMonth); $i++) {
                    $_salary += $PayrollAllMonth[$i]->salary;
                    $_ot += $PayrollAllMonth[$i]->ot;
                    $_total_commission += $PayrollAllMonth[$i]->total_commission;
                    $_total += $PayrollAllMonth[$i]->total;
                }

                $content .= '
                    <tr>
                        <td style="border: none; font-size:17px;"> เงินเดือน </td>
                        <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($_salary, 2) . ' บาท &nbsp;</td>
                    </tr>
                     <tr>
                        <td style="border: none; font-size:17px;"> ค่าคอมมิชชั่น </td>
                        <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($_total_commission, 2) . ' บาท &nbsp;</td>
                    </tr>
                      <tr>
                        <td style="border: none; font-size:17px;"> ค่า OT </td>
                        <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($_ot, 2) . ' บาท &nbsp;</td>
                    </tr>
                    ';

                $incomes = $arrIncomes;
                $maxRows = 14;

                for ($i = 0; $i < $maxRows; $i++) {
                    if (isset($incomes[$i])) {
                        $income = $incomes[$i];

                        $content .= '
                                <tr>
                                    <td style="border: none; font-size:17px;"> ' . $income['income_type_name'] . ' </td>
                                    <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($income['price'], 2) . ' บาท &nbsp;</td>
                                </tr>
                            ';
                    } else {
                        // แถวว่างเมื่อข้อมูลน้อยกว่า 10 แถว
                        $content .= '
                                <tr>
                                    <td style="border: none; font-size:17px;">&nbsp;</td>
                                    <td style="border: none; font-size:17px; text-align: right;">&nbsp;</td>
                                </tr>
                            ';
                    }
                }


                $content .= '
                    </table>
                </div>
                <div style="position:absolute; top: 275; left: 415px;">
                    <table>
                        <tr>
                            <th colspan="2"> รายการหัก </th>
                        </tr>
                        ';

                $PayrollContributionSetting = PayrollContributionSetting::where('status', 1)
                    ->where('branch_id', $Payroll->user->branch->id)
                    ->get();

                $contribution_total = 0;
                for ($i = 0; $i < count($PayrollContributionSetting); $i++) {

                    //เงินประกันสังคม กองทุนสะสมของ
                    $current_total = PayrollContribution::where('user_id', $user_id)
                        // ->where('payroll_id', $Payroll->id)
                        ->WhereHas('payroll', function ($query) use ($year, $month) {
                            $query->where('year', $year);
                            $query->where('month', $month);
                        })
                        ->where('code_contribution', $PayrollContributionSetting[$i]->code)
                        ->sum('employee_total');

                    if ($round == '1' && $PayrollContributionSetting[$i]->code == 'sso') {
                        $current_total = 0;
                    }

                    $contribution_total +=  $current_total;

                    $content .= '
                        <tr>
                            <td style="border: none; font-size:17px;"> ' . $PayrollContributionSetting[$i]->name . ' </td>
                            <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($current_total, 2) . ' บาท &nbsp;</td>
                        </tr>
                        ';
                }


                $content .= '
                        <tr>
                            <td style="border: none; font-size:17px;"> ' . 'หักเบิก' . ' </td>
                            <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($Payroll->total_withdraw_salary, 2) . ' บาท &nbsp;</td>
                        </tr>
                        ';
            }

            $deducts = $arrDeducts;
            $maxRows = 16 - count($PayrollContributionSetting);

            //หักเงินกู้
            $loanPayment = LoanPayment::whereHas('loan', fn($query) => $query->where('user_id', $user_id))
                ->whereYear('paid_date', $year)
                ->whereMonth('paid_date', $month)
                ->first();

            if (isset($loanPayment)) {
                $deductTotalPrice += $loanPayment->total_paid;
                $content .= '
                        <tr>
                            <td style="border: none; font-size:17px;"> ผ่อนชำระเงินกู้งวดที่ ' . $loanPayment->installment_no . ' </td>
                            <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($loanPayment->total_paid, 2) . ' บาท &nbsp;</td>
                        </tr>
                    ';
                $maxRows--;
            }

            for ($i = 0; $i < $maxRows; $i++) {
                if (isset($deducts[$i])) {
                    $deduct = $deducts[$i];
                    $content .= '
                        <tr>
                            <td style="border: none; font-size:17px;"> ' . $deduct['deduct_type_name'] . ' </td>
                            <td style="border: none; font-size:17px; text-align: right;"> ' . number_format($deduct['price'], 2) . ' บาท &nbsp;</td>
                        </tr>
                    ';
                } else {
                    // แถวว่างเมื่อข้อมูลน้อยกว่า 10 แถว
                    $content .= '
                        <tr>
                            <td style="border: none; font-size:17px;">&nbsp;</td>
                            <td style="border: none; font-size:17px; text-align: right;">&nbsp;</td>
                        </tr>
                    ';
                }
            }

            // $total_gov = $total_income - $total_deduct;

            $toal_income = $incomeTotalPrice + $_salary;
            $toal_deduct = $deductTotalPrice + $contribution_total;

            $content .= '
                    </table>
                </div>

                <div style="position: absolute; top: 835px; left:150px; font-size: 17px;">  </div>
                <div style="position: absolute; top: 900px; left:110px; font-size: 17px;"> ลงชื่อ ................................................................ </div>
                <div style="position: absolute; top: 922px; left:37px; font-size: 17px; text-align: center; width: 350px;"> (&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;) </div>
                <div style="position: absolute; top: 944px; left:37px; font-size: 17px; text-align: center; width: 350px;"> หัวหน้าการเงิน </div>

                 <!-- <div style="position: absolute; top: 810px; left:440px; font-size: 17px;">เงินประกันสังคมสะสม</div> -->
                 <!-- <div style="position: absolute; top: 830px; left:440px; font-size: 17px;">กองทุนสะสม</div> -->

                <!-- เส้น -->
                <div style="position: absolute; top: 860px; left:440px; width: 280px; height: 1px; background-color: #000;"></div>

                <div style="position: absolute; top: 880px; left:440px; font-size: 17px;"><b>รวมรับทั้งหมด</b></div>
                <div style="position: absolute; top: 900px; left:440px; font-size: 17px;"><b>รวมหักทั้งหมด</b></div>
                <div style="position: absolute; top: 920px; left:440px; font-size: 17px;"><b>รับสุทธิ</b></div>

                 <!-- <div style="position: absolute; top: 810px; left:460px; font-size: 17px; width: 250px; text-align: right;"> ' . (!empty($sso_total) ? number_format($sso_total, 2) : 0) . ' บาท </div> -->
                 <!-- <div style="position: absolute; top: 830px; left:460px; font-size: 17px; width: 250px; text-align: right;"> ' . (!empty($fund_total) ? number_format($fund_total, 2) : 0) . ' บาท </div> -->
                <div style="position: absolute; top: 880px; left:460px; font-size: 17px; width: 250px; text-align: right;"> ' . (!empty($toal_income) ? number_format($toal_income, 2) : 0) . ' บาท </div>
                <div style="position: absolute; top: 900px; left:460px; font-size: 17px; width: 250px; text-align: right;"> ' . (!empty($toal_deduct) ? number_format(($toal_deduct), 2) : 0) . ' บาท </div>
                <div style="position: absolute; top: 920px; left:460px; font-size: 17px; width: 250px; text-align: right;"> ' . (!empty($_total) ? number_format(($_total), 2) : 0) . ' บาท </div>


                <div style="position: absolute; top: 990px; left:438px; font-size: 15px; text-align:right; width: 300px;"> วันที่ออกหนังสือรับรอง  ' . $today  . '</div>

                <div style="position: absolute; top: 990px; left:55px; font-size: 15px; text-align:left; width: 600px;"> </div>
                ';

            //PDF
            $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
            $fontDirs = $defaultConfig['fontDir'];
            $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
            $fontData = $defaultFontConfig['fontdata'];
            $mpdf = new \Mpdf\Mpdf([
                'fontDir' => array_merge($fontDirs, [
                    base_path() . '/custom/font/directory',
                ]),
                'fontdata' => $fontData + [ // lowercase letters only in font key
                    'th-sarabun-it' => [
                        'R' => 'THSarabunIT๙.ttf',
                        'I' => 'THSarabunIT๙ Italic.ttf',
                        'B' => 'THSarabunIT๙ Bold.ttf',
                        'BI' => 'THSarabunIT๙ BoldItalic.ttf',
                    ],
                    'th-sarabun' => [
                        'R' => 'THSarabun.ttf',
                        'I' => 'THSarabun Italic.ttf',
                        'B' => 'THSarabun Bold.ttf',
                        'BI' => 'THSarabun BoldItalic.ttf',
                    ],
                ],
                'default_font' => 'th-sarabun',
                'mode' => 'utf-8',
                'format' => 'A4',
                // 'default_font_size' => 12,
                // 'default_font' => 'sarabun',
                // 'margin_left' => 5,
                // 'margin_right' => 5,
                // 'margin_top' => 5,
                // 'margin_bottom' => 5,
                // 'margin_header' => 5,
                // 'margin_footer' => 5,
            ]);

            //add password
            $password = '@erpgs2025';
            $User = User::find($user_id);
            if ($User) {
                $password = '1234';
            }
            //

            $userPassword = $password; // รหัสผ่านสำหรับเปิดไฟล์
            $ownerPassword = '@erpgs2025'; // รหัสผ่านสำหรับการจัดการ (Admin)
            $permissions = ['print', 'copy']; // สิทธิ์ที่อนุญาต: พิมพ์, คัดลอก

            $mpdf->SetProtection($permissions, $userPassword, $ownerPassword);

            $mpdf->SetTitle('รายละเอียดเงินเดือน');
            $mpdf->AddPage();
            $mpdf->WriteHTML($content);
            $mpdf->Output('รายละเอียดเงินเดือน.pdf', 'I');
        } catch (\Throwable $e) {

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }


    //รายงานรายวัน
    public function Report_DailyReport(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');
        $user_id = $request->query('user_id', null);
        $type = $request->query('type', null);

        // ตรวจสอบค่าที่จำเป็น
        if (!$date_start || !$date_end) {
            return response()->json(['status' => 'error', 'message' => 'กรุณาระบุ date_start และ date_end'], 400);
        }

        // กำหนดช่วงวันที่
        $dateStartFull = date('Y-m-d', strtotime($date_start)) . ' 00:00:00';
        $dateEndFull = date('Y-m-d', strtotime($date_end)) . ' 23:59:59';


        // ดึงข้อมูลผู้ใช้งาน
        $userQuery = User::with('branch', 'department', 'position');
        if ($user_id) {
            $userQuery->where('id', $user_id);
        }
        $users = $userQuery->get();

        if ($users->isEmpty()) {
            return response()->json(['status' => 'error', 'message' => 'ไม่พบข้อมูลผู้ใช้งาน'], 404);
        }

        // ดึงข้อมูลการเข้าร่วมงานทั้งหมดในช่วงวันที่
        $attendanceQuery = User_attendance::whereBetween('date', [$dateStartFull, $dateEndFull]);
        if ($user_id) {
            $attendanceQuery->where('user_id', $user_id);
        }
        if ($type) {
            $attendanceQuery->where('type', $type);
        }
        $attendance = $attendanceQuery->get();

        // จัดเก็บข้อมูลการเข้าร่วมงานโดยใช้วันที่เป็นคีย์
        $attendanceRecordsByDate = $attendance->groupBy('user_id')->map(function ($records) {
            return $records->keyBy('date');
        });

        $holidays = Holiday::pluck('date')->toArray();

        // ดึงข้อมูลเวลาการทำงานทั้งหมดในช่วงวันที่
        $workTimesInOutQuery = Zk_time::whereBetween('time', ["{$dateStartFull}", "{$dateEndFull}"])
            ->whereNotIn(DB::raw('DATE(time)'), $holidays);

        if ($user_id) {
            $workTimesInOutQuery->where('personnel_id', function ($query) use ($user_id) {
                $query->selectRaw("CONVERT(`personnel_id` USING utf8mb4) COLLATE utf8mb4_unicode_ci")
                    ->from('users')
                    ->where('id', $user_id);
            });
        }
        $workTimesInOut = $workTimesInOutQuery->get();

        // จัดกลุ่มข้อมูลเวลาการทำงานตามวันที่และผู้ใช้
        $workTimesByDate = $workTimesInOut->groupBy(function ($record) {
            return $record->personnel_id;
        })->map(function ($records) {
            return $records->groupBy(function ($record) {
                return Carbon::parse($record->time)->toDateString();
            });
        });

        // สร้างช่วงวันที่
        $period = CarbonPeriod::create($dateStartFull, $dateEndFull);

        // เตรียมข้อมูลสำหรับรายงาน
        $reportData = [];
        foreach ($users as $user) {
            foreach ($period as $dateObj) {
                $date = $dateObj->toDateString();

                // ดึงข้อมูลการเข้าร่วมงานของวันที่นี้สำหรับผู้ใช้
                $attendanceRecord = $attendanceRecordsByDate->get($user->id)?->get($date);

                // ถ้ามีการกำหนด type และ type ของ attendance ไม่ตรงกัน ให้ข้ามวันที่นี้
                if ($type && (!$attendanceRecord || $attendanceRecord->type !== $type)) {
                    continue;
                }

                // ดึงข้อมูลเวลาการทำงานของวันที่นี้สำหรับผู้ใช้
                $records = $workTimesByDate->get($user->personnel_id)?->get($date, collect());
                $workShift = Work_shift_time::where('work_shift_id', $user->work_shift_id)->first();
                if ($records) {
                    if ($records->count() == 1) {
                        if ($workShift) {
                            $_time = $records->first()->time;

                            $firstCheckTime = strtotime($_time);
                            $shiftStart = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_in_start);
                            $shiftEnd = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_out_end);


                            if ($firstCheckTime >= $shiftStart && $firstCheckTime <= $shiftEnd) {
                                // อยู่ในช่วงเวลางาน -> แสดงเป็นเวลาเข้า
                                $timeIn = date('H:i', $firstCheckTime);
                                $timeOut = null;
                            } else {
                                // อยู่นอกช่วงเวลางาน -> แสดงเป็นเวลาออก
                                $timeIn = null;
                                $timeOut = date('H:i', $firstCheckTime);
                            }
                        }
                    } else { // กรณีมีบันทึกเวลามากกว่า 1 ครั้ง
                        $timeIn = $records->min('time') ? date('H:i', strtotime($records->min('time'))) : null;
                        $timeOut = $records->max('time') ? date('H:i', strtotime($records->max('time'))) : null;
                    }
                } else {
                    $timeIn = null;
                    $timeOut = null;
                }

                $hour_work = $timeIn && $timeOut ? $this->TimeDiff($timeIn, $timeOut) : '-';
                $status = $attendanceRecord ? $attendanceRecord->type : 'ไม่มีข้อมูล';

                $reportData[] = [
                    'id' => $user->user_id,
                    'user' => $user,
                    'date' => $date,
                    'time_in' => $timeIn,
                    'time_out' => $timeOut,
                    'hour_work' => $hour_work,
                    'status' => $status,
                ];
            }
        }

        // $ot = Ot::whereDate('date', $date)->get();
        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $mpdf = new \Mpdf\Mpdf([
            'fontDir' => array_merge($fontDirs, [
                base_path() . '/custom/font/directory',
            ]),
            'fontdata' => $fontData + [
                'th-sarabun' => [
                    'R' => 'THSarabun.ttf',
                    'I' => 'THSarabun Italic.ttf',
                    'B' => 'THSarabun Bold.ttf',
                    'BI' => 'THSarabun Bold Italic.ttf',
                ],
            ],
            'default_font' => 'th-sarabun',
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 12,
        ]);

        $mpdf->SetTitle('รายงาน รายวัน');
        $counter = 1;
        $itemPerPage = 45;
        $totalItem = count($attendance);
        $Pages = ceil($totalItem / $itemPerPage);

        for ($Page = 0; $Page < $Pages; $Page++) {
            if ($Page > 0) {
                // $mpdf->AddPage('L); สำหรับกระดาษแนวนอน
                $mpdf->AddPage();
            }

            $html = '
        <style>
            div {
                font-weight: bold;
            }

            table {
                border: 0.1px solid black;
                border-collapse: collapse;
                width: 100%;
            }

            th, td {
                border: 0.1px solid black;
                font-size: 13.5px;
                font-weight: bold;
            }

            td {
                padding-left: 5px;
                padding-top: 1.5px;
                padding-bottom: 1.5px;
            }

            th {
                padding: 3px;
            }

            tr #center {
                text-align: center;
            }

        </style>

            รายงานรายวัน <br>
            ชื่อสาขา: ทุกสาขา
        </div>
        <div style="position: absolute; top: 70px; left: 525px; font-size: 15px; line-height: 0.8;">
            วันออกรายงาน <br>
            <br>
            <br>
            รายงาน รายวัน
        </div>
        <div style="position: absolute; top: 70px; left: 625px; font-size: 15px; line-height: 0.8;">
            : ' . date('d') . ' ' . $this->getThaiMonth(date('m')) . ' ' . (date('Y') + 543) . ' <br>
            ' .  date('H:i') . '
            <br>
            <br>
            ';
            $day_start = date('d', strtotime($date_start));
            $day_end = date('d', strtotime($date_end));
            $month_start = date('m', strtotime($date_start));
            $month_end = date('m', strtotime($date_end));
            $year_start = date('Y', strtotime($date_start));
            $year_end = date('Y', strtotime($date_end));

            if ($month_start !== $month_end || $year_start !== $year_end) {
                // กรณีที่ช่วงวันที่ครอบคลุมหลายเดือนหรือหลายปี
                $html .= '
                    : ' . $day_start . ' ' . $this->getThaiMonth($month_start) . ' ' . ($year_start + 543) . ' - '
                    . $day_end . ' ' . $this->getThaiMonth($month_end) . ' ' . ($year_end + 543);
            } else {
                // กรณีที่อยู่ในเดือนและปีเดียวกัน
                $html .= '
                    : ' . $day_start . ' - ' . $day_end . ' ' . $this->getThaiMonth($month_start) . ' ' . ($year_start + 543);
            }
            $html .= '
        </div>
        <div style="position: absolute; top: 145px; left: 325px; font-size: 25px; line-height: 1;">
            รายงาน รายวัน
        </div>

        <div style="position: absolute; top: 180px; left: 40px; height: 100%; width: 90%;">

            <table>
                <tr style="background-color: #D3D3D3;">
                    <th width="5%"> </th>
                    <th width="10%">รหัสพนักงาน</th>
                    <th width="20%">ชื่อ</th>
                    <th width="10%">ชื่อสาขา</th>
                    <th width="10%">แผนก</th>
                    <th width="10%">วันที่</th>
                    <th width="7%">สถานะ</th>
                    <th width="7%">เวลาเข้า</th>
                    <th width="7%">เวลาออก</th>
                    <th width="7%">ชั่วโมงงาน</th>
                    <th width="7%">โอที</th>
                </tr>
                ';

            for ($i = $Page * $itemPerPage; $i < min(($Page + 1) * $itemPerPage, $totalItem); $i++) {
                if (!isset($reportData[$i])) {
                    continue;
                }
                $data = $reportData[$i];
                $status = $data['status'];
                $statusDisplay = match ($status) {
                    'normal' => '<span style="color: green;">ปกติ</span>',
                    'miss' => '<span style="color: red;">ขาด</span>',
                    'late' => '<span style="color: orange;">สาย</span>',
                    'leave' => '<span style="color: blue;">ลา</span>',
                    'off' => '<span style="color: gray;">หยุด</span>',
                    default => '<span style="color: black;">' . $status . '</span>',
                };
                $html .= '
                        <tr>
                            <td>' . $counter++ . '</td>
                            <td>' . $data['id'] . '</td>
                            <td>' . $data['user']->first_name . ' ' . $data['user']->last_name . '</td>
                            <td>' . $data['user']->branch->name . '</td>
                            <td>' . $data['user']->position->name . '</td>
                            <td id="center">' . date('d/m/Y', strtotime($data['date'])) . '</td>
                            <td id="center">' . $statusDisplay . '</td>
                            <td id="center">' . ($data['time_in'] ? date('H:i', strtotime($data['time_in'])) : '-') . '</td>
                            <td id="center">' . ($data['time_out'] ? date('H:i', strtotime($data['time_out'])) : '-') . '</td>
                            <td id="center">' . $data['hour_work'] . '</td>
                            <td id="center"> - </td>
                        </tr>
                    ';
            }

            $html .= '
            </table>
        </div>

        ';

            $mpdf->WriteHTML($html);
        }

        $mpdf->Output();
    }

    public function Export_DailyReport_Excel(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');
        $user_id = $request->query('user_id');
        $type = $request->query('type', null);

        // ตรวจสอบค่าที่จำเป็น
        if (!$date_start || !$date_end) {
            return response()->json(['status' => 'error', 'message' => 'โปรดระบุ date_start และ date_end'], 400);
        }

        $dateStartFull = date('Y-m-d', strtotime($date_start)) . ' 00:00:00';
        $dateEndFull = date('Y-m-d', strtotime($date_end)) . ' 23:59:59';

        // ดึงข้อมูลผู้ใช้งาน
        $userQuery = User::with('branch', 'department', 'position');
        if ($user_id) {
            $userQuery->where('id', $user_id);
        }
        $users = $userQuery->get();

        if ($users->isEmpty()) {
            return response()->json(['status' => 'error', 'message' => 'ไม่พบข้อมูลผู้ใช้งาน'], 404);
        }

        // ดึงข้อมูลการเข้าร่วมงาน
        $attendanceQuery = User_attendance::whereBetween('date', [$dateStartFull, $dateEndFull]);
        if ($user_id) {
            $attendanceQuery->where('user_id', $user_id);
        }
        if ($type) {
            $attendanceQuery->where('type', $type);
        }
        $attendance = $attendanceQuery->get();

        // จัดเก็บข้อมูลการเข้าร่วมงานโดยใช้วันที่เป็นคีย์
        $attendanceRecordsByDate = $attendance->groupBy('user_id')->map(function ($records) {
            return $records->keyBy('date');
        });

        $holidays = Holiday::pluck('date')->toArray();

        // ดึงข้อมูลเวลาการทำงาน
        $workTimesInOutQuery = Zk_time::whereBetween('time', ["{$dateStartFull}", "{$dateEndFull}"])
            ->whereNotIn(DB::raw('DATE(time)'), $holidays);

        if ($user_id) {
            $workTimesInOutQuery->where('personnel_id', function ($query) use ($user_id) {
                $query->selectRaw("CONVERT(`personnel_id` USING utf8mb4) COLLATE utf8mb4_unicode_ci")
                    ->from('users')
                    ->where('id', $user_id);
            });
        }
        $workTimesInOut = $workTimesInOutQuery->get();

        // จัดกลุ่มข้อมูลเวลาการทำงานตามวันที่และผู้ใช้
        $workTimesByDate = $workTimesInOut->groupBy(function ($record) {
            return $record->personnel_id;
        })->map(function ($records) {
            return $records->groupBy(function ($record) {
                return Carbon::parse($record->time)->toDateString();
            });
        });

        // สร้างข้อมูลสำหรับ Excel
        $counter = 1;
        $reportData = [];
        foreach ($users as $user) {
            foreach (CarbonPeriod::create($date_start, $date_end) as $dateObj) {
                $date = $dateObj->toDateString();

                // ดึงข้อมูลการเข้าร่วมงานของวันที่นี้
                $attendanceRecord = $attendanceRecordsByDate->get($user->id)?->get($date);

                // ถ้ามีการกำหนด type และ type ของ attendance ไม่ตรงกัน ให้ข้ามวันที่นี้
                if ($type && (!$attendanceRecord || $attendanceRecord->type !== $type)) {
                    continue;
                }
                $records = $workTimesByDate->get($user->personnel_id)?->get($date, collect());
                $workShift = Work_shift_time::where('work_shift_id', $user->work_shift_id)->first();
                if ($records->count() == 1) {
                    if ($workShift) {
                        $_time = $records->first()->time;

                        $firstCheckTime = strtotime($_time);
                        $shiftStart = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_in_start);
                        $shiftEnd = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_out_end);


                        if ($firstCheckTime >= $shiftStart && $firstCheckTime <= $shiftEnd) {
                            // อยู่ในช่วงเวลางาน -> แสดงเป็นเวลาเข้า
                            $timeIn = date('H:i', $firstCheckTime);
                            $timeOut = null;
                        } else {
                            // อยู่นอกช่วงเวลางาน -> แสดงเป็นเวลาออก
                            $timeIn = null;
                            $timeOut = date('H:i', $firstCheckTime);
                        }
                    }
                } else { // กรณีมีบันทึกเวลามากกว่า 1 ครั้ง
                    $timeIn = $records->min('time') ? date('H:i', strtotime($records->min('time'))) : null;
                    $timeOut = $records->max('time') ? date('H:i', strtotime($records->max('time'))) : null;
                }

                $status = match ($attendanceRecord->type ?? null) {
                    'normal' => 'ปกติ',
                    'miss' => 'ขาด',
                    'late' => 'สาย',
                    'leave' => 'ลา',
                    'off' => 'หยุด',
                    default => 'ไม่มีข้อมูล',
                };

                $hour_work = $timeIn && $timeOut ? $this->TimeDiff($timeIn, $timeOut) : '-';

                // ไม่ได้ใช้งาน
                $ot_hours = 0;

                $reportData[] = [
                    '#' => $counter++,
                    'รหัสพนักงาน' => $user->personnel_id,
                    'ชื่อ' => $user->first_name . ' ' . $user->last_name,
                    'สาขา' => $user->branch->name,
                    'แผนก' => $user->position->name,
                    'วันที่' => $date,
                    'สถานะ' => $status,
                    'เวลาเข้า' => $timeIn ? date('H:i:s', strtotime($timeIn)) : '-',
                    'เวลาออก' => $timeOut ? date('H:i:s', strtotime($timeOut)) : '-',
                    'ชั่วโมงงาน' => $hour_work !== '-' ? sprintf('%0.2f', $hour_work) : '-',
                    'โอที' => $ot_hours > 0 ? sprintf('%0.2f', $ot_hours) : '-',
                ];
            }
        }

        return Excel::download(new class($reportData) implements FromArray, WithHeadings, WithColumnWidths {
            private $data;

            public function __construct(array $data)
            {
                $this->data = $data;
            }
            public function array(): array
            {
                return $this->data;
            }
            public function headings(): array
            {
                return [
                    '#',
                    'รหัสพนักงาน',
                    'ชื่อ',
                    'สาขา',
                    'แผนก',
                    'วันที่',
                    'สถานะ',
                    'เวลาเข้า',
                    'เวลาออก',
                    'ชั่วโมงงาน',
                    'โอที',
                ];
            }

            public function columnWidths(): array
            {
                return [
                    'A' => 5,
                    'B' => 15,
                    'C' => 30,
                    'D' => 15,
                    'E' => 15,
                    'F' => 15,
                    'G' => 15,
                    'H' => 15,
                    'I' => 15,
                    'J' => 15,
                    'K' => 15,
                ];
            }
        }, 'รายงานรายวัน.xlsx');
    }

    //แปลงเดือนเป็นภาษาไทย
    private function getThaiMonth($month)
    {
        $thaiMonths = [
            "01" => "มกราคม",
            "02" => "กุมภาพันธ์",
            "03" => "มีนาคม",
            "04" => "เมษายน",
            "05" => "พฤษภาคม",
            "06" => "มิถุนายน",
            "07" => "กรกฎาคม",
            "08" => "สิงหาคม",
            "09" => "กันยายน",
            "10" => "ตุลาคม",
            "11" => "พฤศจิกายน",
            "12" => "ธันวาคม"
        ];
        return $thaiMonths[$month];
    }

    //รายงาน รายเดือน
    public function Report_MonthlyReport(Request $request)
    {
        // รับค่าจาก Query Parameters
        $user_id = $request->query('user_id'); // รับ user_id (รหัสพนักงาน เช่น GB0001)
        $round = $request->query('round'); // รอบเงินเดือน
        $year = $request->query('year'); // รับปี
        $month = $request->query('month'); // รับเดือน
        $position_id = $request->query('position_id'); // รับ position_id
        $branch_id = $request->query('branch_id'); // branch_id

        // กำหนดช่วงวันที่
        $startOfMonth = date('Y-m-01', strtotime("$year-$month-01"));
        $endOfMonth = date('Y-m-t', strtotime("$year-$month-01"));

        if ($round) {
            $PayrollRound =  PayrollRound::where('round', $round)->first();

            $start_day = $PayrollRound->start_day;
            $end_day = $PayrollRound->end_day;

            $startOfMonth = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
            $endOfMonth = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

        }

        $userQuery = User::with('branch', 'department', 'position');
        if ($user_id) {
            $userQuery->where('user_id', $user_id);
        }
        if ($position_id) {
            $userQuery->where('position_id', $position_id);
        }
        if ($branch_id) {
            $userQuery->where('branch_id', $branch_id);
        }
        $users = $userQuery->get();

        $leaveTables = Leave_table::where(function ($query) use ($startOfMonth, $endOfMonth) {
            $query->whereBetween('date_start', [$startOfMonth, $endOfMonth])
                ->orWhereBetween('date_end', [$startOfMonth, $endOfMonth])
                ->orWhere(function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where('date_start', '<=', $startOfMonth)
                        ->where('date_end', '>=', $endOfMonth);
                });
        });
        if ($user_id) {
            $leaveTables->where('user_id', $user_id);
        }
        $leaveTables = $leaveTables->get();

        $userAttendanceQuery = User_attendance::whereBetween('date', [$startOfMonth, $endOfMonth]);
        if ($user_id) {
            $userAttendanceQuery->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $userAttendance = $userAttendanceQuery->get();

        $otQuery = Ot::whereBetween('date', [$startOfMonth, $endOfMonth]);
        if ($user_id) {
            $otQuery->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $ot = $otQuery->get();

        $branch = Branch::find($branch_id);

        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $mpdf = new \Mpdf\Mpdf([
            'fontDir' => array_merge($fontDirs, [
                base_path() . '/custom/font/directory',
            ]),
            'fontdata' => $fontData + [
                'th-sarabun' => [
                    'R' => 'THSarabun.ttf',
                    'I' => 'THSarabun Italic.ttf',
                    'B' => 'THSarabun Bold.ttf',
                    'BI' => 'THSarabun Bold Italic.ttf',
                ],
            ],
            'default_font' => 'th-sarabun',
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 12,
        ]);

        $mpdf->SetTitle('รายงาน รายเดือน');

        $counter = 1;
        $itemPerPage = 44;
        $totalItem = count($users);
        $Pages = ceil($totalItem / $itemPerPage);

        for ($Page = 0; $Page < $Pages; $Page++) {
            if ($Page > 0) {
                $mpdf->AddPage();
            }

            $html = '
        <style>
            div {
                font-weight: bold;
            }

            table {
                border: 0.1px solid black;
                border-collapse: collapse;
                width: 100%;
            }

            th, td {
                border: 0.1px solid black;
                font-size: 13.5px;
                font-weight: bold;
            }

            td {
                padding-left: 5px;
                padding-top: 1.5px;
                padding-bottom: 1.5px;
            }

            th {
                padding: 3px;
            }

            #center {
                text-align: center;
            }

        </style>
            รายงาน รายเดือน <br>
            ชื่อสาขา: ' . $branch->name . '
        </div>
        <div style="position: absolute; top: 70px; left: 525px; font-size: 15px; line-height: 0.8;">
            วันออกรายงาน <br>
            <br>
            <br>
            รายงาน รายเดือน
        </div>
        <div style="position: absolute; top: 70px; left: 625px; font-size: 15px; line-height: 0.8;">
            : ' . date('d') . ' ' . $this->getThaiMonth(date('m')) . ' ' . (date('Y') + 543) . ' <br>
            ' .  date('H:i') . '
            <br>
            <br>
            : ' . $this->getThaiMonth($month) . ' ' . ($year ? $year + 543 : ' ') . '
        </div>
        <div style="position: absolute; top: 145px; left: 325px; font-size: 25px; line-height: 1;">
            MONTHLY REPORT
        </div>
        <div style="position: absolute; top: 180px; left: 40px; height: 100%; width: 90%;">
            <table>
                <tr style="background-color: #D3D3D3;">
                    <th rowspan="2"> </th>
                    <th rowspan="2">รหัสพนักงาน</th>
                    <th rowspan="2">ชื่อ</th>
                    <th rowspan="2">แผนก</th>
                    <th colspan="2">รวม</th>
                    <th colspan="4">ลา</th>
                    <th rowspan="2">% งาน</th>
                    <th colspan="3">ผลรวม</th>
                </tr>
                <tr style="background-color: #D3D3D3;">
                    <th>วันทำงาน</th>
                    <th>มาทำงาน</th>
                    <th>สาย</th>
                    <th>ขาด</th>
                    <th>ลากิจ</th>
                    <th>ลาป่วย</th>
                    <th>สาย</th>
                    <th>ออกก่อน</th>
                    <th>โอที</th>
                </tr>';
            for ($i = $Page * $itemPerPage; $i < min(($Page + 1) * $itemPerPage, $totalItem); $i++) {
                //ดึงข้อมูลเวลาทำงาน
                $attendance = $userAttendance
                    ->where('user_id', $users[$i]->id)
                    ->whereIn('type', ['normal', 'late', 'miss']);
                $attendance_records = Zk_time::where('personnel_id', $users[$i]->personnel_id)
                    ->whereBetween('time', ["{$startOfMonth} 00:00:00", "{$endOfMonth} 23:59:59"])
                    ->orderBy('time')
                    ->get();
                $workday = $attendance->count();
                $comework = $userAttendance->where('user_id', $users[$i]->id)->where('type', 'normal')->count();
                $worklate = $userAttendance->where('user_id', $users[$i]->id)->where('type', 'late')->count();
                $workmiss = $userAttendance->where('user_id', $users[$i]->id)->where('type', 'miss')->count();
                $leaveBusiness = $leaveTables->where('user_id', $users[$i]->id)->whereIn('leave_type_id', [1, 2, 5, 6])->count();
                $leaveSick = $leaveTables->where('user_id', $users[$i]->id)->whereIn('leave_type_id', [3, 4])->count();
                $percentwork = ($workday > 0) ? round(($comework / $workday) * 100, 2) : 0;

                // คำนวณเวลาสายและออกก่อนเวลาจาก work_shift_time
                $workShifts = Work_shift_time::all()->keyBy('work_shift_id');
                $workShift = $workShifts[$users[$i]->work_shift_id] ?? null;
                $totalLateSeconds = 0;
                $totalOutEarlySeconds = 0;

                if ($workShift) {

                    $attendanceByDate = $attendance_records
                        ->groupBy(function ($record) {
                            return date('Y-m-d', strtotime($record->time));
                        });
                    foreach ($attendanceByDate as $date => $records) {

                        if ($records->count() == 1) {
                            if ($workShift) {
                                $_time = $records->first()->time;

                                $firstCheckTime = strtotime($_time);
                                $shiftStart = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_in_start);
                                $shiftEnd = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_out_end);

                                if ($firstCheckTime >= $shiftStart && $firstCheckTime <= $shiftEnd) {
                                    // อยู่ในช่วงเวลางาน -> แสดงเป็นเวลาเข้า
                                    $time_in = date('H:i', $firstCheckTime);
                                    $time_out = null;
                                } else {
                                    // อยู่นอกช่วงเวลางาน -> แสดงเป็นเวลาออก
                                    $time_in = null;
                                    $time_out = date('H:i', $firstCheckTime);
                                }
                            }
                        } else { // กรณีมีบันทึกเวลามากกว่า 1 ครั้ง
                            $time_in = $records->min('time') ? date('H:i', strtotime($records->min('time'))) : null;
                            $time_out = $records->max('time') ? date('H:i', strtotime($records->max('time'))) : null;
                        }



                        if ($time_in) {
                            $time_in_end_datetime = date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_in_end;
                            $time_in_end_timestamp = strtotime($time_in_end_datetime);
                            $time_in_timestamp = strtotime($time_in);

                            //เวลาสุดท้ายที่นับว่าเปนเวลาเข้า
                            $time_out_start_timestamp = strtotime(date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_out_start);

                            if ($time_in_timestamp > $time_in_end_timestamp && $time_in_timestamp < $time_out_start_timestamp) {
                                $totalLateSeconds += $time_in_timestamp - $time_in_end_timestamp;
                            }
                        }

                        if ($time_out) {
                            $time_out_start_datetime = date('Y-m-d', strtotime($time_out)) . ' ' . $workShift->time_out_start;
                            $time_out_start_timestamp = strtotime($time_out_start_datetime);
                            $time_out_timestamp = strtotime($time_out);

                            //เวลาสุดท้ายที่นับว่าเปนเวลาเข้า
                            $time_in_end_timestamp = strtotime(date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_in_end);

                            if ($time_out_timestamp < $time_out_start_timestamp  && $time_out_timestamp > $time_in_end_timestamp) {
                                $totalOutEarlySeconds += $time_out_start_timestamp - $time_out_timestamp;
                            }
                        }
                    }
                }
                $late_display = $totalLateSeconds > 0 ? gmdate('H:i', $totalLateSeconds) : '00:00';
                $out_time_display = $totalOutEarlySeconds > 0 ? gmdate('H:i', $totalOutEarlySeconds) : '00:00';

                // คำนวณโอที
                $time_ot = $ot->where('user_id', $users[$i]->id);
                $Ot = $time_ot ? $time_ot->where('status', 'approved')->sum('qty_hour') : '-';

                $html .= '
                <tr>
                    <td id="center" width="4%">' . $counter++ . '</td>
                    <td width="7%">' . $users[$i]->user_id . '</td>
                    <td width="17%">' . $users[$i]->first_name . ' ' . $users[$i]->last_name . '</td>
                    <td width="12%">' . $users[$i]->position->name . '</td>
                    <td id="center" width="6%">' . $workday . '</td>
                    <td id="center" width="6%">' . $comework . '</td>
                    <td id="center" width="6%">' . $worklate . '</td>
                    <td id="center" width="6%">' . $workmiss . '</td>
                    <td id="center" width="6%">' . $leaveBusiness . '</td>
                    <td id="center" width="6%">' . $leaveSick . '</td>
                    <td id="center" width="6%">' . $percentwork . '</td>
                    <td id="center" width="6%">' . $late_display . '</td>
                    <td id="center" width="6%">' . $out_time_display . '</td>
                    <td id="center" width="6%">' . $Ot . '</td>
                </tr>';
            }

            $html .= '
            </table>
        </div>';
            $mpdf->WriteHTML($html);
        }

        $mpdf->Output();
    }

    public function Export_MonthlyReport_Excel(Request $request)
    {
        // รับค่าจาก Query Parameters
        $user_id = $request->query('user_id');
        $year = $request->query('year');
        $month = $request->query('month');
        $position_id = $request->query('position_id');

        //ข้อมูลการลา
        // $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth()->toDateString();
        // $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth()->toDateString();
        $startOfMonth = Carbon::create($year, $month)->subMonth()->day(26)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่ 26 ของเดือนก่อนหน้า
        $endOfMonth = Carbon::create($year, $month)->day(25)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่ 25 ของเดือนใน $round


        $userQuery = User::with('branch', 'department', 'position');
        if ($user_id) {
            $userQuery->where('user_id', $user_id);
        }
        if ($position_id) {
            $userQuery->where('position_id', $position_id);
        }
        $users = $userQuery->get();

        $leaveTables = Leave_table::where(function ($query) use ($startOfMonth, $endOfMonth) {
            $query->whereBetween('date_start', [$startOfMonth, $endOfMonth])
                ->orWhereBetween('date_end', [$startOfMonth, $endOfMonth])
                ->orWhere(function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where('date_start', '<=', $startOfMonth)
                        ->where('date_end', '>=', $endOfMonth);
                });
        });

        if ($user_id) {
            $leaveTables->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $leaveTables = $leaveTables->get();

        $userAttendanceQuery = User_attendance::whereBetween('date', [$startOfMonth, $endOfMonth]);
        if ($user_id) {
            $userAttendanceQuery->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $userAttendance = $userAttendanceQuery->get();

        $otQuery = Ot::where('date', 'like', "{$year}-{$month}-%");
        if ($user_id) {
            $otQuery->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $ot = $otQuery->get();

        // เตรียมข้อมูลสำหรับ Excel
        $data = [];
        foreach ($users as $user) {
            // ดึงข้อมูลเวลาทำงานและการเข้าออก
            $attendance = $userAttendance
                ->where('user_id', $user->id)
                ->whereIn('type', ['normal', 'late', 'miss']);
            $attendance_records = Zk_time::where('personnel_id', $user->personnel_id)
                ->whereBetween('time', ["{$startOfMonth} 00:00:00", "{$endOfMonth} 23:59:59"])
                ->orderBy('time')
                ->get();
            $workday = $attendance->count();
            $comework = $userAttendance->where('user_id', $user->id)->where('type', 'normal')->count();
            $worklate = $userAttendance->where('user_id', $user->id)->where('type', 'late')->count();
            $workmiss = $userAttendance->where('user_id', $user->id)->where('type', 'miss')->count();
            $leaveBusiness = $leaveTables->where('user_id', $user->id)->whereIn('leave_type_id', [1, 2, 5, 6])->count();
            $leaveSick = $leaveTables->where('user_id', $user->id)->whereIn('leave_type_id', [3, 4])->count();
            $percentwork = ($workday > 0) ? round(($comework / $workday) * 100, 2) : 0;

            // ดึงเวลาที่กำหนดใน work_shift_time
            $workShift = Work_shift_time::where('work_shift_id', $user->work_shift_id)->first();
            $totalLateSeconds = 0;
            $totalOutEarlySeconds = 0;
            if ($workShift) {

                $attendanceByDate = $attendance_records
                    ->groupBy(function ($record) {
                        return date('Y-m-d', strtotime($record->time));
                    });

                foreach ($attendanceByDate as $date => $records) {
                    if ($records->count() == 1) {
                        if ($workShift) {
                            $_time = $records->first()->time;

                            $firstCheckTime = strtotime($_time);
                            $shiftStart = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_in_start);
                            $shiftEnd = strtotime(date('Y-m-d', strtotime($_time)) . ' ' . $workShift->time_out_end);

                            if ($firstCheckTime >= $shiftStart && $firstCheckTime <= $shiftEnd) {
                                // อยู่ในช่วงเวลางาน -> แสดงเป็นเวลาเข้า
                                $time_in = date('H:i', $firstCheckTime);
                                $time_out = null;
                            } else {
                                // อยู่นอกช่วงเวลางาน -> แสดงเป็นเวลาออก
                                $time_in = null;
                                $time_out = date('H:i', $firstCheckTime);
                            }
                        }
                    } else { // กรณีมีบันทึกเวลามากกว่า 1 ครั้ง
                        $time_in = $records->min('time') ? date('H:i', strtotime($records->min('time'))) : null;
                        $time_out = $records->max('time') ? date('H:i', strtotime($records->max('time'))) : null;
                    }

                    if ($time_in) {
                        $time_in_end_datetime = date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_in_end;
                        $time_in_end_timestamp = strtotime($time_in_end_datetime);
                        $time_in_timestamp = strtotime($time_in);

                        //เวลาสุดท้ายที่นับว่าเปนเวลาเข้า
                        $time_out_start_timestamp = strtotime(date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_out_start);

                        if ($time_in_timestamp > $time_in_end_timestamp && $time_in_timestamp < $time_out_start_timestamp) {
                            $totalLateSeconds += $time_in_timestamp - $time_in_end_timestamp;
                        }
                    }

                    if ($time_out) {
                        $time_out_start_datetime = date('Y-m-d', strtotime($time_out)) . ' ' . $workShift->time_out_start;
                        $time_out_start_timestamp = strtotime($time_out_start_datetime);
                        $time_out_timestamp = strtotime($time_out);

                        //เวลาสุดท้ายที่นับว่าเปนเวลาเข้า
                        $time_in_end_timestamp = strtotime(date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_in_end);


                        if ($time_out_timestamp < $time_out_start_timestamp  && $time_out_timestamp > $time_in_end_timestamp) {
                            $totalOutEarlySeconds += $time_out_start_timestamp - $time_out_timestamp;
                        }
                    }
                }
            }

            // แปลงเวลาให้อยู่ในรูปแบบ HH:MM
            $late_display = $totalLateSeconds > 0 ? gmdate('H:i', $totalLateSeconds) : '00:00';
            $out_time_display = $totalOutEarlySeconds > 0 ? gmdate('H:i', $totalOutEarlySeconds) : '00:00';

            $time_ot = $ot->where('user_id', $user->id);
            $Ot = $time_ot ? $time_ot->where('status', 'approved')->sum('qty_hour') : 0;

            $data[] = [
                '#' => count($data) + 1,
                'รหัสพนักงาน' => $user->personnel_id,
                'ชื่อ' => $user->first_name . ' ' . $user->last_name,
                'ชื่อกลุ่ม' => $user->position->name,
                'วันทำงาน' => $workday,
                'มาทำงาน' => $comework ?: '0',
                'สาย' => $worklate ?: '0',
                'ขาด' => $workmiss ?: '0',
                'ลากิจ' => $leaveBusiness ?: '0',
                'ลาป่วย' => $leaveSick ?: '0',
                '% งาน' => $percentwork ?: '0',
                'สาย (ชม.)' => $late_display,
                'ออกก่อน (ชม.)' => $out_time_display,
                'โอที' => $Ot ?: '0',
            ];
        }

        // ส่งออกข้อมูลในรูปแบบ Excel
        return Excel::download(new class($data) implements FromArray, WithHeadings, WithColumnWidths, WithMapping {
            private $data;

            public function __construct(array $data)
            {
                $this->data = $data;
            }

            public function array(): array
            {
                return $this->data;
            }

            public function headings(): array
            {
                return [
                    '#',
                    'รหัสพนักงาน',
                    'ชื่อ',
                    'ชื่อกลุ่ม',
                    'วันทำงาน',
                    'มาทำงาน',
                    'สาย',
                    'ขาด',
                    'ลากิจ',
                    'ลาป่วย',
                    '% งาน',
                    'สาย (ชม.)',
                    'ออกก่อน (ชม.)',
                    'โอที',
                ];
            }

            public function columnWidths(): array
            {
                return [
                    'A' => 5,
                    'B' => 15,
                    'C' => 30,
                    'D' => 20,
                    'E' => 10,
                    'F' => 10,
                    'G' => 10,
                    'H' => 10,
                    'I' => 10,
                    'J' => 10,
                    'K' => 10,
                    'L' => 15,
                    'M' => 15,
                    'N' => 10,
                ];
            }

            public function map($row): array
            {
                return array_values($row);
            }
        }, 'รายงานรายเดือน.xlsx');
    }

    public function Report_MonthlyReportJSON(Request $request)
    {
        // รับค่าจาก Query Parameters
        $user_id = $request->query('user_id'); // รับ user_id (รหัสพนักงาน เช่น GB0001)
        $round = $request->query('round'); // รอบเงินเดือน
        $year = $request->query('year'); // รับปี
        $month = $request->query('month'); // รับเดือน
        $position_id = $request->query('position_id'); // รับ position_id
        $branch_id = $request->query('branch_id'); // branch_id

        // กำหนดช่วงวันที่
        $startOfMonth = date('Y-m-01', strtotime("$year-$month-01"));
        $endOfMonth = date('Y-m-t', strtotime("$year-$month-01"));

        if ($round) {
            $PayrollRound =  PayrollRound::where('round', $round)->first();

            $start_day = $PayrollRound->start_day;
            $end_day = $PayrollRound->end_day;

            $startOfMonth = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
            $endOfMonth = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

        }

        // Query Users
        $userQuery = User::with('branch', 'department', 'position');
        if ($user_id) {
            $userQuery->where('user_id', $user_id);
        }
        if ($position_id) {
            $userQuery->where('position_id', $position_id);
        }
        if ($branch_id) {
            $userQuery->where('branch_id', $branch_id);
        }
        $users = $userQuery->get();

        // Query Leave Tables
        $leaveTables = Leave_table::where(function ($query) use ($startOfMonth, $endOfMonth) {
            $query->whereBetween('date_start', [$startOfMonth, $endOfMonth])
                ->orWhereBetween('date_end', [$startOfMonth, $endOfMonth])
                ->orWhere(function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where('date_start', '<=', $startOfMonth)
                        ->where('date_end', '>=', $endOfMonth);
                });
        });
        if ($user_id) {
            $leaveTables->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $leaveTables = $leaveTables->get();

        // Query User Attendance
        $userAttendanceQuery = User_attendance::whereBetween('date', [$startOfMonth, $endOfMonth]);
        if ($user_id) {
            $userAttendanceQuery->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $userAttendance = $userAttendanceQuery->get();


        // Query OT
        $otQuery = Ot::whereBetween('date', [$startOfMonth, $endOfMonth]);
        if ($user_id) {
            $otQuery->whereHas('user', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            });
        }
        $ot = $otQuery->get();

        // ประมวลผลข้อมูล
        $reportData = [];
        foreach ($users as $user) {
            $attendance = $userAttendance
                ->where('user_id', $user->id)
                ->whereIn('type', ['normal', 'late', 'miss']);
            $attendance_records = Zk_time::where('personnel_id', $user->personnel_id)
                ->whereBetween('time', ["{$startOfMonth} 00:00:00", "{$endOfMonth} 23:59:59"])
                ->orderBy('time')
                ->get();

            $workday = $attendance->count();
            $comework = $userAttendance->where('user_id', $user->id)->where('type', 'normal')->count();
            $worklate = $userAttendance->where('user_id', $user->id)->where('type', 'late')->count();
            $workmiss = $userAttendance->where('user_id', $user->id)->where('type', 'miss')->count();

            $leaveBusiness = $leaveTables->where('user_id', $user->id)->whereIn('leave_type_id', [1, 2, 5, 6])->count();
            $leaveSick = $leaveTables->where('user_id', $user->id)->whereIn('leave_type_id', [3, 4])->count();

            $percentwork = ($workday > 0) ? round(($comework / $workday) * 100, 2) : 0;

            // ดึงเวลาที่กำหนดใน work_shift_time
            $workShift = Work_shift_time::where('work_shift_id', $user->work_shift_id)->first();
            $totalLateSeconds = 0;
            $totalOutEarlySeconds = 0;
            if ($workShift) {

                $attendanceByDate = $attendance_records
                    ->groupBy(function ($record) {
                        return date('Y-m-d', strtotime($record->time));
                    });

                foreach ($attendanceByDate as $date => $records) {
                    $time_in = $records->min('time');
                    $time_out = $records->max('time');

                    if ($time_in) {
                        $time_in_end_datetime = date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_in_end;
                        $time_in_end_timestamp = strtotime($time_in_end_datetime);
                        $time_in_timestamp = strtotime($time_in);

                        //เวลาสุดท้ายที่นับว่าเปนเวลาเข้า
                        $time_out_start_timestamp = strtotime(date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_out_start);

                        if ($time_in_timestamp > $time_in_end_timestamp && $time_in_timestamp < $time_out_start_timestamp) {
                            $totalLateSeconds += $time_in_timestamp - $time_in_end_timestamp;
                        }
                    }

                    if ($time_out) {
                        $time_out_start_datetime = date('Y-m-d', strtotime($time_out)) . ' ' . $workShift->time_out_start;
                        $time_out_start_timestamp = strtotime($time_out_start_datetime);
                        $time_out_timestamp = strtotime($time_out);


                        //เวลาสุดท้ายที่นับว่าเปนเวลาเข้า
                        $time_in_end_timestamp = strtotime(date('Y-m-d', strtotime($time_in)) . ' ' . $workShift->time_in_end);


                        if ($time_out_timestamp < $time_out_start_timestamp  && $time_out_timestamp > $time_in_end_timestamp) {
                            $totalOutEarlySeconds += $time_out_start_timestamp - $time_out_timestamp;
                        }
                    }
                }
            }

            // แปลงเวลาให้อยู่ในรูปแบบ HH:MM
            $late_display = $totalLateSeconds > 0 ? gmdate('H:i', $totalLateSeconds) : '00:00';
            $out_time_display = $totalOutEarlySeconds > 0 ? gmdate('H:i', $totalOutEarlySeconds) : '00:00';

            $time_ot = $ot->where('user_id', $user->id);
            $Ot = $time_ot ? $time_ot->where('status', 'approved')->sum('qty_hour') : 0;

            $reportData[] = [
                'user_id' => $user->user_id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'position' => $user->position->name ?? null,
                'workday' => $workday,
                'comework' => $comework,
                'worklate' => $worklate,
                'workmiss' => $workmiss,
                'leaveBusiness' => $leaveBusiness,
                'leaveSick' => $leaveSick,
                'percentwork' => $percentwork,
                'late_display' => $late_display,
                'out_time_display' => $out_time_display,
                'ot' => $Ot,
            ];
        }

        // Return JSON Response
        return response()->json([
            'status' => 'success',
            'data' => $reportData,
        ]);
    }

    //รายงาน รายบุคคล
    public function Report_IndividualReport(Request $request)
    {
        // รับค่าจาก API
        $personnel_id = $request->query('personnel_id');
        $date_starts = $request->query('date_start');
        $date_ends = $request->query('date_end');

        $date_start = date('Y-m-d', strtotime($date_starts)) . ' 00:00:00';
        $date_end = date('Y-m-d', strtotime($date_ends)) . ' 23:59:59';
        // ตรวจสอบว่า personnel_id ถูกส่งมา
        if (!$personnel_id) {
            return response()->json(['status' => 'error', 'message' => 'กรุณาระบุ personnel_id'], 400);
        }

        // ดึงข้อมูลพนักงาน
        $user = User::with('branch', 'position')->where('personnel_id', $personnel_id)->first();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'ไม่พบข้อมูลพนักงาน'], 404);
        }

        $holidays = Holiday::pluck('date')->toArray();

        // ดึงข้อมูลการเข้า-ออกงาน
        $workTimes = Zk_time::where('personnel_id', $personnel_id)
            ->whereBetween('time', [$date_start, $date_end])
            ->whereNotIn(DB::raw('DATE(time)'), $holidays)
            ->orderBy('time', 'asc')
            ->get();

        // ดึงข้อมูลการบันทึกการเข้างาน
        $attendance = User_attendance::where('user_id', $user->id)
            ->whereBetween('date', [$date_start, $date_end])
            ->orderBy('date', 'asc')
            ->get();


        // ดึงข้อมูลตารางกะ
        $workShiftTime = Work_shift_time::where('work_shift_id', $user->work_shift_id)->get();

        //company
        $Company = Company::where('id', $user->branch->company_id)->first();


        // ตั้งค่า mPDF
        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $mpdf = new \Mpdf\Mpdf([
            'fontDir' => array_merge($fontDirs, [
                base_path() . '/custom/font/directory',
            ]),
            'fontdata' => $fontData + [
                'th-sarabun' => [
                    'R' => 'THSarabun.ttf',
                    'I' => 'THSarabun Italic.ttf',
                    'B' => 'THSarabun Bold.ttf',
                    'BI' => 'THSarabun Bold Italic.ttf',
                ],
            ],
            'default_font' => 'th-sarabun',
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 12,
        ]);

        $mpdf->SetTitle('รายงาน พนักงานรายบุคคล');

        // การแบ่งหน้า
        $counter = 1;
        $itemPerPage = 45;
        $totalItem = count($attendance);
        $Pages = ceil($totalItem / $itemPerPage);

        for ($Page = 0; $Page < $Pages; $Page++) {
            if ($Page > 0) {
                $mpdf->AddPage();
            }

            $html = '
            <style>
                div { font-weight: bold; }
                table {
                    border: 0.1px solid black;
                    border-collapse: collapse;
                    width: 100%;
                }
                th, td {
                    border: 0.1px solid black;
                    font-size: 13.5px;
                    font-weight: bold;
                }
                td { padding-left: 5px; padding-top: 1.5px; padding-bottom: 1.5px; }
                #center { text-align: center; }
            </style>
            <div style="position: absolute; top: 105px; left: 40px; font-size: 15px; line-height: 1;">
                รายงาน พนักงานรายบุคคล
            </div>
            <div style="position: absolute; top: 130px; left: 40px; height: 100%; width: 90%;">
                <table>
                    <tr>
                        <td style="background-color: #D3D3D3;">รหัสพนักงาน</td>
                        <td colspan="2">' . $user->personnel_id . '</td>
                        <td style="background-color: #D3D3D3;">สาขา</td>
                        <td colspan="5">' . ($user->branch->name ?? '-') . '</td>
                    </tr>
                    <tr>
                        <td style="background-color: #D3D3D3;">ชื่อ</td>
                        <td colspan="2">' . $user->first_name . ' ' . $user->last_name . '</td>
                        <td style="background-color: #D3D3D3;">แผนก</td>
                        <td colspan="5">' . ($user->position->name ?? '-') . '</td>
                    </tr>
                    <tr style="background-color: #D3D3D3;">
                        <th>#</th>
                        <th>วัน</th>
                        <th>วัน (DD/MM/YYYY)</th>
                        <th>เวลาทำงาน</th>
                        <th>เวลาเข้า</th>
                        <th>เวลาออก</th>
                        <th>เวลาเข้าพัก</th>
                        <th>เวลาออกพัก</th>
                        <th>ชั่วโมงรวม</th>
                    </tr>
            ';

            for ($i = $Page * $itemPerPage; $i < min(($Page + 1) * $itemPerPage, $totalItem); $i++) {
                $record = $attendance[$i];
                $date = $record->date;

                // เปลี่ยนวันที่เป็นภาษาไทย
                $dateThai = Carbon::parse($date)->locale('th')->translatedFormat('l');

                // สร้างตัวย่อของชื่อวันในภาษาอังกฤษ
                $dayOfWeekEnglish = date('D', strtotime($date));

                // กรองหา shift ตามตัวย่อของชื่อวันในภาษาอังกฤษ
                $shift = $workShiftTime->filter(function ($item) use ($dayOfWeekEnglish) {
                    return $item->day == $dayOfWeekEnglish;
                })->first();

                // ตรวจสอบว่าพบ shift หรือไม่ และดึงค่า time_in และ time_out
                if ($shift) {
                    $shift_time_in = isset($shift->time_in) ? date('H:i', strtotime($shift->time_in)) : '-';
                    $shift_time_out = isset($shift->time_out) ? date('H:i', strtotime($shift->time_out)) : '-';
                } else {
                    $shift_time_in = '-';
                    $shift_time_out = '-';
                }

                $time_in = $workTimes->where('time', '>=', $date . ' 00:00:00')
                    ->where('time', '<=', $date . ' 23:59:59')
                    ->min('time');
                $time_out = $workTimes->where('time', '>=', $date . ' 00:00:00')
                    ->where('time', '<=', $date . ' 23:59:59')
                    ->max('time');

                // ดึงข้อมูลเวลาพัก
                $break_in_record = $workTimes->first(function ($timeRecord) use ($date, $shift) {
                    if ($shift && $shift->time_brake_in_start && $shift->time_brake_in_end) {
                        return date('Y-m-d', strtotime($timeRecord->time)) === $date &&
                            date('H:i:s', strtotime($timeRecord->time)) >= $shift->time_brake_in_start &&
                            date('H:i:s', strtotime($timeRecord->time)) <= $shift->time_brake_in_end;
                    }
                    return false;
                });

                $break_out_record = $workTimes->first(function ($timeRecord) use ($date, $shift) {
                    if ($shift && $shift->time_brake_out_start && $shift->time_brake_out_end) {
                        return date('Y-m-d', strtotime($timeRecord->time)) === $date &&
                            date('H:i:s', strtotime($timeRecord->time)) >= $shift->time_brake_out_start &&
                            date('H:i:s', strtotime($timeRecord->time)) <= $shift->time_brake_out_end;
                    }
                    return false;
                });

                // กำหนดเวลาพัก
                $break_in = $break_in_record ? date('H:i', strtotime($break_in_record->time)) : '-';
                $break_out = $break_out_record ? date('H:i', strtotime($break_out_record->time)) : '-';


                $total_work_time = '-';

                if ($time_in && $time_out) {
                    $total_work_time = $this->TimeDiff(date('H:i', strtotime($time_in)), date('H:i', strtotime($time_out)));
                }

                $html .= '
                <tr>
                    <td id="center">' . $counter++ . '</td>
                    <td>' . $dateThai . '</td>
                    <td>' . $date . '</td>
                    <td>' . $shift_time_in . ' - ' . $shift_time_out . '</td>
                    <td id="center">' . ($time_in ? date('H:i', strtotime($time_in)) : '-') . '</td>
                    <td id="center">' . ($time_out ? date('H:i', strtotime($time_out)) : '-') . '</td>
                    <td id="center">' . $break_in . '</td>
                    <td id="center">' . $break_out . '</td>
                    <td id="center">' . $total_work_time . '</td>
                </tr>';
            }

            $html .= '
                </table>
            </div>';

            $mpdf->WriteHTML($html);
        }

        $mpdf->Output();
    }

    public function Export_IndividualReport_Excel(Request $request)
    {
        $personnel_id = $request->query('personnel_id');
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');

        if (!$personnel_id) {
            return response()->json(['status' => 'error', 'message' => 'กรุณาระบุ personnel_id'], 400);
        }

        $user = User::with('branch', 'position')->where('personnel_id', $personnel_id)->first();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'ไม่พบข้อมูลพนักงาน'], 404);
        }

        if (!$date_start || !$date_end) {
            return response()->json(['status' => 'error', 'message' => 'กรุณาระบุ date_start และ date_end'], 400);
        }

        $holidays = Holiday::pluck('date')->toArray();

        $workTimes = Zk_time::where('personnel_id', $personnel_id)
            ->whereBetween('time', [$date_start, $date_end])
            ->whereNotIn(DB::raw('DATE(time)'), $holidays)
            ->orderBy('time', 'asc')
            ->get();

        $attendance = User_attendance::where('user_id', $user->id)
            ->whereBetween('date', [$date_start, $date_end])
            ->orderBy('date', 'asc')
            ->get();

        $workShiftTime = Work_shift_time::where('work_shift_id', $user->work_shift_id)->get();

        $fileName = 'รายงานพนักงาน_' . $personnel_id . '.xlsx';

        return Excel::download(new class($user, $attendance, $workTimes, $workShiftTime) implements FromArray, WithColumnWidths {
            private $user, $attendance, $workTimes, $workShiftTime;

            public function __construct($user, $attendance, $workTimes, $workShiftTime)
            {
                $this->user = $user;
                $this->attendance = $attendance;
                $this->workTimes = $workTimes;
                $this->workShiftTime = $workShiftTime;
            }
            public function array(): array
            {
                $data = [];
                // Header ส่วนบนของรายงาน
                $data[] = ["รหัสพนักงาน", $this->user->personnel_id, "", "สาขา", $this->user->branch->name ?? "-"];
                $data[] = ["ชื่อ", $this->user->first_name . " " . $this->user->last_name, "", "แผนก", $this->user->position->name ?? "-"];
                // Header ตาราง
                $data[] = [
                    "#",
                    "วัน",
                    "วัน (DD/MM/YYYY)",
                    "เวลาทำงาน",
                    "เวลาเข้า",
                    "เวลาออก",
                    "เวลาเข้าพัก",
                    "เวลาออกพัก",
                    "ชั่วโมงรวม"
                ];

                // รายการข้อมูล
                $counter = 1;
                foreach ($this->attendance as $record) {
                    $date = $record->date;

                    // วันภาษาไทย
                    $dateThai = Carbon::parse($date)->locale('th')->translatedFormat('l');

                    // Shift ข้อมูล
                    $dayOfWeekEnglish = date('D', strtotime($date));
                    $shift = $this->workShiftTime->filter(function ($item) use ($dayOfWeekEnglish) {
                        return $item->day == $dayOfWeekEnglish;
                    })->first();

                    $shift_time_in = $shift->time_in ?? '-';
                    $shift_time_out = $shift->time_out ?? '-';

                    // เวลาทำงาน
                    $time_in = $this->workTimes->where('time', '>=', $date . ' 00:00:00')
                        ->where('time', '<=', $date . ' 23:59:59')
                        ->min('time');
                    $time_out = $this->workTimes->where('time', '>=', $date . ' 00:00:00')
                        ->where('time', '<=', $date . ' 23:59:59')
                        ->max('time');

                    // เวลาพัก
                    $break_in_record = $this->workTimes->first(function ($timeRecord) use ($date, $shift) {
                        return $shift && date('Y-m-d', strtotime($timeRecord->time)) === $date &&
                            date('H:i:s', strtotime($timeRecord->time)) >= $shift->time_brake_in_start &&
                            date('H:i:s', strtotime($timeRecord->time)) <= $shift->time_brake_in_end;
                    });

                    $break_out_record = $this->workTimes->first(function ($timeRecord) use ($date, $shift) {
                        return $shift && date('Y-m-d', strtotime($timeRecord->time)) === $date &&
                            date('H:i:s', strtotime($timeRecord->time)) >= $shift->time_brake_out_start &&
                            date('H:i:s', strtotime($timeRecord->time)) <= $shift->time_brake_out_end;
                    });

                    $break_in = $break_in_record ? date('H:i', strtotime($break_in_record->time)) : '-';
                    $break_out = $break_out_record ? date('H:i', strtotime($break_out_record->time)) : '-';

                    $total_work_time = $time_in && $time_out
                        ? gmdate('H:i', strtotime($time_out) - strtotime($time_in))
                        : '-';

                    $data[] = [
                        $counter++,
                        $dateThai,
                        $date,
                        "$shift_time_in - $shift_time_out",
                        $time_in ? date('H:i', strtotime($time_in)) : '-',
                        $time_out ? date('H:i', strtotime($time_out)) : '-',
                        $break_in,
                        $break_out,
                        $total_work_time,
                    ];
                }
                return $data;
            }

            public function columnWidths(): array
            {
                return [
                    'A' => 5,
                    'B' => 15,
                    'C' => 15,
                    'D' => 15,
                    'E' => 15,
                    'F' => 15,
                    'G' => 15,
                    'H' => 15,
                    'I' => 15,
                ];
            }
        }, $fileName);
    }

    public function getUserDetails()
    {
        $users = User::select('id', 'first_name', 'last_name', 'salary')->get();

        $data = $users->map(function ($user, $index) {
            return [
                'ลำดับ' => $index + 1,
                'ชื่อ-สกุล' => $user->first_name . ' ' . $user->last_name,
                'เงินเพิ่ม' => null,
                'เงินหัก' => null,
                'เงินเดือน' => $user->salary,
                'เบิกเงินเดือนล่วงหน้า' => null,
                'ค่าคอม' => null,
                'OT' => null,
                'รายได้สุทธิ' => $user->salary,
                'มาสายทั้งหมด' => null,
                'สถานะ' => 'ยังไม่จ่าย'
            ];
        });

        return response()->json($data);
    }

    public function getPayrollReport(Request $request)
    {
        $round = $request->query('round');
        $year = $request->query('year');
        $month = $request->query('month');

        if (!$year || !$month) {
            return response()->json(['error' => 'กรุณาระบุปีและเดือน'], 400);
        }

        $date_start = date('Y-m-01', strtotime("$year-$month-01"));
        $date_end = date('Y-m-t', strtotime("$year-$month-01"));

        if ($round) {
            $PayrollRound =  PayrollRound::where('round', $round)->first();

            $start_day = $PayrollRound->start_day;
            $end_day = $PayrollRound->end_day;

            $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
            $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

        }


        $users = User::select('id', 'first_name', 'last_name', 'salary', 'work_type', 'position_id')
            ->get();

        $data = [];
        $no = 1;

        foreach ($users as $user) {

            // คำนวณจำนวนวันทำงานในช่วงวันที่ที่กำหนด
            $workDays = User_attendance::where('user_id', $user->id)
                ->where('type', 'normal')
                ->orWhere('type', 'late')
                ->whereBetween('date', [$date_start, $date_end])
                ->count();

            $salary = $user->salary;
            if ($user->work_type == 'day') {
                $salary = $salary * $workDays;
            }

            $incomeFromIncomePaid = IncomePaid::where('user_id', $user->id)
                ->whereBetween('date', [$date_start, $date_end])
                ->sum('price');

            $incomeFromUserIncome = UserIncome::where('user_id', $user->id)
                ->sum('price');

            // รวม'เงินเพิ่ม'จากทั้งสองตาราง
            $income = $incomeFromIncomePaid + $incomeFromUserIncome;

            $deductFromDeductPaid = DeductPaid::where('user_id', $user->id)
                ->whereBetween('date', [$date_start, $date_end])
                ->sum('price');

            $deductFromUserDeduct = UserDeduct::where('user_id', $user->id)
                ->sum('price');

            // รวม'เงินหัก'จากทั้งสองตาราง
            $deduct = $deductFromDeductPaid + $deductFromUserDeduct;


            // สายทั้งหมด
            $latetimein = User_attendance::where('user_id', $user->id)
                ->with('zk_time')
                ->where('type', '=', 'late')
                ->whereBetween('date', [$date_start, $date_end])
                ->get()
                ->toArray();

            // เวลาเข้างานของผู้ใช้
            $worktime = Work_shift_time::where('work_shift_id', $user->work_shift_id)->first()->value('time_in');

            // เวลามาสายทั้งหมด
            $total_minOflate = 0;
            for ($j = 0; $j < count($latetimein); $j++) {
                $latetime =  date('H:i', strtotime($latetimein[$j]['zk_time']['time']));

                $total_minOflate += abs(strtotime($latetime) - strtotime($worktime)) / 60;
            }

            if ($user->work_type == 'day') {
                $salaryPerMin = round(($salary / 8) / 60, 2);
            } else {
                $salaryPerMin = round(($salary / 30 / 8 / 60), 2);
            }

            $lateDeduct = $total_minOflate * $salaryPerMin;

            // $commission = $this->getCommisionUser($user->id, $round);
            $commission = 0;
            $ot = $this->getOtUser($user->id, $date_start, $date_end);
            $withdraw = $this->getWithdrawSalaryUser($user->id, $date_start, $date_end);

            $netIncome = $salary + $income + $commission + $ot - $deduct - $withdraw - $lateDeduct;

            $payStatus = Employee_salary::where('user_id', $user->id)
                ->where('hire_date', 'like', "$round-%")
                ->exists();

            $data[] = [
                'ลำดับ' => $no++,
                'ชื่อ-สกุล' => $user->first_name . ' ' . $user->last_name,
                'เงินเพิ่ม' => $income ?: '0',
                'เงินหัก' => $deduct ?: '0',
                'เงินเดือน' => $salary ?: '0',
                'เบิกเงินล่วงหน้า' => $withdraw ?: '0',
                'ค่าคอม' => $commission ?: '0',
                'OT' => $ot ?: '0',
                'รายได้สุทธิ' => $netIncome ?: '0',
                'มาสายทั้งหมด' => $total_minOflate ?: '0',
                'สถานะ' => $payStatus ? 'จ่ายแล้ว' : 'ยังไม่จ่าย',
            ];
        }

        return response()->json([
            'message' => 'เรียกดูข้อมูลสำเร็จ',
            'data' => $data,
        ]);
    }

    public function Export_getPayrool_Excel(Request $request)
    {
        $round = $request->query('round');
        $year = $request->query('year');
        $month = $request->query('month');

        if (!$year || !$month) {
            return response()->json(['error' => 'กรุณาระบุปีและเดือน'], 400);
        }

        $date_start = date('Y-m-01', strtotime("$year-$month-01"));
        $date_end = date('Y-m-t', strtotime("$year-$month-01"));

        if ($round) {
            $PayrollRound =  PayrollRound::where('round', $round)->first();

            $start_day = $PayrollRound->start_day;
            $end_day = $PayrollRound->end_day;

            $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
            $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

        }

        $users = User::select('id', 'first_name', 'last_name', 'salary', 'work_type', 'position_id', 'work_shift_id', 'user_id')
            ->get();

        $data = [];
        $no = 1;
        $createdBy = auth()->user()->name ?? 'System'; // ชื่อผู้สร้าง (หรือ System)

        foreach ($users as $user) {

            // ดึง Work Shift Time
            $workShift = Work_shift_time::where('id', $user->work_shift_id)->first();
            $time_in_end = $workShift ? strtotime($workShift->time_in_end) : null;


            // คำนวณเวลาสายทั้งหมด
            $attendanceRecords = Zk_time::where('personnel_id', $user->user_id)
                ->whereBetween('time', ["{$date_start} 00:00:00", "{$date_end} 23:59:59"])
                ->get();

            $totalLateSeconds = 0;

            if ($time_in_end) {
                foreach ($attendanceRecords as $record) {
                    $recordTime = strtotime($record->time);
                    if ($recordTime > $time_in_end) {
                        $totalLateSeconds += $recordTime - $time_in_end;
                    }
                }
            }

            // แปลงเวลาสายทั้งหมดเป็นรูปแบบ HH:MM
            $lateDisplay = $totalLateSeconds > 0 ? gmdate('H:i', $totalLateSeconds) : '00:00';

            // คำนวณจำนวนวันทำงานในช่วงวันที่ที่กำหนด
            $workDays = User_attendance::where('user_id', $user->id)
                ->whereIn('type', ['normal', 'late'])
                ->whereBetween('date', [$date_start, $date_end])
                ->count();

            $salary = $user->salary;
            if ($user->work_type == 'day') {
                $salary = $salary * $workDays;
            }

            $incomeFromIncomePaid = IncomePaid::where('user_id', $user->id)
                ->whereBetween('date', [$date_start, $date_end])
                ->sum('price');

            $incomeFromUserIncome = UserIncome::where('user_id', $user->id)
                ->sum('price');

            // รวม'เงินเพิ่ม'จากทั้งสองตาราง
            $income = $incomeFromIncomePaid + $incomeFromUserIncome;

            $deductFromDeductPaid = DeductPaid::where('user_id', $user->id)
                ->whereBetween('date', [$date_start, $date_end])
                ->sum('price');

            $deductFromUserDeduct = UserDeduct::where('user_id', $user->id)
                ->sum('price');

            // รวม'เงินหัก'จากทั้งสองตาราง
            $deduct = $deductFromDeductPaid + $deductFromUserDeduct;

            //$commission = $this->getCommisionUser($user->id, $round);
            $commission = 0;
            $ot = $this->getOtUser($user->id, $date_start, $date_end);
            $withdraw = $this->getWithdrawSalaryUser($user->id, $date_start, $date_end);

            $netIncome = $salary + $income + $commission + $ot - $deduct - $withdraw;

            // ตรวจสอบและบันทึก/อัปเดตข้อมูลในตาราง employee_salaries
            $hireDate = Carbon::createFromDate($year, $month, 1)->toDateString();
            Employee_salary::updateOrCreate(
                ['user_id' => $user->id, 'hire_date' => $hireDate],
                [
                    'salary' => $salary,
                    'commission' => $commission,
                    'ot' => $ot,
                    'income' => $income,
                    'deduct' => $deduct,
                    'total' => $netIncome,
                    'create_by' => $createdBy,
                    'updated_at' => now(),
                ]
            );

            $payStatus = Employee_salary::where('user_id', $user->id)
                ->where('hire_date', $hireDate)
                ->where(function ($query) {
                    $query->whereNull('slip')
                        ->orWhere('slip', '=', '');
                })
                ->exists();

            $data[] = [
                'ลำดับ' => $no++,
                'ชื่อ-สกุล' => $user->first_name . ' ' . $user->last_name,
                'เงินเพิ่ม' => $income ?: '0',
                'เงินหัก' => $deduct ?: '0',
                'เงินเดือน' => $salary ?: '0',
                'เบิกเงินล่วงหน้า' => $withdraw ?: '0',
                'ค่าคอม' => $commission ?: '0',
                'OT' => $ot ?: '0',
                'รายได้สุทธิ' => $netIncome ?: '0',
                'มาสายทั้งหมด' => $lateDisplay,
                'สถานะ' => $payStatus ? 'ยังไม่จ่าย' : 'จ่ายแล้ว',
            ];
        }

        return Excel::download(new class($data) implements FromArray, WithHeadings, WithColumnWidths, WithMapping {
            private $data;

            public function __construct(array $data)
            {
                $this->data = $data;
            }

            public function array(): array
            {
                return $this->data;
            }

            public function headings(): array
            {
                return [
                    'ลำดับ',
                    'ชื่อ-สกุล',
                    'เงินเพิ่ม',
                    'เงินหัก',
                    'เงินเดือน',
                    'เบิกเงินล่วงหน้า',
                    'ค่าคอม',
                    'OT',
                    'รายได้สุทธิ',
                    'มาสายทั้งหมด',
                    'สถานะ',
                ];
            }

            public function columnWidths(): array
            {
                return [
                    'A' => 5,
                    'B' => 30,
                    'C' => 15,
                    'D' => 15,
                    'E' => 15,
                    'F' => 20,
                    'G' => 15,
                    'H' => 15,
                    'I' => 20,
                    'J' => 15,
                    'K' => 15,
                ];
            }

            public function map($row): array
            {
                return [
                    $row['ลำดับ'],
                    $row['ชื่อ-สกุล'],
                    $row['เงินเพิ่ม'],
                    $row['เงินหัก'],
                    $row['เงินเดือน'],
                    $row['เบิกเงินล่วงหน้า'],
                    $row['ค่าคอม'],
                    $row['OT'],
                    $row['รายได้สุทธิ'],
                    $row['มาสายทั้งหมด'],
                    $row['สถานะ'],
                ];
            }
        }, "รายงานเงินเดือน_{$year}_{$month}.xlsx");
    }


    // public function Export_getPayrool_Excel(Request $request)
    // {
    //     $year = $request->query('year');
    //     $month = $request->query('month');

    //     if (!$year || !$month) {
    //         return response()->json(['error' => 'กรุณาระบุปีและเดือน'], 400);
    //     }

    //     $round = $year . '-' . $month;
    //     $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth()->toDateString();
    //     $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth()->toDateString();

    //     $users = User::select('id', 'first_name', 'last_name', 'salary', 'work_type', 'user_id', 'work_shift_id')->get();

    //     $workTimesInOut = Zk_time::whereBetween('time', ["{$startOfMonth} 00:00:00", "{$endOfMonth} 23:59:59"])
    //         ->get();

    //     $data = [];
    //     $no = 1;

    //     foreach ($users as $user) {
    //         // ดึงข้อมูลเวลาของกะการทำงาน
    //         $workShift = Work_shift_time::where('work_shift_id', $user->work_shift_id)->first();
    //         if (!$workShift) {
    //             continue; // ข้ามถ้าไม่พบข้อมูลกะการทำงาน
    //         }

    //         $workTimeIn = strtotime($workShift->time_in);
    //         $workTimeOut = strtotime($workShift->time_out);

    //         $attendanceRecords = $workTimesInOut
    //             ->where('personnel_id', $user->user_id)
    //             ->sortBy('time');

    //         $totalLateSeconds = 0;
    //         $totalOutEarlySeconds = 0;

    //         $currentDate = null;
    //         $timeIn = null;
    //         $timeOut = null;

    //         foreach ($attendanceRecords as $record) {
    //             $recordDate = date('Y-m-d', strtotime($record->time));

    //             if ($currentDate !== $recordDate) {
    //                 if ($timeIn && $timeOut) {
    //                     // คำนวณเวลามาสาย
    //                     if ($timeIn > $workTimeIn) {
    //                         $totalLateSeconds += $timeIn - $workTimeIn;
    //                     }

    //                     // คำนวณเวลาออกก่อน
    //                     if ($timeOut < $workTimeOut) {
    //                         $totalOutEarlySeconds += $workTimeOut - $timeOut;
    //                     }
    //                 }

    //                 $currentDate = $recordDate;
    //                 $timeIn = strtotime($record->time);
    //                 $timeOut = null;
    //             } else {
    //                 $timeOut = strtotime($record->time);
    //             }
    //         }

    //         if ($timeIn) {
    //             if ($timeIn > $workTimeIn) {
    //                 $totalLateSeconds += $timeIn - $workTimeIn;
    //             }
    //         }

    //         if ($timeOut) {
    //             if ($timeOut < $workTimeOut) {
    //                 $totalOutEarlySeconds += $workTimeOut - $timeOut;
    //             }
    //         }

    //         $lateDisplay = $totalLateSeconds > 0 ? gmdate('H:i', $totalLateSeconds) : '00:00';

    //         $salary = $user->salary;
    //         $workDays = User_attendance::where('user_id', $user->id)
    //             ->where('type', '!=', 'off')
    //             ->whereBetween('date', [$startOfMonth, $endOfMonth])
    //             ->count();

    //         if ($user->work_type == 'day') {
    //             $salary = $salary * $workDays;
    //         }

    //         $income = IncomePaid::where('user_id', $user->id)
    //             ->where('date', 'like', "$round-%")
    //             ->sum('price');

    //         $deduct = DeductPaid::where('user_id', $user->id)
    //             ->where('date', 'like', "$round-%")
    //             ->sum('price');

    //         $salaryPerMin = $user->work_type == 'day'
    //             ? ($salary / 8 / 60)
    //             : ($salary / 30 / 8 / 60);

    //         $lateDeduct = round($totalLateSeconds * $salaryPerMin / 60, 2);

    //         $commission = $this->getCommisionUser($user->id, $round);
    //         $ot = $this->getOtUser($user->id, $round);
    //         $withdraw = $this->getWithdrawSalaryUser($user->id);

    //         $netIncome = $salary + $income + $commission + $ot - $deduct - $withdraw - $lateDeduct;

    //         $payStatus = Employee_salary::where('user_id', $user->id)
    //             ->where('hire_date', 'like', "$round-%")
    //             ->exists();

    //         $data[] = [
    //             'ลำดับ' => $no++,
    //             'ชื่อ-สกุล' => $user->first_name . ' ' . $user->last_name,
    //             'เงินเพิ่ม' => $income ?: '0',
    //             'เงินหัก' => $deduct ?: '0',
    //             'เงินเดือน' => $salary,
    //             'เบิกเงินล่วงหน้า' => $withdraw ?: '0',
    //             'ค่าคอม' => $commission ?: '0',
    //             'OT' => $ot ?: '0',
    //             'รายได้สุทธิ' => $netIncome ?: '0',
    //             'มาสายทั้งหมด' => $lateDisplay,
    //             'สถานะ' => $payStatus ? 'จ่ายแล้ว' : 'ยังไม่จ่าย',
    //         ];
    //     }

    //     return Excel::download(new class($data) implements FromArray, WithHeadings, WithColumnWidths, WithMapping {
    //         private $data;

    //         public function __construct(array $data)
    //         {
    //             $this->data = $data;
    //         }

    //         public function array(): array
    //         {
    //             return $this->data;
    //         }

    //         public function headings(): array
    //         {
    //             return [
    //                 'ลำดับ',
    //                 'ชื่อ-สกุล',
    //                 'เงินเพิ่ม',
    //                 'เงินหัก',
    //                 'เงินเดือน',
    //                 'เบิกเงินล่วงหน้า',
    //                 'ค่าคอม',
    //                 'OT',
    //                 'รายได้สุทธิ',
    //                 'มาสายทั้งหมด',
    //                 'สถานะ',
    //             ];
    //         }

    //         public function columnWidths(): array
    //         {
    //             return [
    //                 'A' => 5,
    //                 'B' => 30,
    //                 'C' => 15,
    //                 'D' => 15,
    //                 'E' => 15,
    //                 'F' => 20,
    //                 'G' => 15,
    //                 'H' => 15,
    //                 'I' => 20,
    //                 'J' => 15,
    //                 'K' => 15,
    //             ];
    //         }

    //         public function map($row): array
    //         {
    //             return [
    //                 $row['ลำดับ'],
    //                 $row['ชื่อ-สกุล'],
    //                 $row['เงินเพิ่ม'],
    //                 $row['เงินหัก'],
    //                 $row['เงินเดือน'],
    //                 $row['เบิกเงินล่วงหน้า'],
    //                 $row['ค่าคอม'],
    //                 $row['OT'],
    //                 $row['รายได้สุทธิ'],
    //                 $row['มาสายทั้งหมด'],
    //                 $row['สถานะ'],
    //             ];
    //         }
    //     }, "รายงานเงินเดือน_{$year}_{$month}.xlsx");
    // }

    //import excel เข้าไป
    // public function Import_Payrool_Excel(Request $request)
    // {
    //     $file = $request->file('excel_file');
    //     $year = $request->query('year'); // รับ query 'year'
    //     $month = $request->query('month'); // รับ query 'month'

    //     if (!$file || !$year || !$month) {
    //         return response()->json(['error' => 'กรุณาอัปโหลดไฟล์ Excel และระบุปี/เดือน'], 400);
    //     }

    //     // กำหนดวันที่ใน hire_date
    //     $hireDate = Carbon::createFromDate($year, $month, 1)->toDateString(); // ใช้วันที่ 1 ของเดือนที่ระบุ

    //     $rows = Excel::toArray([], $file)[0]; // อ่านข้อมูลจาก Excel
    //     $createdBy = auth()->user()->name ?? 'System'; // ชื่อผู้สร้าง (หรือ System)

    //     foreach ($rows as $key => $row) {
    //         // ข้ามหัวตาราง
    //         if ($key == 0) {
    //             continue;
    //         }

    //         // ตรวจสอบสถานะ
    //         if (trim($row[10]) === 'จ่ายแล้ว') {
    //             // แยกชื่อและนามสกุลจากข้อมูล
    //             $fullName = $row[1];

    //             // ลิสต์ของคำนำหน้าที่เป็นไปได้
    //             $prefixes = ['นาย ', 'นาง ', 'นางสาว '];

    //             // ตรวจสอบว่ามีคำนำหน้าหรือไม่
    //             $firstName = '';
    //             $lastName = '';

    //             // ลบคำนำหน้าถ้ามี
    //             foreach ($prefixes as $prefix) {
    //                 if (strpos($fullName, $prefix) === 0) { // ตรวจสอบว่าชื่อเริ่มต้นด้วยคำนำหน้าหรือไม่
    //                     $fullName = substr($fullName, strlen($prefix)); // ลบคำนำหน้าออก
    //                     break;
    //                 }
    //             }
    //             $nameParts = explode(' ', $fullName, 2);
    //             $firstName = $nameParts[0] ?? null;
    //             $lastName = $nameParts[1] ?? null;

    //             // ค้นหา user_id
    //             $userId = User::where('first_name', 'LIKE', '%' . $firstName . '%')
    //                 ->where('last_name', 'LIKE', '%' . $lastName . '%')
    //                 ->value('id'); // ค้นหา user_id จากชื่อและนามสกุล

    //             if (!$userId) {
    //                 continue; // ข้ามถ้าไม่พบผู้ใช้
    //             }

    //             // ตรวจสอบว่ามี user_id และ hire_date ซ้ำหรือไม่
    //             $isDuplicate = Employee_salary::where('user_id', $userId)
    //                 ->where('hire_date', $hireDate)
    //                 ->exists();

    //             if ($isDuplicate) {
    //                 continue; // ข้ามถ้าข้อมูลซ้ำ
    //             }

    //             // บันทึกข้อมูลใน employee_salaries
    //             Employee_salary::create([
    //                 'user_id' => $userId,
    //                 'hire_date' => $hireDate,
    //                 'salary' => (float) $row[4] ?: 0.00,
    //                 'commission' => (float) $row[6] ?: 0.00,
    //                 'ot' => (float) $row[7] ?: 0.00,
    //                 'income' => (float) $row[2] ?: 0.00,
    //                 'deduct' => (float) $row[3] ?: 0.00,
    //                 'total' => (float) $row[8] ?: 0.00,
    //                 'slip' => null,
    //                 'create_by' => $createdBy,
    //                 'created_at' => now(),
    //                 'updated_at' => now(),
    //             ]);
    //         }
    //     }

    //     return response()->json(['message' => 'นำเข้าข้อมูลสำเร็จ'], 200);
    // }

    public function Import_Slip_Zip(Request $request)
    {
        $file = $request->file('zip_file');
        $round = $request->query('round');
        $year = $request->query('year');
        $month = $request->query('month');

        if (!$file || !$round || !$year || !$month) {
            return response()->json(['error' => 'กรุณาอัปโหลดไฟล์ ZIP และระบุปี/เดือน'], 400);
        }

        // ดึง path สำหรับการอัปโหลดไฟล์
        $uploadPath = public_path("images/slip_images/{$year}/{$month}");
        if (!file_exists($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // แตกไฟล์ ZIP
        $zip = new \ZipArchive;
        if ($zip->open($file) === true) {
            $zip->extractTo($uploadPath);
            $zip->close();
        } else {
            return response()->json(['error' => 'ไม่สามารถเปิดไฟล์ ZIP ได้'], 400);
        }

        // อ่านไฟล์ในโฟลเดอร์
        $files = scandir($uploadPath);

        // ลูปผ่านไฟล์ทั้งหมด
        foreach ($files as $fileName) {
            if ($fileName === '.' || $fileName === '..') {
                continue;
            }

            // ดึง user_id จากชื่อไฟล์ (ตัดนามสกุลออก)
            $userIdFromFile = pathinfo($fileName, PATHINFO_FILENAME);


            // ค้นหา id จาก user_id
            $userId = User::where('user_id', $userIdFromFile)->value('id');

            if (!$userId) {
                continue; // ข้ามถ้าไม่พบ id
            }

            // ตรวจสอบ employee_salaries โดยใช้ id, hire_date
            $employeeSalary = Payroll::where('user_id', $userId)
                ->where('round', $round)
                ->where('year', $year)
                ->where('month', $month)
                ->first();

            if (!$employeeSalary) {
                continue; // ข้ามถ้าไม่พบ employee_salary
            }

            $employeeSalary->slip =  "images/slip_images/{$year}/{$month}/{$fileName}";
            $employeeSalary->save();
        }

        return response()->json(['message' => 'นำเข้ารูปภาพสำเร็จ'], 200);
    }

    //รายงานเบื้ยขยัน
    public function reportBonusStep(Request $request)
    {

        $year = $request->year ?? now()->year;
        $month = $request->month ?? now()->month;

        $user_id = $request->user_id ?? null;


        $loginBy = $request->login_by;


        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_branch_id = $request->login_branch_id;


        $IncomePaid = IncomePaid::with('user')
            ->with('income_type')
            ->with('payroll')
            ->where('year', $year)
            ->where('month', $month);

        $IncomePaid->WhereHas('income_type', function ($query) use ($login_branch_id) {
            $query->where('name', 'เบี้ยขยัน');
            if ($login_branch_id) {
                $query->where('branch_id', $login_branch_id);
            }
        });

        if ($user_id) {
            $IncomePaid->where('user_id', $user_id);
        }
        $IncomePaid = $IncomePaid->get();

        if ($IncomePaid->isNotEmpty()) {

            for ($i = 0; $i < count($IncomePaid); $i++) {

                $No = $i + 1;
                $IncomePaid[$i]->No = $No;
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $IncomePaid);
    }

    // public function Import_Slip_Zip(Request $request)
    // {
    //     $file = $request->file('zip_file');
    //     $year = $request->query('year');
    //     $month = $request->query('month');

    //     if (!$file || !$year || !$month) {
    //         return response()->json(['error' => 'กรุณาอัปโหลดไฟล์ ZIP และระบุปี/เดือน'], 400);
    //     }

    //     // ดึง path สำหรับการอัปโหลดไฟล์
    //     $uploadPath = public_path("images/slip_images/{$year}/{$month}");
    //     if (!file_exists($uploadPath)) {
    //         mkdir($uploadPath, 0755, true);
    //     }

    //     // แตกไฟล์ ZIP
    //     $zip = new \ZipArchive;
    //     if ($zip->open($file) === true) {
    //         $zip->extractTo($uploadPath);
    //         $zip->close();
    //     } else {
    //         return response()->json(['error' => 'ไม่สามารถเปิดไฟล์ ZIP ได้'], 400);
    //     }

    //     // อ่านไฟล์ในโฟลเดอร์
    //     $files = scandir($uploadPath);

    //     // ลูปผ่านไฟล์ทั้งหมด
    //     foreach ($files as $fileName) {
    //         if ($fileName === '.' || $fileName === '..') {
    //             continue;
    //         }

    //         // ดึง user_id จากชื่อไฟล์ (ตัดนามสกุลออก)
    //         $userIdFromFile = pathinfo($fileName, PATHINFO_FILENAME);

    //         // ค้นหา id จาก user_id
    //         $userId = User::where('user_id', $userIdFromFile)->value('id');

    //         if (!$userId) {
    //             continue; // ข้ามถ้าไม่พบ id
    //         }

    //         // ตรวจสอบ employee_salaries โดยใช้ id, hire_date
    //         $employeeSalary = Employee_salary::where('user_id', $userId)
    //             ->whereYear('hire_date', $year)
    //             ->whereMonth('hire_date', $month)
    //             ->first();

    //         if (!$employeeSalary) {
    //             continue; // ข้ามถ้าไม่พบ employee_salary
    //         }

    //         // อัปเดตฟิลด์ slip ด้วย path ของรูปภาพ
    //         $employeeSalary->update([
    //             'slip' => "images/slip_images/{$year}/{$month}/{$fileName}",
    //         ]);
    //     }

    //     return response()->json(['message' => 'นำเข้ารูปภาพสำเร็จ'], 200);
    // }

    public function reportSummarySale(Request $request)
    {
        // return response()->json(['message' => 'test'], 200);

        // $year = $request->year;
        // $page_id = $request->page_id;
        // $channal = $request->channal;

        // $months = [
        //     ['month' => 'มกราคม',   'num' => 1, 'total' => 0],
        //     ['month' => 'กุมภาพันธ์',  'num' => 2, 'total' => 0],
        //     ['month' => 'มีนาคม',    'num' => 3, 'total' => 0],
        //     ['month' => 'เมษายน',   'num' => 4, 'total' => 0],
        //     ['month' => 'พฤษภาคม',  'num' => 5, 'total' => 0],
        //     ['month' => 'มิถุนายน',   'num' => 6, 'total' => 0],
        //     ['month' => 'กรกฎาคม',  'num' => 7, 'total' => 0],
        //     ['month' => 'สิงหาคม',   'num' => 8, 'total' => 0],
        //     ['month' => 'กันยายน',   'num' => 9, 'total' => 0],
        //     ['month' => 'ตุลาคม',    'num' => 10, 'total' => 0],
        //     ['month' => 'พฤศจิกายน', 'num' => 11, 'total' => 0],
        //     ['month' => 'ธันวาคม',   'num' => 12, 'total' => 0],
        // ];

        // for ($i = 0; $i < count($months); $i++) {


        //     $date_start = Carbon::create($year, $months[$i]['num'], 1)->startOfMonth()->toDateString();
        //     $date_end = Carbon::create($year, $months[$i]['num'], 1)->endOfMonth()->toDateString();



        //     $config = $this->getConfig();
        //     $total = 0;

        //     if ($config->commission_calculate == 'sales') {
        //         //จากยอดขาย

        //         //ยอดขาย
        //         $Sale_order_line = Sale_order_line::WhereHas('sale_order', function ($query) use ($date_start, $date_end, $channal, $page_id) {
        //             $query->where('date_time', '>=', $date_start);
        //             $query->where('date_time', '<=', $date_end);
        //             if ($channal) {
        //                 $query->where('channal', $channal);
        //             }
        //             if ($page_id) {
        //                 $query->where('page_id', $page_id);
        //             }
        //             $query->where('status', 'finish');
        //         })
        //             ->sum('total');

        //         $total = $Sale_order_line;
        //     } else {
        //         //จากกำไร

        //         //ยอดขาย
        //         $Sale_order_line = Sale_order_line::WhereHas('sale_order', function ($query) use ($date_start, $date_end, $channal, $page_id) {
        //             $query->where('date_time', '>=', $date_start);
        //             $query->where('date_time', '<=', $date_end);
        //             if ($channal) {
        //                 $query->where('channal', $channal);
        //             }
        //             if ($page_id) {
        //                 $query->where('page_id', $page_id);
        //             }
        //             $query->where('status', 'finish');
        //         })
        //             ->sum('total');

        //         //ต้นทุน
        //         $Sale_order_line = Sale_order_line::with('item')
        //             ->with('sale_order')
        //             ->WhereHas('sale_order', function ($query) use ($date_start, $date_end) {
        //                 $query->where('date_time', '>=', $date_start);
        //                 $query->where('date_time', '<=', $date_end);
        //                 $query->where('status', 'finish');
        //             })
        //             ->get();

        //         $totalCost = 0;
        //         for ($i = 0; $i < count($Sale_order_line); $i++) {

        //             if ($Sale_order_line[$i]->item) {
        //                 $totalCost += $Sale_order_line[$i]->item->unit_cost;
        //             }
        //         }
        //         $total = $Sale_order_line - $totalCost;
        //     }

        //     $months[$i]['total'] = $total;
        // }

        // return response()->json([
        //     'code' => strval(200),
        //     'status' => true,
        //     'message' => 'Successful',
        //     'data' => $months,
        // ], 200);
    }
}
