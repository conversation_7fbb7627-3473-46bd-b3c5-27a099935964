<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LoanMoratorium;
use App\Models\Loan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class LoanMoratoriumController extends Controller
{
    /**
     * Display a listing of the resource for DataTables.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function datatables(Request $request)
    {
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        // Get login information
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $col = array(
            'id', 'loan_id', 'start_installment', 'months',
            'interest_accrual_mode', 'calculated_extra_interest',
            'approved_by', 'approved_at', 'reason', 'created_at', 'updated_at'
        );

        $d = LoanMoratorium::select($col)
            ->with(['loan.user', 'approver'])
            ->orderby($col[$order[0]['column']], $order[0]['dir']);

        // Apply branch/company filters if needed
        if ($login_branch_id) {
            $d->whereHas('loan.user', function ($query) use ($login_branch_id) {
                $query->where('branch_id', $login_branch_id);
            });
        }

        if ($login_company_id) {
            $d->whereHas('loan.user.branch', function ($query) use ($login_company_id) {
                $query->where('company_id', $login_company_id);
            });
        }

        // Search functionality
        if ($search['value'] != '' && $search['value'] != null) {
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {
            // Add row numbers
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {
                $No = $No + 1;
                $d[$i]->No = $No;

                // Add status based on approval
                $d[$i]->status = $d[$i]->approved_at ? 'approved' : 'pending';

                // Format dates
                if ($d[$i]->approved_at) {
                    $d[$i]->approved_at_formatted = Carbon::parse($d[$i]->approved_at)->format('d/m/Y H:i:s');
                }
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'loan_id' => 'required',
            'start_installment' => 'required|integer|min:1',
            'months' => 'required|integer|min:1|max:12',
            'interest_accrual_mode' => 'required|in:none,accrue',
            'reason' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        $loginBy = $request->login_by;
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {
            // Check if loan exists and is active
            $loan = Loan::find($request->loan_id);
            if (!$loan) {
                return $this->returnErrorData('ไม่พบสัญญาเงินกู้', 404);
            }

            if ($loan->status !== 'active') {
                return $this->returnErrorData('สัญญาเงินกู้ไม่อยู่ในสถานะที่สามารถขอพักชำระได้', 400);
            }

            // Check if there's already a pending moratorium for this loan
            $existingMoratorium = LoanMoratorium::where('loan_id', $request->loan_id)
                ->whereNull('approved_at')
                ->first();

            if ($existingMoratorium) {
                return $this->returnErrorData('มีคำขอพักชำระที่รออนุมัติอยู่แล้วสำหรับสัญญานี้', 400);
            }

            // Calculate extra interest if accrual mode is 'accrue'
            $calculatedExtraInterest = 0;
            if ($request->interest_accrual_mode === 'accrue') {
                // Simple calculation - can be enhanced based on business logic
                $calculatedExtraInterest = ($loan->outstanding_principal * $loan->interest_rate_pa / 100 / 12) * $request->months;
            }

            $loanMoratorium = LoanMoratorium::create([
                'loan_id' => $request->loan_id,
                'start_installment' => $request->start_installment,
                'months' => $request->months,
                'interest_accrual_mode' => $request->interest_accrual_mode,
                'calculated_extra_interest' => $calculatedExtraInterest,
                'reason' => $request->reason,
            ]);

            // Log the action
            $userId = $loginBy->user_id;
            $type = 'Create Loan Moratorium';
            $description = 'User ' . $userId . ' has created loan moratorium for loan ID: ' . $request->loan_id;
            $this->Log($userId, $description, $type);

            DB::commit();

            return $this->returnSuccess('สร้างคำขอพักชำระสำเร็จ', $loanMoratorium);

        } catch (\Throwable $e) {
            DB::rollback();
            Log::error($e);
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $loanMoratorium = LoanMoratorium::with(['loan.user', 'loan.loan_schedules', 'approver'])
            ->find($id);

        if (!$loanMoratorium) {
            return $this->returnErrorData('ไม่พบคำขอพักชำระ', 404);
        }

        // Add status
        $loanMoratorium->status = $loanMoratorium->approved_at ? 'approved' : 'pending';

        // Format dates
        if ($loanMoratorium->approved_at) {
            $loanMoratorium->approved_at_formatted = Carbon::parse($loanMoratorium->approved_at)->format('d/m/Y H:i:s');
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $loanMoratorium);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'start_installment' => 'nullable|integer|min:1',
            'months' => 'nullable|integer|min:1|max:12',
            'interest_accrual_mode' => 'nullable|in:none,accrue',
            'reason' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        $loginBy = $request->login_by;
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {
            $loanMoratorium = LoanMoratorium::with('loan')->find($id);

            if (!$loanMoratorium) {
                return $this->returnErrorData('ไม่พบคำขอพักชำระ', 404);
            }

            // Check if already approved - cannot edit approved moratorium
            if ($loanMoratorium->approved_at) {
                return $this->returnErrorData('ไม่สามารถแก้ไขคำขอที่อนุมัติแล้ว', 400);
            }

            // Update fields if provided
            if ($request->has('start_installment')) {
                $loanMoratorium->start_installment = $request->start_installment;
            }
            if ($request->has('months')) {
                $loanMoratorium->months = $request->months;
            }
            if ($request->has('interest_accrual_mode')) {
                $loanMoratorium->interest_accrual_mode = $request->interest_accrual_mode;
            }
            if ($request->has('reason')) {
                $loanMoratorium->reason = $request->reason;
            }

            // Recalculate extra interest if accrual mode or months changed
            if ($request->has('interest_accrual_mode') || $request->has('months')) {
                $calculatedExtraInterest = 0;
                if ($loanMoratorium->interest_accrual_mode === 'accrue') {
                    $loan = $loanMoratorium->loan;
                    $calculatedExtraInterest = ($loan->outstanding_principal * $loan->interest_rate_pa / 100 / 12) * $loanMoratorium->months;
                }
                $loanMoratorium->calculated_extra_interest = $calculatedExtraInterest;
            }

            $loanMoratorium->save();

            // Log the action
            $userId = $loginBy->user_id;
            $type = 'Update Loan Moratorium';
            $description = 'User ' . $userId . ' has updated loan moratorium ID: ' . $id;
            $this->Log($userId, $description, $type);

            DB::commit();

            return $this->returnSuccess('แก้ไขคำขอพักชำระสำเร็จ', $loanMoratorium);

        } catch (\Throwable $e) {
            DB::rollback();
            Log::error($e);
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {
            $loanMoratorium = LoanMoratorium::find($id);

            if (!$loanMoratorium) {
                return $this->returnErrorData('ไม่พบคำขอพักชำระ', 404);
            }

            // Check if already approved - cannot delete approved moratorium
            if ($loanMoratorium->approved_at) {
                return $this->returnErrorData('ไม่สามารถลบคำขอที่อนุมัติแล้ว', 400);
            }

            $loanId = $loanMoratorium->loan_id;
            $loanMoratorium->delete();

            // Log the action
            $userId = $loginBy->user_id;
            $type = 'Delete Loan Moratorium';
            $description = 'User ' . $userId . ' has deleted loan moratorium ID: ' . $id . ' for loan ID: ' . $loanId;
            $this->Log($userId, $description, $type);

            DB::commit();

            return $this->returnSuccess('ลบคำขอพักชำระสำเร็จ', []);

        } catch (\Throwable $e) {
            DB::rollback();
            Log::error($e);
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Approve or reject loan moratorium request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function approve(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:approve,reject',
            'remark' => 'required_if:action,reject|nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        $loginBy = $request->login_by;
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {
            $loanMoratorium = LoanMoratorium::with('loan.loan_schedules')->find($id);

            if (!$loanMoratorium) {
                return $this->returnErrorData('ไม่พบคำขอพักชำระ', 404);
            }

            // Check if already processed
            if ($loanMoratorium->approved_at) {
                return $this->returnErrorData('คำขอนี้ได้รับการพิจารณาแล้ว', 400);
            }

            if ($request->action === 'approve') {
                $loanMoratorium->approved_by = $loginBy->id;
                $loanMoratorium->approved_at = Carbon::now();

                // Update loan schedules if approved
                $this->updateLoanSchedules($loanMoratorium);

                $message = 'อนุมัติคำขอพักชำระสำเร็จ';
                $logType = 'Approve Loan Moratorium';
            } else {
                // For rejection, we can add a reason field or use the remark
                $loanMoratorium->reason = $request->remark ?? $loanMoratorium->reason;
                $message = 'ปฏิเสธคำขอพักชำระสำเร็จ';
                $logType = 'Reject Loan Moratorium';

                // Delete the moratorium request for rejection
                $loanMoratorium->delete();

                // Log the action
                $userId = $loginBy->user_id;
                $description = 'User ' . $userId . ' has rejected loan moratorium ID: ' . $id;
                $this->Log($userId, $description, $logType);

                DB::commit();
                return $this->returnSuccess($message, []);
            }

            $loanMoratorium->save();

            // Log the action
            $userId = $loginBy->user_id;
            $description = 'User ' . $userId . ' has ' . strtolower($request->action) . 'd loan moratorium ID: ' . $id;
            $this->Log($userId, $description, $logType);

            DB::commit();

            return $this->returnSuccess($message, $loanMoratorium);

        } catch (\Throwable $e) {
            DB::rollback();
            Log::error($e);
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Helper method to update loan schedules when moratorium is approved.
     *
     * @param  \App\Models\LoanMoratorium  $loanMoratorium
     * @return void
     */
    private function updateLoanSchedules($loanMoratorium)
    {
        $loan = $loanMoratorium->loan;
        $startInstallment = $loanMoratorium->start_installment;
        $months = $loanMoratorium->months;

        // งวดที่จะพัก
        $schedulesToUpdate = $loan->loan_schedules()
            ->where('installment_no', '>=', $startInstallment)
            ->where('status', 'due')
            ->orderBy('installment_no')
            ->take($months)
            ->get();

        foreach ($schedulesToUpdate as $schedule) {
            if ($loanMoratorium->interest_accrual_mode === 'none') {
                $schedule->principal_due = 0;
                $schedule->interest_due = 0;
                $schedule->total_due = 0;
            }
            // ถ้า accrue จะยังคงยอด principal/interest เดิมไว้ แต่ไม่ตัด, งวดสถานะ moratorium
            $schedule->status = 'moratorium';
            $schedule->save();
        }

        // ขยับ due_date ของงวดที่เหลือถัดไป
        $remainingSchedules = $loan->loan_schedules()
            ->where('installment_no', '>', $startInstallment + $months - 1)
            ->where('status', 'due')
            ->orderBy('installment_no')
            ->get();

        foreach ($remainingSchedules as $schedule) {
            $schedule->due_date = Carbon::parse($schedule->due_date)->addMonths($months);
            $schedule->save();
        }

        // ถ้า mode=accrue และมีดอกเพิ่ม -> บวกเข้ากับ outstanding และงวดสุดท้าย
        if ($loanMoratorium->interest_accrual_mode === 'accrue' && $loanMoratorium->calculated_extra_interest > 0) {
            $loan->outstanding_principal += $loanMoratorium->calculated_extra_interest;
            $loan->save();

            $lastSchedule = $loan->loan_schedules()
                ->where('status', 'due')
                ->orderByDesc('installment_no')
                ->first();
            if ($lastSchedule) {
                $lastSchedule->interest_due += $loanMoratorium->calculated_extra_interest;
                $lastSchedule->total_due = $lastSchedule->principal_due + $lastSchedule->interest_due;
                $lastSchedule->save();
            }
        }
    }
}
