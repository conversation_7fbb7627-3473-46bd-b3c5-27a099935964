<?php

namespace App\Http\Controllers;

use App\Imports\CommissionImport;
use App\Models\Commission;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class CommissionController extends Controller
{

    public function getCommission(Request $request)
    {

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $q = Commission::where('status', 1)
            ->with('position')
            ->with('user_create');

        if ($login_branch_id) {
            $q->where('branch_id', $login_branch_id);
        }

        $Commission = $q->get()->toArray();

        if (!empty($Commission)) {

            for ($i = 0; $i < count($Commission); $i++) {
                $Commission[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $Commission);
    }

    public function CommissionPage(Request $request)
    {

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $col = array('id', 'position_id', 'start', 'end', 'percent', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Commission::select($col)->with('position')
            ->with('user_create');

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        $d->orderBy($col[$order[0]['column']], $order[0]['dir']);

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($request->position_id)) {
            return $this->returnErrorData('กรุณาเลือกตำแหน่งด้วย', 404);
        } else  if (!isset($request->start)) {
            return $this->returnErrorData('กรุณาระบุจำนวนเริ่มต้น', 404);
        } else  if (!isset($request->end)) {
            return $this->returnErrorData('กรุณาระบุจำนวนสิ้นสุด', 404);
        } else  if (!isset($request->percent)) {
            return $this->returnErrorData('กรุณาระบุจำนวน % ค่าคอมมิชชั่น', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Commission = new Commission();
            $Commission->branch_id = $login_branch_id;
            $Commission->position_id = $request->position_id;
            $Commission->start = $request->start;
            $Commission->end = $request->end;
            $Commission->percent = $request->percent;
            $Commission->status = 1;

            $Commission->create_by = $loginBy->user_id;

            $Commission->save();

            //log
            $userId = $loginBy->user_id;
            $type = 'Add Commission';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Commission->start . ' - ' . $Commission->end;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Commission = Commission::with('position')
            ->with('user_create')
            ->find($id);
        return $this->returnSuccess('Successful', $Commission);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        DB::beginTransaction();

        try {

            $Commission = Commission::find($id);

            $Commission->position_id = $request->position_id;
            $Commission->start = $request->start;
            $Commission->end = $request->end;
            $Commission->percent = $request->percent;

            $Commission->status = $request->status;

            $Commission->update_by = $loginBy->user_id;
            $Commission->updated_at = Carbon::now()->toDateTimeString();

            $Commission->save();
            //log
            $userId = $loginBy->user_id;
            $type = 'Edit Commission';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Commission->start . ' - ' . $Commission->end;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Commission = Commission::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Commission';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Commission->start . ' - ' . $Commission->end;
            $this->Log($userId, $description, $type);
            //

            $Commission->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

//     public function ImportCommission(Request $request)
//     {

//         $loginBy = $request->login_by;

//         if (!isset($loginBy)) {
//             return $this->returnErrorData('User information not found. Please login again', 404);
//         }

//         $file = request()->file('file');
//         $fileName = $file->getClientOriginalName();

//         $Data = Excel::toArray(new CommissionImport(), $file);
//         $data = $Data[0];

//         if (count($data) > 0) {

//             $insert_data = [];

//             for ($i = 0; $i < count($data); $i++) {

//                 $name = trim($data[$i]['name']);

//                 $row = $i + 2;

//                 if ($name == '') {
//                     return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
//                 }

//                 //check row sample
//                 if ($name == 'SIMPLE-000') {
//                     //
//                 } else {

//                     // //check name
//                     // $Commission = Commission::where('name', $name)->first();
//                     // if ($Commission) {
//                     //     return $this->returnErrorData('Commission ' . $name . ' was information information is already in the system', 404);
//                     // }

//                     //check dupicate data form file import
//                     for ($j = 0; $j < count($insert_data); $j++) {

//                         if ($name == $insert_data[$j]['name']) {
//                             return $this->returnErrorData('Commission ' . $name . ' There is duplicate data in the import file', 404);
//                         }
//                     }
//                     ///

//                     $insert_data[] = array(
//                         'name' => $name,
//                         'status' => 1,
//                         'created_at' => date('Y-m-d H:i:s'),
//                         'updated_at' => date('Y-m-d H:i:s'),
//                     );
//                 }
//             }

//             if (!empty($insert_data)) {

//                 DB::beginTransaction();

//                 try {

//                     //updateOrInsert
//                     for ($i = 0; $i < count($insert_data); $i++) {

//                         DB::table('Commission')
//                             ->updateOrInsert(
//                                 [
//                                     'id' => trim($data[$i]['id']), //id
//                                 ],
//                                 $insert_data[$i]
//                             );
//                     }
//                     //

//                     DB::commit();

//                     //log
//                     $userId = $loginBy->user_id;
//                     $type = 'Import Commission';
//                     $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
//                     $this->Log($userId, $description, $type);
//                     //

//                     DB::commit();

//                     return $this->returnSuccess('Successful operation', []);
//                 } catch (\Throwable $e) {

//                     DB::rollback();

//                     return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
//                 }
//             } else {
//                 return $this->returnErrorData('Data Not Found', 404);
//             }
//         } else {
//             return $this->returnErrorData('Data Not Found', 404);
//         }
//     }
}
