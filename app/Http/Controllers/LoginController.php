<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use \Firebase\JWT\JWT;

class LoginController extends Controller
{
    public $key = "gs_key";

    public function genToken($id, $user)
    {
        $payload = array(
            "iss" => "gs",
            "aud" => $id,
            "lun" => $user,
            "iat" => Carbon::now()->timestamp,
            "exp" => Carbon::now()->timestamp + 86400,
            // "exp" => Carbon::now()->timestamp + 31556926,
            "nbf" => Carbon::now()->timestamp,
        );

        $token = JWT::encode($payload, $this->key);
        return $token;
    }

    public function checkLogin(Request $request)
    {
        $header = $request->header('Authorization');
        $token = str_replace('Bearer ', '', $header);

        try {

            if ($token == "") {
                return $this->returnError('Token Not Found', 401);
            }

            $payload = JWT::decode($token, $this->key, array('HS256'));
            $payload->exp = Carbon::now()->timestamp + 86400;
            $token = JWT::encode($payload, $this->key);

            return response()->json([
                'code' => '200',
                'status' => true,
                'message' => 'Active',
                'data' => [],
                'token' => $token,
            ], 200);
        } catch (\Firebase\JWT\ExpiredException $e) {

            list($header, $payload, $signature) = explode(".", $token);
            $payload = json_decode(base64_decode($payload));
            $payload->exp = Carbon::now()->timestamp + 86400;
            $token = JWT::encode($payload, $this->key);

            return response()->json([
                'code' => '200',
                'status' => true,
                'message' => 'Token is expire',
                'data' => [],
                'token' => $token,
            ], 200);
        } catch (Exception $e) {
            return $this->returnError('Can not verify identity', 401);
        }
    }



    public function login(Request $request)
    {
        if (!isset($request->email)) {
            return $this->returnErrorData('Emailไม่ถูกต้อง', 404);
        } else if (!isset($request->password)) {
            return $this->returnErrorData('passwordไม่ถูกต้อง', 404);
        }

        $user = User::with('permission')
            ->with('branch.company')
            ->with('position')
            ->where(function ($query) use ($request) {
                $query->orWhere('user_id', $request->email);
                $query->orWhere('email', $request->email);
            })
            ->where('password', md5($request->password))
            ->where('status', 1)
            ->first();

        if ($user) {

            //log
            $username = $user->user_id;
            $log_type = 'เข้าสู่ระบบ';
            $log_description = 'ผู้ใช้งาน ' . $username . ' ได้ทำการ ' . $log_type;
            $this->Log($username, $log_description, $log_type);
            //

            return response()->json([
                'code' => '200',
                'status' => true,
                'message' => 'เข้าสู่ระบบสำเร็จ',
                'data' => $user,
                'token' => $this->genToken($user->id, [
                    'id' => $user->id,
                    'user_id' => $user->user_id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'permission_id' => $user->permission_id,
                    'head_id' => $user->head_id,
                    'work_shift_id' => $user->work_shift_id,
                    'work_shift_group_id' => $user->work_shift_group_id,
                    'permission' => $user->permission,
                    'branch' => $user->branch,
                ]),
            ], 200);
        } else {
            return $this->returnError('รหัสผู้ใช้งานหรือรหัสผ่านไม่ถูกต้อง', 401);
        }
    }

    public function loginChangeBranch(Request $request)
    {
        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($request->branch_id)) {
            return $this->returnErrorData('branch_id ไม่ถูกต้อง', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }


        $user = User::with('permission')
            ->with('position')
            ->where('id', $loginBy->id)
            ->where('status', 1)
            ->first();

        if ($user) {

            $user->branch_id = $request->branch_id;
            $getBranch = Branch::with('company')->where('id', $request->branch_id)->first();
            $user->branch =  $getBranch;

            return response()->json([
                'code' => '200',
                'status' => true,
                'message' => 'เข้าสู่ระบบสำเร็จ',
                'data' => $user,
                'token' => $this->genToken($user->id, [
                    'id' => $user->id,
                    'user_id' => $user->user_id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'permission_id' => $user->permission_id,
                    'head_id' => $user->head_id,
                    'work_shift_id' => $user->work_shift_id,
                    'work_shift_group_id' => $user->work_shift_group_id,
                    'permission' => $user->permission,
                    'branch' => $getBranch,
                ]),
            ], 200);
        } else {
            return $this->returnError('รหัสผู้ใช้งานหรือรหัสผ่านไม่ถูกต้อง', 401);
        }
    }
}
