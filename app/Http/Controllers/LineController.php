<?php

namespace App\Http\Controllers;

use App\Models\LineRichMenu;
use App\Models\User;
use App\Services\LineRichMenuService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use LINE\LINEBot\Event\MessageEvent\TextMessage;
use Revolution\Line\Facades\Bot;

class LineController extends Controller
{
    protected $richMenuService;

    public function __construct(LineRichMenuService $richMenuService)
    {
        $this->richMenuService = $richMenuService;
    }

    public function updateLine()
    {
        //get all rich menu on Line OA and delete all
        $richMenuIds = $this->richMenuService->getRichMenuList();
        if ($richMenuIds) {
            foreach ($richMenuIds as $richMenuId) {
                $this->richMenuService->deleteRichMenu($richMenuId['richMenuId']);
            }
        }

        LineRichMenu::query()->delete();

        //lang
        $arr_lang = ['TH', 'EN', 'KM', 'LO', 'MY'];

        for ($i = 0; $i < count($arr_lang); $i++) {

            // 1. กำหนดโครงสร้าง Rich Menu (จาก LINE Rich menu object documentation)
            // ตย. โครงสร้างที่ซับซ้อนขึ้น
            $richMenuData = self::richMenuData();

            // 2. สร้าง Rich Menu ID
            $richMenuId = $this->richMenuService->createRichMenu($richMenuData);

            if (!$richMenuId) {
                return response()->json(['message' => 'Failed to create Rich Menu'], 500);
            }

            $lineRichMenu = new LineRichMenu();
            $lineRichMenu->rich_menu_id = $richMenuId;
            $lineRichMenu->lang = $arr_lang[$i];
            $lineRichMenu->save();

            // 3. อัปโหลดรูปภาพสำหรับ Rich Menu
            // ต้องเตรียมรูปภาพไว้ใน Storage หรือรับจาก Request

            if ($arr_lang[$i] == 'TH') {
                $imagePath = resource_path("project/" . config('project.customer') . "/rich_menu/images/staffTh.png"); // สมมติว่าคุณมีรูปนี้ใน public/images
            } else  if ($arr_lang[$i] == 'EN') {
                $imagePath = resource_path("project/" . config('project.customer') . "/rich_menu/images/staffEn.png");
            } else  if ($arr_lang[$i] == 'KM') {
                $imagePath = resource_path("project/" . config('project.customer') . "/rich_menu/images/staffBu.png");
            } else  if ($arr_lang[$i] == 'LO') {
                $imagePath = resource_path("project/" . config('project.customer') . "/rich_menu/images/staffLao.png");
            } else  if ($arr_lang[$i] == 'MY') {
                $imagePath = resource_path("project/" . config('project.customer') . "/rich_menu/images/staffMm.png");
            }

            $contentType = 'image/png'; // หรือ 'image/jpeg'

            if (!file_exists($imagePath)) {
                return response()->json(['message' => 'Rich Menu image not found'], 500);
            }
            if (!$this->richMenuService->uploadRichMenuImage($richMenuId, $imagePath, $contentType)) {
                return response()->json(['message' => 'Failed to upload Rich Menu image'], 500);
            }

            $line_ids = User::whereNotNull('line_id')->where('line_lang', $arr_lang[$i])->get()->map(function ($user) {
                return $user->line_id;
            })->toArray();

            if (count($line_ids) > 0 && !$this->richMenuService->linkRichMenuToUsers($line_ids, $richMenuId)) {
                return response()->json(['message' => 'Rich Menu created but failed to link to user'], 500);
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Rich Menu created and linked successfully!',
        ]);
    }

    public function richMenuData()
    {
        $path = resource_path("project/" . config('project.customer') . "/rich_menu/richMenuData.json");

        if (!file_exists($path)) {
            // กรณีหาไม่เจอให้แจ้ง error
            throw new \Exception("richMenuData.json not found at $path");
        }

        $json = file_get_contents($path);
        return json_decode($json, true);
    }

    // public function richMenuData()
    // {
    //     return [
    //         'size' => [
    //             'width' => 2500,
    //             'height' => 1686
    //         ],
    //         'selected' => true,
    //         'name' => "staff richmenu",
    //         'chatBarText' => "showMenu",
    //         'areas' => [
    //             [
    //                 'bounds' => [
    //                     'x' => 0,
    //                     'y' => 0,
    //                     'width' => 832,
    //                     'height' => 744
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'history',
    //                     'text' => 'ประวัติการขออนุมัติ'
    //                 ]

    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 843,
    //                     'y' => 0,
    //                     'width' => 788,
    //                     'height' => 745
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'request',
    //                     'text' => 'ขออนุมัติ'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 1657,
    //                     'y' => 0,
    //                     'width' => 843,
    //                     'height' => 737
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'attendance',
    //                     'text' => 'ข้อมูลการลงเวลา'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 0,
    //                     'y' => 755,
    //                     'width' => 822,
    //                     'height' => 703
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'slip',
    //                     'text' => 'สลิปเงินเดือน'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 852,
    //                     'y' => 763,
    //                     'width' => 775,
    //                     'height' => 700
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'checkin',
    //                     'text' => 'ลงเวลา'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 1665,
    //                     'y' => 767,
    //                     'width' => 835,
    //                     'height' => 695
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'other',
    //                     'text' => 'อื่นๆ'
    //                 ]
    //             ]
    //             // ,
    //             // [
    //             //     'bounds' => [
    //             //         'x' => 0,
    //             //         'y' => 1470,
    //             //         'width' => 2500,
    //             //         'height' => 216
    //             //     ],
    //             //     'action' => [
    //             //         'type' => 'message',
    //             //         'label' => 'change lang',
    //             //         'text' => 'เปลี่ยนภาษา'
    //             //     ]
    //             // ]
    //         ]
    //     ];
    // }

    // public function richMenuData()
    // {
    //     return [
    //         'size' => [
    //             'width' => 2500,
    //             'height' => 1686
    //         ],
    //         'selected' => true,
    //         'name' => "staff richmenu",
    //         'chatBarText' => "showMenu",
    //         'areas' => [
    //             [
    //                 'bounds' => [
    //                     'x' => 0,
    //                     'y' => 0,
    //                     'width' => 832,
    //                     'height' => 744
    //                 ],
    //                 'action' => [
    //                     'type' => 'uri',
    //                     'label' => 'request ot',
    //                     'uri' => 'https://liff.line.me/2007390860-X2qMdGjL'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 843,
    //                     'y' => 0,
    //                     'width' => 788,
    //                     'height' => 745
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'show history menu',
    //                     'text' => 'ประวัติการลา'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 1657,
    //                     'y' => 0,
    //                     'width' => 843,
    //                     'height' => 737
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'my attendance',
    //                     'text' => 'วันนี้'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 0,
    //                     'y' => 755,
    //                     'width' => 822,
    //                     'height' => 703
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'check in',
    //                     'text' => 'เปลี่ยนภาษา'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 852,
    //                     'y' => 763,
    //                     'width' => 775,
    //                     'height' => 700
    //                 ],
    //                 // 'action' => [
    //                 //     'type' => 'uri',
    //                 //     'label' => 'leave/onsite',
    //                 //     'uri' => 'https://liff.line.me/2007390860-RXDjZMYo'
    //                 //     // 'text' => 'ลา'
    //                 // ]
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'สิทธิ์การลา',
    //                     'text' => 'สิทธิ์การลา'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 1665,
    //                     'y' => 767,
    //                     'width' => 835,
    //                     'height' => 695
    //                 ],
    //                 'action' => [
    //                     'type' => 'uri',
    //                     'label' => 'checkin - onsite',
    //                     'uri' => 'https://liff.line.me/2007390860-VJY46w5A'
    //                     // 'uri' => 'LINE_LIFF_ONSITE_URL'
    //                     // 'text' => 'นอกสถานที่'
    //                 ]
    //             ],
    //             [
    //                 'bounds' => [
    //                     'x' => 0,
    //                     'y' => 1470,
    //                     'width' => 2500,
    //                     'height' => 216
    //                 ],
    //                 'action' => [
    //                     'type' => 'message',
    //                     'label' => 'change lang',
    //                     'text' => 'เปลี่ยนภาษา'
    //                 ]
    //             ]
    //         ]
    //     ];
    // }
}
