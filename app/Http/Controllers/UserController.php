<?php

namespace App\Http\Controllers;

use App\Exports\UserExport;
use App\Imports\UserImport;
use App\Models\Branch;
use App\Models\DeductPaid;
use App\Models\DeductType;
use App\Models\Department;
use App\Models\Employee_salary;
use App\Models\IncomePaid;
use App\Models\IncomeType;
use App\Models\Leave_permission;
use App\Models\LeaveType;
use App\Models\LoanPolicyPersonalLimit;
use App\Models\PayrollContribution;
use App\Models\PayrollRound;
use App\Models\Permission;
use App\Models\Position;
use App\Models\User;
use App\Models\UserDeduct;
use App\Models\User_attendance;
use App\Models\User_leave_permission;
use App\Models\UserFiles;
use App\Models\UserIncome;
use App\Models\Work_shift_time;
use App\Models\Work_times;
use App\Models\Zk_time;
use App\Services\LineRichMenuService;
use Carbon\Carbon;
use finfo;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class UserController extends Controller
{
    protected $richMenuService;

    public function __construct(LineRichMenuService $richMenuService)
    {
        $this->richMenuService = $richMenuService;
    }

    public function getUser(Request $request)
    {
        // $position = $request->position;

        $loginBy = $request->login_by;
        $is_head = $request->is_head;
        $position = $request->position;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->dropdownUser($loginBy, $request);
    }

    public function getUserLine(Request $request)
    {
        $line_id = $request->line_id;
        $is_head = $request->is_head;
        $position = $request->position;

        if (!isset($line_id)) {
            return $this->returnErrorData('[line_id] Data Not Found', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();

        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $getPermissionViewData = $this->getPermissionViewData($loginBy);

        $request->request->add([
            'login_id' => $getPermissionViewData['login_id'],
            'login_by' => $getPermissionViewData['login_by'],
            'login_permission_view' => $getPermissionViewData['login_permission_view'],
            'login_user_id' => $getPermissionViewData['login_user_id'],
            'login_branch_id' => $getPermissionViewData['login_branch_id'],
            'login_company_id' => $getPermissionViewData['login_company_id'],
        ]);


        return $this->dropdownUser($loginBy, $request);
    }

    public function dropdownUser($loginBy, $request)
    {
        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $position = $request->position;
        $is_head = $request->is_head;


        // $User = User::get()->toarray();

        $user = User::with('Position');

        if ($position) {
            $user->where('position_id', $position);
        }
        // if ($login_company_id) {
        //     $user->WhereHas('branch', function ($query) use ($login_company_id) {
        //         $query->where('company_id', $login_company_id);
        //     });
        // }

        if ($is_head) {
            if ($is_head == 'true' || $is_head == '1' || $is_head == true || $is_head == 1) {
                $user->where('is_head', 1);
            } else {
                $user->where('is_head', 0);
            }
        }

        if ($login_branch_id) {
            $user->where('branch_id', $login_branch_id);
        }

        $User = $user->get()->toarray();
        if (!empty($User)) {

            for ($i = 0; $i < count($User); $i++) {
                $User[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $User);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณากรอกรหัสพนักงาน', 404);
        } else if (!isset($request->first_name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อ', 404);
        } else if (!isset($request->last_name)) {
            return $this->returnErrorData('กรุณากรอกนามสกุล', 404);
        } else if (!isset($request->email)) {
            return $this->returnErrorData('กรุณากรอกอีเมล์', 404);
        } else if (!isset($request->password)) {
            return $this->returnErrorData('กรุณากรอกรหัสผ่าน', 404);
        }
        // else if (!isset($request->image)) {
        //     return $this->returnErrorData('กรุณาเพิ่มรูปโปรไฟล์', 404);
        // }
        else if (!isset($request->salary)) {
            return $this->returnErrorData('กรุณาระบุเงินเดือน', 404);
        }
        // else if (!isset($request->image_signature)) {
        //     return $this->returnErrorData('กรุณาเพิ่มรูปลายเซ็น', 404);
        // }
        else if (!isset($request->position_id)) {
            return $this->returnErrorData('กรุณาระบุตำแหน่ง', 404);
        } else if (!isset($request->branch_id)) {
            return $this->returnErrorData('กรุณาระบุสาขา', 404);
        } else if (!isset($request->register_date)) {
            return $this->returnErrorData('กรุณาระบุวันที่เริ่มทำงาน', 404);
        } else if (!isset($request->sex)) {
            return $this->returnErrorData('กรุณาระบุเพศ', 404);
        } else if (!isset($request->sync_olaf_status)) {
            return $this->returnErrorData('กรุณาระบุ sync_olaf_status', 404);
        }

        // else if (!isset($request->olaf_department_id)) {
        //     return $this->returnErrorData('กรุณาระบุแผนกใน Olaf', 404);
        // } else if (!isset($request->olaf_group_id)) {
        //     return $this->returnErrorData('กรุณาระบุกลุ่มใน Olaf', 404);
        // } else if (!isset($request->olaf_role)) {
        //     return $this->returnErrorData('กรุณาระบุตำแหน่งใน Olaf', 404);
        // }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $checkName = User::where(function ($query) use ($request) {

            $query->orwhere('email', $request->email)
                ->orWhere('user_id', $request->user_id);
        })
            ->first();

        if ($checkName) {
            return $this->returnErrorData('มีเจ้าหน้าที่นี้ในระบบแล้ว', 404);
        } else {

            DB::beginTransaction();

            try {

                //
                $User = new User();
                $User->user_id = $request->user_id;

                $User->password = md5($request->password);

                $User->prefix = $request->prefix;
                $User->first_name = $request->first_name;
                $User->last_name = $request->last_name;
                $User->prefix_en = $request->prefix_en;
                $User->first_name_en = $request->first_name_en;
                $User->last_name_en = $request->last_name_en;
                $User->nickname = $request->nickname;

                $User->email = $request->email;
                $User->citizen_no = $request->citizen_no;
                $User->passport_no = $request->passport_no;
                $User->nationality = $request->nationality;

                $User->phone_no = $request->phone_no;
                $User->birthday = $request->birthday;

                $User->work_type = $request->work_type; /// ประเภทงาน จ่ายรายวัน หรือ จ่ายรายเดือน
                $User->permission_id = $request->permission_id;
                $User->department_id = $request->department_id;
                $User->position_id = $request->position_id;
                $User->branch_id = $request->branch_id;
                if ($request->image && $request->image != null && $request->image != 'null') {
                    $User->image = $this->uploadImage($request->image, '/images/users/');
                }
                if ($request->image_signature && $request->image_signature != 'null' && $request->image_signature != null) {
                    $User->image_signature = $this->uploadImage($request->image_signature, '/images/users_signature/');
                }

                $User->salary = $request->salary;

                $User->bank_name = $request->bank_name;
                $User->account_no = $request->account_no;
                $User->account_name = $request->account_name;

                $User->register_date = $request->register_date;
                $User->end_date = $request->end_date;
                $User->sex = $request->sex;
                $User->personnel_id = $request->personnel_id;

                $User->home_address = $request->home_address;
                $User->district = $request->district;
                $User->subdistrict = $request->subdistrict;
                $User->province = $request->province;
                $User->zip_code = $request->zip_code;

                $User->work_status = $request->work_status ?? 'ปกติ'; //ปกติ พักงาน ลาออก

                //olaf
                $User->olaf_department_id = $request->olaf_department_id;
                $User->olaf_group_id = $request->olaf_group_id;
                $User->olaf_role = $request->olaf_role;

                $User->work_shift_id = $request->work_shift_id;
                $User->head_id = $request->head_id;

                $User->sso_status = $request->sso_status;
                $User->pvd_status = $request->pvd_status;

                $User->designated_persons = false;

                $User->payroll_status = 1;

                $User->status = 1;
                $User->create_by = $loginBy->user_id;

                // พนักงานใหม่ ยังไม่จ่ายเงินประกัน
                $User->is_bond_complete = 0;
                $User->is_head = $request->is_head ?? 0;

                $User->save();


                if ($request->has('files')) {
                    foreach ($request->input('files') as $index => $fileMeta) {
                        // ดึงชื่อไฟล์จาก input
                        $name = $fileMeta['name'] ?? null;

                        // ดึงไฟล์จาก form-data
                        $uploadedFile = $request->file("files.$index.data");

                        if ($uploadedFile && $name) {
                            $UserFile = new UserFiles();
                            $UserFile->user_id = $User->id;
                            $UserFile->name = $name;
                            $UserFile->data = $this->uploadFile($uploadedFile, '/files/user/');
                            $UserFile->created_at = Carbon::now()->toDateTimeString();
                            $UserFile->save();
                        }
                    }
                }

                ////////////////////////// zk upload image ////////////////////////////////////////////////

                $dataUrl = null;
                if ($request->image && $request->image != null && $request->image != 'null') {
                    $dataUrl =  $this->ImageUrlToImageBase64($User->image);
                }


                $fullName = $User->first_name . ' ' . $User->last_name;

                $startTime = $User->register_date;

                $registerDate = Carbon::parse($startTime);
                $newDate = $registerDate->addYears(20);
                $endTime = $newDate->format('Y-m-d');

                if ($dataUrl) {

                    $data = [
                        "userNo" => $User->user_id,
                        "name" => $fullName,
                        "startTime" => $startTime,
                        "endTime" => $endTime,
                        "file" => ($dataUrl ?  $dataUrl : ""),
                    ];

                    $zkController = new ZkController();
                    $x = $zkController->uploadImg($data);

                    if ($x) {
                        if ($x['status'] == 200) {
                            $User->zk_sn = $x['sn'];
                            $User->zk_cmd_id = $x['cmd_id'];
                            $User->zk_device_code = $x['device_code'];
                            $User->save();
                        } else {
                            return $this->returnErrorData('ไม่สามารถอัพโหลดรูปไปยังเครื่องบันทึกเวลาทำงานได้ กรุณาลองใหม่อีกครั้ง', 404);
                        }
                    } else {
                        return $this->returnErrorData('ไม่สามารถอัพโหลดรูปไปยังเครื่องบันทึกเวลาทำงานได้ กรุณาลองใหม่อีกครั้ง', 404);
                    }
                }
                ////////////////////////////////////////////////////////////////////////////////////////////////////////


                //leave permission
                $sex = ['all', $User->sex];
                $permission = [];
                $LeaveType = LeaveType::whereIn('sex', $sex)
                    ->where('branch_id', $login_branch_id)
                    ->get();

                for ($i = 0; $i < count($LeaveType); $i++) {

                    //permission
                    $User->work_day = $this->dateDiff(date('Y-m-d'), $User->register_date);
                    $work_year = intval($User->work_day / 365);

                    $Leave_permission = Leave_permission::where('year', '<=',  $work_year)
                        ->where('leave_type_id', $LeaveType[$i]->id)
                        ->orderby('year', 'desc')
                        ->first();

                    $qtyPermission = 0;
                    if ($Leave_permission) {
                        $qtyPermission = $Leave_permission->qty;
                    }
                    //

                    // $permission[] = [
                    //     "id" => null,
                    //     "user_id" => $User->id,
                    //     "leave_type_id" => $LeaveType[$i]->id,
                    //     "year" => $year,
                    //     "qty" => $qtyPermission,
                    //     "leave_type" => $LeaveType[$i],
                    //     "leave_permission_balance" => $this->qtyLeavePermissionUser($User->id, $LeaveType[$i]->id, 0, $qty_hour_work_shift)
                    // ];


                    //new
                    $year = date('Y');
                    $User_leave_permission = new User_leave_permission();

                    $User_leave_permission->year = $year;

                    $User_leave_permission->user_id = $User->id;
                    $User_leave_permission->leave_type_id = $LeaveType[$i]->id;
                    $User_leave_permission->qty =   $qtyPermission;

                    $User_leave_permission->status = true;
                    $User_leave_permission->create_by = $loginBy->user_id;
                    $User_leave_permission->updated_at = Carbon::now()->toDateTimeString();

                    $User_leave_permission->save();
                }
                //

                //log
                $userId = "admin";
                $type = 'เพิ่ม admin';
                $description = 'เจ้าหน้าที่ ' . $userId . ' ได้ทำการ ' . $type . ' ' . $request->user_id;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('ดำเนินการสำเร็จ', $User);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $loginBy = $request->login_by;
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;


        $year = date('Y');

        $User = User::with('branch')
            ->with('position')
            ->with('work_shift')
            ->with('head')
            ->with('user_files')
            // ->with('zk_times')
            ->with(['user_leave_permissions' => function ($query) use ($year) {
                $query->where('year', $year)
                    ->join('leave_types', 'user_leave_permissions.leave_type_id', '=', 'leave_types.id')
                    ->orderBy('leave_types.id')
                    ->select('user_leave_permissions.*')
                    ->with('leave_type');
            }])
            ->with('loans')
            ->with('loanApplications')
            ->with('loanMoratoriums')
            ->with('loanPolicyPersonalLimit')
            ->where('id', $id)
            ->first();

        if ($User) {
            //age work_day
            $User->work_day = $this->dateDiff(date('Y-m-d'), $User->register_date);

            //
            $sex = ['all', $User->sex];

            //work shift
            $qty_hour_work_shift = 0;
            if ($User->work_shift) {

                $time_1 =   $User->work_shift->work_shift_times[0]->time_in;
                $time_2 = $User->work_shift->work_shift_times[0]->time_out;

                $qty_hour_work_shift += ($this->TimeDiff($time_1, $time_2) - 1);
            }

            //permission
            if ($User->user_leave_permissions->isEmpty()) {

                $permission = [];
                $LeaveType = LeaveType::whereIn('sex', $sex)
                    ->where('branch_id', $login_branch_id)
                    ->get();

                for ($i = 0; $i < count($LeaveType); $i++) {

                    //permission
                    $User->work_day = $this->dateDiff(date('Y-m-d'), $User->register_date);
                    $work_year = intval($User->work_day / 365);

                    $Leave_permission = Leave_permission::where('year', '<=',  $work_year)
                        ->where('leave_type_id', $LeaveType[$i]->id)
                        ->orderby('year', 'desc')
                        ->first();

                    $qtyPermission = 0;
                    if ($Leave_permission) {
                        $qtyPermission = $Leave_permission->qty;
                    }
                    //

                    $permission[] = [
                        "id" => null,
                        "user_id" => $User->id,
                        "leave_type_id" => $LeaveType[$i]->id,
                        "year" => $year,
                        "qty" => $qtyPermission,
                        "leave_type" => $LeaveType[$i],
                        "leave_permission_balance" => $this->qtyLeavePermissionUser($User->id, $LeaveType[$i]->id, 0, $qty_hour_work_shift)
                    ];
                }
                $User->leave_permissions =  $permission;
            } else {

                for ($i = 0; $i < count($User->user_leave_permissions); $i++) {
                    $User->user_leave_permissions[$i]->leave_permission_balance =  $this->qtyLeavePermissionUser($User->id, $User->user_leave_permissions[$i]->leave_type_id, $User->user_leave_permissions[$i]->qty, $qty_hour_work_shift);
                }
                $User->leave_permissions = $User->user_leave_permissions;
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $User);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        //
    }

    public function getProfileUser(Request $request)
    {
        $round = $request->round;
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่ กรุณาเข้าสู่ระบบใหม่อีกครั้ง', 404);
        }

        $user_id = $loginBy->id;
        return  $this->ProfileUser($user_id, $request, $loginBy);
    }

    public function getProfileUserLine(Request $request)
    {
        $round = $request->round;
        $line_id = $request->line_id;
        if (!isset($line_id)) {
            return $this->returnErrorData('ระบุ line_id', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();

        if (!$loginBy) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่', 404);
        }

        $user_id = $loginBy->id;
        return  $this->ProfileUser($user_id, $request, $loginBy);
    }

    public function ProfileUser($user_id, $request, $loginBy)
    {

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $round = $request->round;
        $year = date('Y');
        $month = date('m');

        if (!$year || !$month) {
            return response()->json(['error' => 'กรุณาระบุปีและเดือน'], 400);
        }

        $date_start = date('Y-m-01', strtotime("$year-$month-01"));
        $date_end = date('Y-m-t', strtotime("$year-$month-01"));

        if ($round) {
            $PayrollRound =  PayrollRound::where('round', $round)->first();

            $start_day = $PayrollRound->start_day;
            $end_day = $PayrollRound->end_day;

            $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
            $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

        }


        $User = User::with('permission')
            ->with('department')
            ->with('position')
            ->with('branch')
            ->with('work_shift')
            ->with('head')
            // ->with('position')
            // ->with('prefix_type')
            // ->with('prefix')
            ->with(['user_leave_permissions' => function ($query) use ($year) {
                $query->where('year', $year)
                    ->join('leave_types', 'user_leave_permissions.leave_type_id', '=', 'leave_types.id')
                    ->orderBy('leave_types.id')
                    ->select('user_leave_permissions.*')
                    ->with('leave_type');
            }])
            ->where('id', $user_id)
            ->first();

        $User->income = IncomePaid::where('user_id', $User->id)->get();
        $User->deduct = DeductPaid::where('user_id', $User->id)->get();

        $User->total_income = IncomePaid::where('user_id', $User->id)->sum('price');
        $User->total_deduct = DeductPaid::where('user_id', $User->id)->sum('price');

        //commission
        // $User->total_commission = $this->getCommisionUser($User->id, $round);
        $User->total_commission = 0;

        //ot
        $User->total_ot = $this->getOtUser($User->id, $date_start, $date_end);


        //withdraw salary
        $User->total_withdraw_salary = $this->getWithdrawSalaryUser($User->id, $date_start, $date_end);

        $User->total = ($User->salary + $User->total_income + $User->total_commission + $User->total_ot) - $User->total_deduct - $User->total_withdraw_salary;

        //
        $sex = ['all', $User->sex];

        //permission
        if ($User->user_leave_permissions->isEmpty()) {

            $permission = [];
            $LeaveType = LeaveType::whereIn('sex', $sex)
                ->where('branch_id', $login_branch_id)
                ->get();

            for ($i = 0; $i < count($LeaveType); $i++) {
                $permission[] = [
                    "id" => null,
                    "user_id" => $User->id,
                    "leave_type_id" => $LeaveType[$i]->id,
                    "year" => $year,
                    "qty" => 0,
                    "hr_leave_type" => $LeaveType[$i],
                    "leave_permission_balance" => $this->qtyLeavePermissionUser($loginBy->id, $LeaveType[$i]->id, 0)
                ];
            }
            $User->leave_permissions =  $permission;
        } else {

            for ($i = 0; $i < count($User->user_leave_permissions); $i++) {
                $User->user_leave_permissions[$i]->leave_permission_balance =  $this->qtyLeavePermissionUser($loginBy->id, $User->user_leave_permissions[$i]->leave_type_id, $User->user_leave_permissions[$i]->qty);
            }
            $User->leave_permissions = $User->user_leave_permissions;
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $User);
    }

    public function updateProfileUser(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($request->id)) {
            return $this->returnErrorData('ไม่พบข้อมูล id', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่ กรุณาเข้าสู่ระบบใหม่อีกครั้ง', 404);
        }

        DB::beginTransaction();

        try {

            $id = $request->id;
            $User = User::find($id);

            // $User->work_type = $request->work_type;

            // $User->prefix = $request->prefix;
            // $User->first_name = $request->first_name;

            // $User->last_name = $request->last_name;

            // $User->email = $request->email;

            if ($request->image && $request->image != null && $request->image != 'null') {
                $User->image = $this->uploadImage($request->image, '/images/users/');
            }
            // if ($request->image_signature && $request->image_signature != 'null' && $request->image_signature != null) {
            //     $User->image_signature = $this->uploadImage($request->image_signature, '/images/users_signature/');
            // }
            $User->update_by = $loginBy->user_id;
            $User->updated_at = Carbon::now()->toDateTimeString();

            $User->save();

            //log
            $userId = $loginBy->user_id;
            $type = 'แก้ไขโปรไฟล์เจ้าหน้าที่';
            $description = 'เจ้าหน้าที่ ' . $userId . ' ได้ทำการ ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('ดำเนินการสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }
    }

    public function updateUser(Request $request)
    {

        $status = $request->status;
        $loginBy = $request->login_by;

        if (!isset($request->id)) {
            return $this->returnErrorData('ไม่พบข้อมูล id', 404);
        }

        DB::beginTransaction();

        try {

            //
            $id = $request->id;
            $User = User::find($id);

            // $User->user_id = $request->user_id;

            $User->prefix = $request->prefix;
            $User->first_name = $request->first_name;
            $User->last_name = $request->last_name;
            $User->prefix_en = $request->prefix_en;
            $User->first_name_en = $request->first_name_en;
            $User->last_name_en = $request->last_name_en;
            $User->nickname = $request->nickname;

            $User->work_type = $request->work_type;
            $User->email = $request->email;

            $User->citizen_no = $request->citizen_no;
            $User->passport_no = $request->passport_no;
            $User->nationality = $request->nationality;

            $User->phone_no = $request->phone_no;
            $User->birthday = $request->birthday;

            $User->position_id = $request->position_id;
            $User->department_id = $request->department_id;
            $User->permission_id = $request->permission_id;
            $User->branch_id = $request->branch_id;
            if ($request->image && $request->image != null && $request->image != 'null') {
                $User->image = $this->uploadImage($request->image, '/images/users/');
            }
            if ($request->image_signature && $request->image_signature != 'null' && $request->image_signature != null) {
                $User->image_signature = $this->uploadImage($request->image_signature, '/images/users_signature/');
            }
            $User->salary = $request->salary;

            $User->bank_name = $request->bank_name;
            $User->account_no = $request->account_no;
            $User->account_name = $request->account_name;

            $User->register_date = $request->register_date;
            $User->end_date = $request->end_date;
            $User->sex = $request->sex;

            $User->personnel_id = $request->personnel_id;

            $User->home_address = $request->home_address;
            $User->district = $request->district;
            $User->subdistrict = $request->subdistrict;
            $User->province = $request->province;
            $User->zip_code = $request->zip_code;

            $User->work_status = $request->work_status; //ปกติ พักงาน ลาออก

            //olaf
            $User->olaf_department_id = $request->olaf_department_id;
            $User->olaf_group_id = $request->olaf_group_id;
            $User->olaf_role = $request->olaf_role;

            $User->work_shift_id = $request->work_shift_id;
            $User->head_id = $request->head_id;

            $User->sso_status = $request->sso_status;
            $User->pvd_status = $request->pvd_status;

            $User->designated_persons = $request->designated_persons;


            //payroll_status
            if ($request->payroll_status != 'false') {
                $User->payroll_status = 1;
            } else {
                $User->payroll_status = 0;
            }
            //

            //status
            if ($request->status != 'false') {
                $User->status = 1;
            } else {
                $User->status = 0;
            }
            //

            $User->create_by = $loginBy->user_id;

            $User->is_head = $request->is_head;

            $User->save();

            ////////////////////////// zk upload image ////////////////////////////////////////////////

            $dataUrl = null;
            if ($request->image && $request->image != null && $request->image != 'null') {

                $dataUrl =  $this->ImageUrlToImageBase64($User->image);

                $fullName = $User->first_name . ' ' . $User->last_name;

                $startTime = $User->register_date;

                $registerDate = Carbon::parse($startTime);
                $newDate = $registerDate->addYears(20);
                $endTime = $newDate->format('Y-m-d');

                $data = [
                    "userNo" => $User->user_id,
                    "name" => $fullName,
                    "startTime" => $startTime,
                    "endTime" => $endTime,
                    "file" => ($dataUrl ?  $dataUrl : ""),
                ];

                $zkController = new ZkController();
                $x = $zkController->uploadImg($data);

                if ($x) {
                    if ($x['status'] == 200) {
                        $User->zk_sn = $x['sn'];
                        $User->zk_cmd_id = $x['cmd_id'];
                        $User->zk_device_code = $x['device_code'];
                        $User->save();
                    } else {
                        return $this->returnErrorData('ไม่สามารถอัพโหลดรูปไปยังเครื่องบันทึกเวลาทำงานได้ กรุณาลองใหม่อีกครั้ง', 404);
                    }
                } else {
                    return $this->returnErrorData('ไม่สามารถอัพโหลดรูปไปยังเครื่องบันทึกเวลาทำงานได้ กรุณาลองใหม่อีกครั้ง', 404);
                }
            }
            ////////////////////////////////////////////////////////////////////////////////////////////////////////

            //log
            $userId = "admin";
            $type = 'แก้ไข admin';
            $description = 'เจ้าหน้าที่ ' . $userId . ' ได้ทำการ ' . $type . ' ' . $User->id;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่ กรุณาเข้าสู่ระบบใหม่อีกครั้ง', 404);
        }

        DB::beginTransaction();

        try {

            $User = User::find($id);

            //////////////// zk ////////////////////////////////
            $data = [
                "userNo" => $User->user_id,
                "sns" => [$User->zk_sn],
            ];

            $zkController = new ZkController();
            $x = $zkController->delete($data);

            /////////////////////////////////////////////////////

            $User->user_id = $User->user_id . '_del_' . date('YmdHis');
            $User->save();

            //log
            $userId = $loginBy->user_id;
            $type = 'ลบเจ้าหน้าที่';
            $description = 'เจ้าหน้าที่ ' . $userId . ' ได้ทำการ ' . $type . ' ' . $User->user_id;
            $this->Log($userId, $description, $type);
            //

            $User->delete();

            DB::commit();

            return $this->returnUpdate('ดำเนินการสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    public function updatePasswordUser(Request $request, $id)
    {

        $password = $request->password;

        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('ไม่พบ id', 404);
        } else if (!isset($password)) {
            return $this->returnErrorData('กรุณาระบุรหัสผ่านใหม่ให้เรียบร้อย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่ กรุณาเข้าสู่ระบบใหม่อีกครั้ง', 404);
        }

        if (strlen($password) < 6) {
            return $this->returnErrorData('กรุณาระบุรหัสผ่านอย่างน้อย 6 หลัก', 404);
        }

        DB::beginTransaction();

        try {

            $User = User::find($id);
            $User->password = md5($password);

            $User->save();

            //log
            $userId = $loginBy->user_id;
            $type = 'เปลื่ยนหรัสผ่าน';
            $description = 'เจ้าหน้าที่ ' . $userId . ' ได้ทำการ ' . $type . ' ของ ' . $User->user_id;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('ดำเนินการสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    public function deleteUser(Request $request, $id)
    {

        $loginBy = $request->login_by;
        //dd($loginBy);
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] ไม่มีข้อมูล', 404);
        }

        DB::beginTransaction();

        try {

            $User = User::find($id);

            //////////////// zk ////////////////////////////////
            $data = [
                "userNo" => $User->user_id,
                "sns" => [$User->zk_sn],
            ];

            $zkController = new ZkController();
            $x = $zkController->delete($data);

            /////////////////////////////////////////////////////

            //edit user_id
            $User->user_id = 'del_' . date('Ymdhis') . '_' . $User->user_id;
            $User->save();
            //
            //log
            $userId = $loginBy->user_id;
            //$userId = $loginBy;
            $type = 'ลบUser';
            $description = 'เจ้าหน้าที่ ' . $userId . ' ได้ทำการ ' . $type . ' ลบ ' . $User->user_id;
            $this->Log($userId, $description, $type);
            //

            $User->delete();

            DB::commit();

            return $this->returnUpdate('ดำเนินการลบสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('ดำเนินการลบUserผิดพลาด ' . $e, 404);
        }
    }

    public function createUserAdmin(Request $request)
    {

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณากรอกรหัสพนักงาน', 404);
        } else if (!isset($request->first_name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อ', 404);
        } else if (!isset($request->last_name)) {
            return $this->returnErrorData('กรุณากรอกนามสกุล', 404);
        } else if (!isset($request->email)) {
            return $this->returnErrorData('กรุณากรอกอีเมล์', 404);
        } else if (!isset($request->password)) {
            return $this->returnErrorData('กรุณากรอกรหัสผ่าน', 404);
        } else if (!isset($request->image)) {
            return $this->returnErrorData('กรุณาเพิ่มรูป', 404);
        } else if (!isset($request->image_signature)) {
            return $this->returnErrorData('กรุณาเพิ่มรูปลายเซ็น', 404);
        } else if (!isset($request->department_id)) {

            return $this->returnErrorData('กรุณาระบุแผนก', 404);
        } else if (!isset($request->position_id)) {
            return $this->returnErrorData('กรุณาระบุตำแหน่ง', 404);
        } else if (!isset($request->branch_id)) {
            return $this->returnErrorData('กรุณาระบุสาขา', 404);
        } else if (!isset($request->permission_id)) {
            return $this->returnErrorData('กรุณาระบุสิทธิ์ผู้ใช้งาน', 404);
        } else if (!isset($request->register_date)) {
            return $this->returnErrorData('กรุณาระบุวันที่เริ่มทำงาน', 404);
        } else if (!isset($request->sex)) {
            return $this->returnErrorData('กรุณาระบุเพศ', 404);
        }

        $checkName = User::where(function ($query) use ($request) {

            $query->orwhere('email', $request->email)
                ->orWhere('user_id', $request->user_id);
        })
            ->first();

        if ($checkName) {
            return $this->returnErrorData('มีเจ้าหน้าที่นี้ในระบบแล้ว', 404);
        } else {

            DB::beginTransaction();

            try {

                //
                $User = new User();
                $User->user_id = $request->user_id;

                $User->password = md5($request->password);

                $User->prefix = $request->prefix;
                $User->first_name = $request->first_name;
                $User->last_name = $request->last_name;
                $User->email = $request->email;

                $User->citizen_no = $request->citizen_no;
                $User->passport_no = $request->passport_no;
                $User->nationality = $request->nationality;

                $User->phone_no = $request->phone_no;
                $User->birthday = $request->birthday;

                $User->work_type = $request->work_type;
                $User->permission_id = $request->permission_id;
                $User->department_id = $request->department_id;
                $User->position_id = $request->position_id;
                $User->branch_id = $request->branch_id;
                $User->image = $this->uploadImage($request->image, '/images/users/');
                $User->image_signature = $this->uploadImage($request->image_signature, '/images/users_signature/');

                $User->salary = $request->salary;

                $User->bank_name = $request->bank_name;
                $User->account_no = $request->account_no;
                $User->account_name = $request->account_name;

                $User->register_date = $request->register_date;
                $User->end_date = $request->end_date;
                $User->sex = $request->sex;

                $User->work_status = $request->work_status ?? 'ปกติ'; //ปกติ พักงาน ลาออก

                //olaf
                $User->olaf_department_id = $request->olaf_department_id;
                $User->olaf_group_id = $request->olaf_group_id;
                $User->olaf_role = $request->olaf_role;

                $User->work_shift_id = $request->work_shift_id;
                $User->head_id = $request->head_id;

                $User->designated_persons = false;

                $User->status = 1;
                $User->create_by = "admin";

                $User->is_head = $request->is_head ?? false;

                $User->save();

                //log
                $userId = "admin";
                $type = 'เพิ่ม admin';
                $description = 'เจ้าหน้าที่ ' . $userId . ' ได้ทำการ ' . $type . ' ' . $request->user_id;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('ดำเนินการสำเร็จ', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
            }
        }
    }

    public function ResetPasswordUser(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($id)) {

            return $this->returnErrorData('ไม่พบข้อมูล id', 404);
        } else if (!isset($request->password)) {
            return $this->returnErrorData('กรุณาระบุรหัสผ่านให้เรียบร้อย', 404);
        } else if (!isset($request->new_password)) {
            return $this->returnErrorData('กรุณาระบุรหัสผ่านใหม่ให้เรียบร้อย', 404);
        } else if (!isset($request->confirm_new_password)) {
            return $this->returnErrorData('กรุณาระบุรหัสผ่านใหม่อีกครั้ง', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่ กรุณาเข้าสู่ระบบใหม่อีกครั้ง', 404);
        }

        if (strlen($request->new_password) < 6) {
            return $this->returnErrorData('กรุณาระบุรหัสผ่านอย่างน้อย 6 หลัก', 404);
        }

        if ($request->new_password != $request->confirm_new_password) {
            return $this->returnErrorData('รหัสผ่านไม่ตรงกัน', 404);
        }

        DB::beginTransaction();

        try {

            $User = User::find($id);

            if ($User->password == md5($request->password)) {

                $User->password = md5($request->new_password);
                $User->updated_at = Carbon::now()->toDateTimeString();
                $User->save();

                DB::commit();

                return $this->returnUpdate('ดำเนินการสำเร็จ');
            } else {

                return $this->returnErrorData('รหัสผ่านไม่ถูกต้อง', 404);
            }
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    public function ActivateUserPage(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $work_status = $request->work_status;

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $col = array(

            'id',
            'branch_id',
            'position_id',
            'user_id',
            'password',
            'prefix',
            'first_name',
            'last_name',
            'first_name_en',
            'last_name_en',
            'nickname',

            'citizen_no',
            'passport_no',
            'nationality',
            'work_status',

            'email',
            'phone_no',
            'birthday',
            'sex',
            'image',
            'image_signature',
            'register_date',
            'personnel_id',

            'salary',
            'bank_name',
            'account_no',
            'account_name',

            'designated_persons',
            'home_address',
            'district',
            'subdistrict',
            'province',
            'zip_code',

            'work_shift_id',
            'head_id',
            'is_head',

            'sso_status',
            'pvd_status',

            'payroll_status',

            'status',
            'create_by',
            'update_by',
            'created_at',
            'updated_at',
            'deleted_at',
            'work_type',

            // 'olaf_department_id',
            // 'olaf_group_id',
            // 'olaf_role',
            // 'olaf_id',


        );

        $D = User::select($col)

            ->with('branch')
            ->with('position')
            ->with('work_shift')
            ->with('head');
        // ->where('status', 'Request')

        // if ($login_company_id) {
        //     $D->WhereHas('branch', function ($query) use ($login_company_id) {
        //         $query->where('company_id', $login_company_id);
        //     });
        // }

        if ($login_branch_id) {
            $D->where('branch_id', $login_branch_id);
        }

        if ($work_status) {
            $D->where('work_status', $work_status);
        }

        $D->orderby($col[$order[0]['column']], $order[0]['dir']);

        if ($search['value'] != '' && $search['value'] != null) {

            $D->Where(function ($query) use ($search, $col) {

                //search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$c) {
                        $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                    }
                });

                // search with
                $query = $this->withPosition($query, $search);
            });
        }

        $d = $D->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;

                //image
                if ($d[$i]->image) {
                    $d[$i]->image = url($d[$i]->image);
                } else {
                    $d[$i]->image = null;
                }

                //signature
                if ($d[$i]->signature) {
                    $d[$i]->signature = url($d[$i]->signature);
                } else {
                    $d[$i]->signature = null;
                }

                //age work_day
                $d[$i]->work_day = $this->dateDiff(date('Y-m-d'), $d[$i]->register_date);
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    public function ForgotPasswordUser(Request $request)
    {

        $email = $request->email;

        $User = User::where('email', $email)->where('status', 'Yes')->first();

        if (!empty($User)) {

            //random string
            $length = 8;
            $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $charactersLength = strlen($characters);
            $randomString = '';
            for ($i = 0; $i < $length; $i++) {
                $randomString .= $characters[rand(0, $charactersLength - 1)];
            }
            //

            $newPasword = md5($randomString);

            DB::beginTransaction();

            try {

                $User->password = $newPasword;
                $User->save();

                $title = 'รหัสผ่านใหม่';
                $text = 'รหัสผ่านใหม่ของคุณคือ  ' . $randomString;
                $type = 'Forgot Password';

                // //send line
                // if ($User->line_token) {
                //     $this->sendLine($User->line_token, $text);
                // }

                //send email
                if ($User->email) {
                    $this->sendMail($User->email, $text, $title, $type);
                }

                DB::commit();

                return $this->returnUpdate('ดำเนินการสำเร็จ');
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
            }
        } else {
            return $this->returnErrorData('ไม่พบอีเมล์ในระบบ ', 404);
        }
    }

    public function ActivateUser(Request $request, $id)
    {

        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('[id] Data Not Found', 404);
        } else if (!isset($request->status)) {
            return $this->returnErrorData('[status] Data Not Found', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $User = User::find($id);

            $User->status = $request->status;
            $User->updated_at = Carbon::now()->toDateTimeString();
            $User->save();

            //log
            $userId = $loginBy->user_id;
            //$userId = $loginBy;
            $type = 'Activate User';
            $description = 'User ' . $userId . ' has ' . $type . ' number ' . $User->user_id;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function getLastUserID()
    {
        $User = User::latest()->first();
        $Item = array();

        if ($User) {
            $last = $User->user_id + 1;
            switch (strlen($last)) {
                case 1:
                    $Item["user_last_id"] = "0000" . $last;
                    break;
                case 2:
                    $Item["user_last_id"] = "000" . $last;
                    break;
                case 3:
                    $Item["user_last_id"] = "00" . $last;
                    break;
                case 4:
                    $Item["user_last_id"] = "0" . $last;
                    break;
                case 5:
                    $Item["user_last_id"] = $last;
                    break;
                default:
                    $Item["user_last_id"] = "000001";
            }
        } else {
            $Item["user_last_id"] = "000001";
        }
        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    public function getUserPayroll(Request $request)
    {
        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $user_id = $request->user_id;
        $month = $request->month;
        $year = $request->year;

        $round = $year . "-" . $month;

        $col = array('id', 'user_id', 'first_name', 'last_name', 'email', 'salary', 'bank_name', 'account_no', 'account_name', 'image', 'personnel_id', 'create_by', 'update_by', 'created_at', 'updated_at', 'work_type', 'position_id');

        $orderby = array('', 'user_id', 'first_name', 'last_name', 'email', 'salary', 'image', 'create_by ', 'work_type', 'position_id');

        $D = User::select($col);

        if ($user_id) {
            $D->where('id', $user_id);
        }

        if ($orderby[$order[0]['column']]) {
            $D->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            $D->Where(function ($query) use ($search, $col) {

                //search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$c) {
                        $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                    }
                });

                //search with
                //$query = $this->withPermission($query, $search);
            });
        }

        $d = $D->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;

                $startOfMonth = Carbon::create($year, $month)->subMonth()->day(26)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่ 26 ของเดือนก่อนหน้า
                $endOfMonth = Carbon::create($year, $month)->day(25)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่ 25 ของเดือนใน $round

                $calculatePerday = User_attendance::where('user_id', $d[$i]->id)
                    ->whereIn('type', ['normal', 'late'])
                    ->whereBetween('date', [$startOfMonth, $endOfMonth])
                    ->count();


                if ($d[$i]->work_type == 'day') {
                    $d[$i]->dayPerWork = $calculatePerday;
                    $d[$i]->salary = $d[$i]->salary * $calculatePerday;
                } else {
                    $d[$i]->dayPerWork = 0;
                    $d[$i]->salalyPerday = 0;
                }

                // สายทั้งหมด
                $latetimein = User_attendance::where('user_id', $user_id)
                    ->with('zk_time')
                    ->where('type', '=', 'late')
                    ->where('date', 'like', '%' . $year . '-' . $month . '-%')
                    ->get()
                    ->toArray();

                // เวลาเข้างานของผู้ใช้
                $worktime = Work_shift_time::where('work_shift_id', $d[$i]->work_shift_id)->first()->value('time_in');

                // เวลามาสายทั้งหมด
                $total_minOflate = 0;
                for ($j = 0; $j < count($latetimein); $j++) {
                    $latetime =  date('H:i', strtotime($latetimein[$j]['zk_time']['time']));

                    $total_minOflate += abs(strtotime($latetime) - strtotime($worktime)) / 60;
                }


                // ค่าแรงต่อนาที
                if ($d[$i]->work_type == 'day') {
                    $salaryPerMin = round(($d[$i]->salary / 8) / 60, 2);
                } else {
                    $salaryPerMin = round(($d[$i]->salary / 30) / 8 / 60, 2);
                }

                $d[$i]->total_late = ($total_minOflate * $salaryPerMin);
                //


                //income
                $incomeFromIncomePaid = IncomePaid::where('user_id', $d[$i]->id)
                    ->where('date', 'like', "$round-%")
                    ->sum('price');

                $incomeFromUserIncome = UserIncome::where('user_id', $d[$i]->id)
                    ->sum('price');

                // รวม'เงินเพิ่ม'จากทั้งสองตาราง
                $income = $incomeFromIncomePaid + $incomeFromUserIncome;

                $d[$i]->total_income = $income;
                //


                //deduct
                $deductFromDeductPaid = DeductPaid::where('user_id', $d[$i]->id)
                    ->where('date', 'like', "$round-%")
                    ->sum('price');

                $deductFromUserDeduct = UserDeduct::where('user_id', $d[$i]->id)
                    ->sum('price');

                // รวม'เงินหัก'จากทั้งสองตาราง
                $deduct = $deductFromDeductPaid + $deductFromUserDeduct;

                $d[$i]->total_deduct = $deduct;
                //

                //commissionx
                // $d[$i]->total_commission = $this->getCommisionUser($d[$i]->id, $round);
                $d[$i]->total_commission = 0;

                //ot
                // $d[$i]->total_ot = $this->getOtUser($d[$i]->id, $round);
                $d[$i]->total_ot = 0;

                //withdraw salary
                // $d[$i]->total_withdraw_salary = $this->getWithdrawSalaryUser($d[$i]->id, $round);
                $d[$i]->total_withdraw_salary = 0;

                $d[$i]->total = ($d[$i]->salary + $d[$i]->total_income + $d[$i]->total_commission + $d[$i]->total_ot) - $d[$i]->total_deduct - $d[$i]->total_withdraw_salary - $d[$i]->total_late;

                // $d[$i]->user = User::where('id', $d[$i]->user_id)->get();

                //pay status
                $Employee_salary = Employee_salary::where('user_id', $d[$i]->id)
                    ->where('hire_date', 'like', '%' . $round . '-%')
                    ->where(function ($query) {
                        $query->whereNull('slip')
                            ->orWhere('slip', '=', '');
                    })
                    ->exists();

                $d[$i]->pay_status = $Employee_salary;
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }

    public function updateSalaryUser(Request $request, $id)
    {

        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('[id] Data Not Found', 404);
        } else if (!isset($request->salary)) {
            return $this->returnErrorData('[salary] Data Not Found', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $User = User::find($id);

            $User->salary = $request->salary;
            $User->updated_at = Carbon::now()->toDateTimeString();
            $User->save();

            //log
            $userId = $loginBy->user_id;
            //$userId = $loginBy;
            $type = 'Update Salary';
            $description = 'User ' . $userId . ' has ' . $type . ' number ' . $User->user_id;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function getslipsalary(Request $request)
    {
        if (!isset($request->user_id)) {
            return $this->returnErrorData('ต้องใส่ user_id ด้วย', 404);
        } else if (!isset($request->month)) {
            return $this->returnErrorData('ต้องใส่ month ด้วย', 404);
        } else if (!isset($request->year)) {
            return $this->returnErrorData('ต้องใส่ year ด้วย', 404);
        }

        DB::beginTransaction();

        try {

            $user_id = $request->user_id;
            $month = $request->month;
            $year = $request->year;

            $round = $year . "-" . $month;

            $User = User::where('id', $user_id)
                ->with('position')
                ->with('branch')
                ->first();

            if ($User) {

                $currentDate = date('Y-m-d');

                // วันที่ 26 ของเดือนที่แล้ว
                $lastMonth26 = date('Y-m-26', strtotime('-1 month', strtotime($currentDate)));
                $nowMonth25 = date('Y-m-25', strtotime($currentDate));

                $calculatePerday = User_attendance::where('user_id', $user_id)
                    ->whereIn('type', ['normal', 'late'])
                    ->whereBetween('date', [$lastMonth26, $nowMonth25])
                    ->count();

                if ($User->work_type == 'day') {
                    $User->dayPerWork = $calculatePerday;
                    $User->salary = $User->salary * $calculatePerday;
                } else {
                    $User->dayPerWork = 0;
                    $User->salalyPerday = 0;
                }

                $User->income = UserIncome::where('user_id', $user_id)
                    ->with('income_type')
                    ->get();

                $Income = UserIncome::where('user_id', $user_id)
                    ->with('income_type')
                    ->sum('price');

                $User->deduct = UserDeduct::where('user_id', $user_id)
                    ->with('deduct_type')
                    ->get();

                $Deduct = UserDeduct::where('user_id', $user_id)
                    ->with('deduct_type')
                    ->sum('price');

                // สายทั้งหมด
                $latetimein = User_attendance::where('user_id', $user_id)
                    ->with('zk_time')
                    ->where('type', '=', 'late')
                    ->where('date', 'like', '%' . $year . '-' . $month . '-%')
                    ->get()
                    ->toArray();

                // เวลาเข้างานของผู้ใช้
                $worktime = Work_shift_time::where('work_shift_id', $User->work_shift_id)->first()->value('time_in');

                // เวลามาสายทั้งหมด
                $total_minOflate = 0;
                for ($j = 0; $j < count($latetimein); $j++) {
                    $latetime =  date('H:i', strtotime($latetimein[$j]['zk_time']['time']));

                    $total_minOflate += abs(strtotime($latetime) - strtotime($worktime)) / 60;
                }

                // ค่าแรงต่อนาที
                if ($User->work_type == 'day') {
                    $salaryPerMin = round(($User->salary / 8) / 60, 2);
                } else {
                    $salaryPerMin = round(($User->salary / 30) / 8 / 60, 2);
                }

                $User->total_late = ($total_minOflate * $salaryPerMin);

                //Check User has Deduct?
                $deduct_user = UserDeduct::select()
                    ->where('user_id', $User->id)
                    ->first();

                if ($deduct_user) {
                    $deduct_tax = UserDeduct::where('user_id', $User->id)
                        ->where('deduct_type_id', 22)
                        ->first();

                    if ($deduct_tax) {
                        $User->total_tax = ($User->salary * ($deduct_tax->rate / 100));
                    } else {
                        $User->total_tax = 0;
                    }
                    $deduct_insure = UserDeduct::where('user_id', $User->id)
                        ->where('deduct_type_id', 21)
                        ->first();

                    if ($deduct_insure) {
                        $User->total_insure = ($User->salary * ($deduct_insure->rate / 100));
                    } else {
                        $User->total_insure = 0;
                    }
                }

                //commissionx
                $User->total_commission = $this->getCommisionUser($user_id, $round);

                //ot
                // $User->total_ot = $this->getOtUser($user_id, $round);
                $User->total_ot = 0;

                //withdraw salary
                // $User->total_withdraw_salary = $this->getWithdrawSalaryUser($user_id, $round);
                $User->total_withdraw_salary = 0;
                // $User->user = User::where('id', $User->user_id)->get();

                //pay status
                $Employee_salary = Employee_salary::where('user_id', $user_id)
                    ->where('hire_date', 'like', '%' . $round . '-%')
                    ->first();

                if ($Employee_salary) {
                    $User->pay_status = true;
                } else {
                    $User->pay_status = false;
                }
                $User->total_deduct = $Deduct;
                $User->total_income = $User->salary + $User->total_commission + $User->total_ot + $Income;
                $User->total = $User->total_income - $User->total_deduct;

                //pay status
                $Employee_salary = Employee_salary::where('user_id', $user_id)
                    ->where('hire_date', 'like', '%' . $round . '-%')
                    ->first();

                if ($Employee_salary) {
                    $User->pay_status = true;
                } else {
                    $User->pay_status = false;
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'data' => $User,
            ]);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
        // {
        //     $columns = $request->columns;
        //     $length = $request->length;
        //     $order = $request->order;
        //     $search = $request->search;
        //     $start = $request->start;
        //     $page = $start / $length + 1;

        //     $user_id = $request->user_id;
        //     $month = $request->month;
        //     $year = $request->year;

        //     $round = $year . "-" . $month;

        //     $col = array('id', 'user_id', 'first_name', 'last_name', 'email', 'salary', 'bank_name', 'account_no', 'account_name', 'image', 'personnel_id', 'create_by', 'update_by', 'created_at', 'updated_at', 'work_type', 'position_id');

        //     $orderby = array('', 'user_id', 'first_name', 'last_name', 'email', 'salary', 'image', 'create_by ', 'work_type', 'position_id');

        //     $D = User::select($col);

        //     if ($user_id) {
        //         $uId = User::where('user_id', $user_id)->first();
        //         $D->where('user_id', $uId->user_id);
        //     }

        //     if ($orderby[$order[0]['column']]) {
        //         $D->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        //     }

        //     if ($search['value'] != '' && $search['value'] != null) {

        //         $D->Where(function ($query) use ($search, $col) {

        //             //search datatable
        //             $query->orWhere(function ($query) use ($search, $col) {
        //                 foreach ($col as &$c) {
        //                     $query->orWhere($c, 'like', '%' . $search['value'] . '%');
        //                 }
        //             });

        //             //search with
        //             //$query = $this->withPermission($query, $search);
        //         });
        //     }

        //     $d = $d->orderby('id','desc')->paginate($length, ['*'], 'page', $page);

        //     if ($d->isNotEmpty()) {

        //         //run no
        //         $No = (($page - 1) * $length);

        //         for ($i = 0; $i < count($d); $i++) {

        //             $No = $No + 1;
        //             $d[$i]->No = $No;

        //             $currentDate = date('Y-m-d');
        //             // วันที่ 26 ของเดือนที่แล้ว
        //             $lastMonth26 = date('Y-m-26', strtotime('-1 month', strtotime($currentDate)));
        //             $nowMonth25 = date('Y-m-25', strtotime($currentDate));

        //             $calculatePerday = User_attendance::where('user_id', $d[$i]->user_id)
        //                 ->where('type', '!=', 'off')->whereBetween('date', [$lastMonth26, $nowMonth25])->count();
        //             if ($d[$i]->work_type == 'day') {
        //                 $d[$i]->dayPerWork = $calculatePerday;
        //                 $d[$i]->salary = $d[$i]->salary * $calculatePerday;
        //             } else {
        //                 $d[$i]->dayPerWork = 0;
        //                 $d[$i]->salaryPerday = 0;
        //             }

        //             $d[$i]->income = UserIncome::where('user_id', $d[$i]->user_id)
        //                 ->where('date', 'like', '%' . $round . '-%')
        //                 ->get();

        //             $d[$i]->total_income = UserIncome::where('user_id', $d[$i]->user_id)
        //                 ->where('date', 'like', '%' . $round . '-%')
        //                 ->sum('price');

        //             $d[$i]->total_deduct = DeductPaid::where('user_id', $d[$i]->user_id)
        //                 ->where('date', 'like', '%' . $round . '-%')
        //                 ->sum('price');

        //             // สายทั้งหมด
        //             $latetimein = User_attendance::where('user_id', $d[$i]->user_id)
        //                 ->where('type', '=', 'late')
        //                 ->where('date', 'like', '%' . $round . '-%')
        //                 ->pluck('time_in')->toArray();

        //             // เวลาเข้างานของผู้ใช้
        //             $worktime = Work_times::where('position_id', $d[$i]->position_id)
        //                 ->where('date', 'like', '%' . $round . '-%')
        //                 ->where('type', '!=', 'Holiday')
        //                 ->value('time_in');

        //             // เวลามาสายทั้งหมด
        //             $total_minOflate = 0;
        //             for ($j = 0; $j < count($latetimein); $j++) {
        //                 $total_minOflate += abs(strtotime($latetimein[$j]) - strtotime($worktime)) / 60;
        //             }

        //             // ค่าแรงต่อนาที
        //             if ($d[$i]->work_type == 'day') {
        //                 $salaryPerMin = round(($d[$i]->salary / 8) / 60, 2);
        //             } else {
        //                 $salaryPerMin = round(($d[$i]->salary / 30) / 8 / 60, 2);
        //             }

        //             $d[$i]->total_late = ($total_minOflate * $salaryPerMin);

        //             //commissionx
        //             $d[$i]->total_commission = $this->getCommisionUser($d[$i]->id, $round);

        //             //ot
        //             $d[$i]->total_ot = $this->getOtUser($d[$i]->id, $round);
        //             //withdraw salary
        //             $d[$i]->total_withdraw_salary = $this->getWithdrawSalaryUser($d[$i]->id);

        //             //Check User has Deduct?
        //             $deduct_user = UserDeduct::select()
        //                 ->where('user_id', $d[$i]->id)
        //                 ->where('date', 'like', '%' . $round . '-%')
        //                 ->first();

        //             if ($deduct_user) {
        //                 $deduct_tax = UserDeduct::where('user_id', $d[$i]->id)
        //                     ->where('date', 'like', '%' . $round . '-%')
        //                     ->where('deduct_type_id', 22)
        //                     ->first();

        //                 if ($deduct_tax) {
        //                     $d[$i]->total_tax = ($d[$i]->salary * ($deduct_tax->rate / 100));
        //                 } else {
        //                     $d[$i]->total_tax = 0;
        //                 }
        //                 $deduct_insure = UserDeduct::where('user_id', $d[$i]->id)
        //                     ->where('date', 'like', '%' . $round . '-%')
        //                     ->where('deduct_type_id', 21)
        //                     ->first();

        //                 if ($deduct_insure) {
        //                     $d[$i]->total_insure = ($d[$i]->salary * ($deduct_insure->rate / 100));
        //                 } else {
        //                     $d[$i]->total_insure = 0;
        //                 }
        //             }

        //             $d[$i]->total = ($d[$i]->salary + $d[$i]->total_income + $d[$i]->total_commission + $d[$i]->total_ot) - $d[$i]->total_withdraw_salary - $d[$i]->total_tax - $d[$i]->total_insure;

        //             // $d[$i]->user = User::where('id', $d[$i]->user_id)->get();

        //             //pay status
        //             $Employee_salary = Employee_salary::where('user_id', $d[$i]->user_id)
        //                 ->where('hire_date', 'like', '%' . $round . '-%')
        //                 ->first();

        //             if ($Employee_salary) {
        //                 $d[$i]->pay_status = true;
        //             } else {
        //                 $d[$i]->pay_status = false;
        //             }
        //         }
        //     }

        //     return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
        // }
    }

    public function convertExcelDateToYmd($value)
    {
        if (is_numeric($value)) {
            // ถ้าเป็นตัวเลข ให้ถือว่าเป็น Excel serial number
            return Carbon::createFromDate(1900, 1, 1)->addDays($value - 2)->format('Y-m-d');
        }

        // ถ้าเป็นรูปแบบวันที่ (เช่น 22/3/1979)
        try {
            return Carbon::createFromFormat('d/m/Y', $value)->format('Y-m-d');
        } catch (\Exception $e) {
            // ลองแปลงเป็น date ทั่วไป (เผื่อกรณีใช้ / หรือ - หรือรูปแบบอื่น)
            try {
                return Carbon::parse($value)->format('Y-m-d');
            } catch (\Exception $e) {
                return null; // ถ้าไม่สามารถแปลงได้ ให้ส่ง null
            }
        }
    }

    public function ImportUser(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('User information not found. Please login again', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $file = request()->file('file');
        $fileName = $file->getClientOriginalName();

        $Data = Excel::toArray(new UserImport(), $file);
        $data = $Data[0];

        if (count($data) > 0) {

            DB::beginTransaction();

            try {


                // dd($data);

                for ($i = 0; $i < count($data); $i++) {

                    $id        = trim($data[$i][0]);
                    $branch_name    = trim($data[$i][1]);
                    $permission_name = trim($data[$i][2]);
                    $user_id        = trim($data[$i][3]);
                    $prefix         = trim($data[$i][4]);
                    $first_name     = trim($data[$i][5]);
                    $last_name      = trim($data[$i][6]);
                    $first_name_en  = trim($data[$i][7]);
                    $last_name_en   = trim($data[$i][8]);
                    $citizen_no     = trim($data[$i][9]);
                    $department     = trim($data[$i][10]);
                    $position       = trim($data[$i][11]);
                    $nickname       = trim($data[$i][12]);
                    $sex            = trim($data[$i][13]);
                    $email          = trim($data[$i][14]);
                    $phone_no       = trim($data[$i][15]);
                    $birthday       = trim($data[$i][16]);
                    $nationality    = trim($data[$i][17]);
                    $home_address   = trim($data[$i][18]);
                    $province       = trim($data[$i][19]);
                    $district       = trim($data[$i][20]);
                    $subdistrict    = trim($data[$i][21]);
                    $zipCode        = trim($data[$i][22]);
                    $register_date  = trim($data[$i][23]);
                    $salary         = trim($data[$i][24]);
                    $bank_name      = trim($data[$i][25]);
                    $account_no     = trim($data[$i][26]);
                    $account_name   = trim($data[$i][27]);
                    $head_id        = trim($data[$i][28]);


                    $password = md5(1234);

                    $dateBirthday = $this->convertExcelDateToYmd($birthday);
                    $dateRegister = $this->convertExcelDateToYmd($register_date);

                    //branch_name
                    $BranchId = null;
                    $Branch = Branch::where('name', $branch_name)->first();
                    if ($Branch) {
                        $BranchId = $Branch->id;
                    }


                    //permission_name
                    $PermissionId = null;
                    $Permission = Permission::where('name', $permission_name)->first();
                    if ($Permission) {
                        $PermissionId = $Permission->id;
                    }

                    //department
                    $DepartmentId = null;
                    $Department = Department::where('name', $department)
                        ->where('branch_id', $BranchId)
                        ->first();
                    if ($Department) {
                        $DepartmentId = $Department->id;
                    }

                    //position
                    $PositionId = null;
                    $Position = Position::where('name', $position)
                        ->where('branch_id', $BranchId)
                        ->first();
                    if ($Position) {
                        $PositionId = $Position->id;
                    } else {

                        $nPosition = new Position();
                        $nPosition->name = $position;
                        $nPosition->branch_id = $BranchId;
                        $nPosition->status = 1;
                        $nPosition->create_by = $loginBy->user_id;
                        $nPosition->save();

                        $PositionId = $nPosition->id;
                    }

                    $row = $i + 2;

                    if ($id == '') {
                        return $this->returnErrorData('Row excel data ' . $row . ' please enter id', 404);
                    }
                    // else if ($contact == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter contact', 404);
                    // } else if ($email == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter email', 404);
                    // } else if ($phone == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter phone', 404);
                    // } else if ($adress == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter adress', 404);
                    // }


                    // 🔍 Check by olaf_id
                    $User = User::where('id', $id)->first();

                    if (!$User) {
                        $User = new User();
                        $User->user_id = $user_id;
                        $User->password = $password;
                    }

                    $User->branch_id =  $BranchId;
                    $User->permission_id = $PermissionId;
                    $User->department_id = $DepartmentId;
                    $User->position_id = $PositionId;

                    //
                    $User->user_id = $user_id;
                    $User->password = $password;

                    $User->prefix = $prefix;
                    $User->first_name =  $first_name;
                    $User->last_name = $last_name;

                    $User->first_name_en = $first_name_en;
                    $User->last_name_en = $last_name_en;

                    $User->citizen_no = $citizen_no;

                    $User->nickname = $nickname;
                    $User->sex = $sex;
                    $User->email = $email;
                    $User->phone_no = $phone_no;
                    $User->birthday = $dateBirthday;
                    $User->nationality = $nationality;

                    $User->home_address =  $home_address;
                    $User->subdistrict = $subdistrict;
                    $User->district = $district;
                    $User->province = $province;
                    $User->zip_code = $zipCode;


                    // $User->image = null;

                    $User->salary =  $salary;
                    $User->work_type = ($salary > 1000 ? 'month' : 'day'); /// ประเภทงาน จ่ายรายวัน หรือ จ่ายรายเดือน
                    $User->work_status = 'ปกติ';
                    $User->bank_name = $bank_name;
                    $User->account_no = $account_no;
                    $User->account_name = $account_name;

                    $User->register_date = $dateRegister;

                    $User->personnel_id = $user_id;

                    //olaf
                    $User->olaf_department_id = null;
                    $User->olaf_group_id = null;
                    $User->olaf_role = null;
                    $User->olaf_id = $id;
                    // $User->work_shift_id = null;
                    $User->head_id =  $head_id ?? null;

                    $User->status = 1;


                    $User->save();
                    // }


                    //leave permission
                    $year = date('Y');
                    $sex = ['all', $User->sex];

                    $checkName = User_leave_permission::where('year', $year)
                        ->where('user_id', $User->id)
                        ->first();

                    if (!$checkName) {

                        $permission = [];
                        $LeaveType = LeaveType::whereIn('sex', $sex)
                            ->where('branch_id', $BranchId)
                            ->get();


                        for ($j = 0; $j < count($LeaveType); $j++) {

                            $qtyPermission = 0;
                            $rd = $User->register_date;

                            if ($rd) {
                                //permission
                                $User->work_day = $this->dateDiff(date('Y-m-d'), $rd);

                                $work_year = intval($User->work_day / 365);

                                $Leave_permission = Leave_permission::where('year', '<=',  $work_year)
                                    ->where('leave_type_id', $LeaveType[$j]->id)
                                    ->orderby('year', 'desc')
                                    ->first();


                                if ($Leave_permission) {
                                    $qtyPermission = $Leave_permission->qty;
                                }
                            }
                            //


                            //new

                            $User_leave_permission = new User_leave_permission();

                            $User_leave_permission->year = $year;

                            $User_leave_permission->user_id = $User->id;
                            $User_leave_permission->leave_type_id = $LeaveType[$j]->id;
                            $User_leave_permission->qty = $qtyPermission;

                            $User_leave_permission->status = true;
                            $User_leave_permission->create_by = $loginBy->user_id;
                            $User_leave_permission->updated_at = Carbon::now()->toDateTimeString();

                            $User_leave_permission->save();
                        }
                    }
                    //

                    // if ($sso == 'มี') {
                    //     $deduct_type_id = null;
                    //     $DeductType = DeductType::where('branch_id', $login_branch_id)
                    //         ->where('name', 'เงินประกันสังคม')
                    //         ->first();

                    //     if ($DeductType) {
                    //         $deduct_type_id = $DeductType->id;
                    //     }

                    //     $userDeduct = new UserDeduct();
                    //     $userDeduct->user_id = $User->id;
                    //     $userDeduct->deduct_type_id = $deduct_type_id;
                    //     $userDeduct->price = $request->price;
                    //     $userDeduct->save();
                    // }
                }

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                // dd($birthday);
                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        } else {
            return $this->returnErrorData('Data Not Found', 404);
        }
    }

    public function ExportUser(Request $request)
    {
        $filename = 'user_export_' . date('Ymd_His') . '.xlsx';
        return Excel::download(new UserExport, $filename);
    }

    public function ImportUserSalary(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('User information not found. Please login again', 404);
        }

        $file = request()->file('file');
        $fileName = $file->getClientOriginalName();

        $Data = Excel::toArray(new UserImport(), $file);
        $data = $Data[0];

        if (count($data) > 0) {

            DB::beginTransaction();

            try {


                // dd($data);

                for ($i = 0; $i < count($data); $i++) {
                    // dd($data[$i]);
                    $user_id = trim($data[$i][0]);
                    $citizen_no = trim($data[$i][1]);
                    $first_name = trim($data[$i][2]);
                    $last_name = trim($data[$i][3]);
                    $salary = trim($data[$i][4]);
                    $work_type = trim($data[$i][5]);



                    $row = $i + 2;

                    // if ($name == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
                    // } else if ($contact == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter contact', 404);
                    // } else if ($email == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter email', 404);
                    // } else if ($phone == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter phone', 404);
                    // } else if ($adress == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter adress', 404);
                    // }



                    $checkUser = User::where('user_id', $user_id)->first();
                    if ($checkUser) {
                        $checkUser->user_id = $user_id;
                        $checkUser->citizen_no = $citizen_no;
                        $checkUser->first_name = $first_name;
                        $checkUser->last_name = $last_name;
                        $checkUser->salary = $salary;
                        $checkUser->work_type = ($work_type == 'รายวัน' ? 'day' : 'month'); /// ประเภทงาน จ่ายรายวัน หรือ จ่ายรายเดือน 'day','month'
                        $checkUser->save();
                    }
                }

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        } else {
            return $this->returnErrorData('Data Not Found', 404);
        }
    }

    public function lineGenerate(Request $request, $id)
    {
        $user = User::where('id', $id)->first();
        if (!$user) {
            return $this->returnErrorData('Data Not Found', 404);
        }

        //unlink old rich menu
        try {
            $this->richMenuService->unlinkRichMenuFromUser($user->line_id);
        } catch (\Throwable $e) {
            //
        }

        //generate line token
        $line_token = $this->generateLineToken($request->line_token);

        $user->line_key = $line_token;
        $user->line_id = null;
        $user->save();

        return $this->returnSuccess('Successful operation', $line_token);
    }

    public function generateLineToken()
    {
        $token = '';
        $length = 8;

        do {
            $token = Str::random($length);

            // ตัวอย่างการใช้ DB Facade (แนะนำถ้า token นี้ไม่มี Model โดยตรง หรือต้องการแค่เช็คเร็วๆ)
            $exists = User::where('line_key', $token) // เช่น 'line_token' หรือ 'access_token'
                ->exists();
        } while ($exists); // ทำซ้ำจนกว่าจะสร้าง token ที่ไม่ซ้ำได้

        return $token;
    }

    public function updatePolicyLimit(Request $request, $id)
    {
        $loginBy = $request->login_by;

        $validator = Validator::make($request->all(), [
            'max_dti_pct' => 'required|numeric|min:0',
            'max_multiplier' => 'required|numeric|min:0',
            'net_min_amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        //Find Personal Limit
        $limit = LoanPolicyPersonalLimit::updateOrCreate(
            ['user_id' => $id],
            [
                'max_dti_pct' => $request->max_dti_pct,
                'max_multiplier' => $request->max_multiplier,
                'net_min_amount' => $request->net_min_amount,
                'create_by' => $loginBy->user_id,
            ]
        );

        return $this->returnSuccess('สำเร็จ', $limit);
    }

    public function ImportUserZkTime(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('User information not found. Please login again', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $file = request()->file('file');
        $fileName = $file->getClientOriginalName();

        $Data = Excel::toArray(new UserImport(), $file);
        $data = $Data[0];

        if (count($data) > 0) {

            DB::beginTransaction();

            try {


                // dd($data);

                for ($i = 0; $i < count($data); $i++) {

                    $id        = trim($data[$i][0]);
                    $event_id    = trim($data[$i][1]);
                    $time = trim($data[$i][2]);
                    $area_name        = trim($data[$i][3]);
                    $device_name         = trim($data[$i][4]);
                    $event_point     = trim($data[$i][5]);
                    $event_description      = trim($data[$i][6]);
                    $personnel_id  = trim($data[$i][7]);
                    $first_name   = trim($data[$i][8]);
                    $last_name     = trim($data[$i][9]);
                    $card_number     = trim($data[$i][10]);
                    $department_number       = trim($data[$i][11]);
                    $department_name       = trim($data[$i][12]);
                    $reader_name            = trim($data[$i][13]);
                    $verification_mode          = trim($data[$i][14]);
                    $created_at       = trim($data[$i][15]);
                    $updated_at       = trim($data[$i][16]);


                    $row = $i + 2;

                    if ($event_id == '') {
                        return $this->returnErrorData('Row excel data ' . $row . ' please enter id', 404);
                    }
                    // else if ($contact == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter contact', 404);
                    // } else if ($email == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter email', 404);
                    // } else if ($phone == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter phone', 404);
                    // } else if ($adress == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter adress', 404);
                    // }


                    //time
                    $unix = (int) round(($time - 25569) * 86400); // ใช้ round เพื่อกัน 07:59:59
                    $Time =  gmdate('Y-m-d H:i:s', $unix); // ใช้เวลา UTC; เปลี่ยนเป็น date() หากอยากได้ตาม timezone server

                    //created_at
                    $unix_created_at = (int) round(($created_at - 25569) * 86400); // ใช้ round เพื่อกัน 07:59:59
                    $TimeCreatedAt =  gmdate('Y-m-d H:i:s', $unix_created_at); // ใช้เวลา UTC; เปลี่ยนเป็น date() หากอยากได้ตาม timezone server


                    //updated_at
                    $unix_updated_at = (int) round(($updated_at - 25569) * 86400); // ใช้ round เพื่อกัน 07:59:59
                    $TimeUpdatedAt =  gmdate('Y-m-d H:i:s', $unix_updated_at); // ใช้เวลา UTC; เปลี่ยนเป็น date() หากอยากได้ตาม timezone server


                    $Zk_time = new Zk_time();

                    $Zk_time->event_id = $event_id;
                    $Zk_time->time = $Time;
                    $Zk_time->personnel_id = $personnel_id;

                    $Zk_time->created_at = $TimeCreatedAt;
                    $Zk_time->updated_at = $TimeUpdatedAt;

                    $Zk_time->save();
                }

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                // dd($birthday);
                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        } else {
            return $this->returnErrorData('Data Not Found', 404);
        }
    }

    public function ImportUserSalarySocial(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('User information not found. Please login again', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $file = request()->file('file');
        $fileName = $file->getClientOriginalName();

        $Data = Excel::toArray(new UserImport(), $file);
        $data = $Data[0];

        if (count($data) > 0) {

            DB::beginTransaction();

            try {


                // dd($data);

                for ($i = 0; $i < count($data); $i++) {

                    $id        = trim($data[$i][0]);
                    $user_id    = trim($data[$i][1]);
                    $name = trim($data[$i][2]);
                    $social        = trim($data[$i][3]);
                    $work_type         = trim($data[$i][4]);
                    $salary     = trim($data[$i][5]);
                    $monney_position      = trim($data[$i][6]);


                    $row = $i + 2;

                    if ($user_id == '') {
                        return $this->returnErrorData('Row excel data ' . $row . ' please enter id', 404);
                    }
                    // else if ($contact == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter contact', 404);
                    // } else if ($email == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter email', 404);
                    // } else if ($phone == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter phone', 404);
                    // } else if ($adress == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter adress', 404);
                    // }


                    $User = User::where('user_id', $user_id)->first();
                    if ($User) {

                        $User->work_type = $work_type;
                        $User->salary = $salary;
                        $User->save();

                        //user income

                        $incomeTypesId = null;
                        $IncomeType = IncomeType::where('name', 'ค่าตำแหน่ง')
                            ->where('branch_id', $User->branch_id)
                            ->first();

                        if ($IncomeType) {
                            $incomeTypesId = $IncomeType->id;
                        }

                        if ($monney_position && $monney_position > 0) {
                            //UserIncome
                            $Item = new UserIncome();
                            $Item->user_id = $User->id;
                            $Item->income_types_id = $incomeTypesId;
                            $Item->price =  $monney_position;

                            $Item->save();
                        }


                        //
                        if ($social && $social > 0) {

                            $PayrollContribution = new PayrollContribution();
                            $PayrollContribution->code_contribution = 'sso';
                            $PayrollContribution->payroll_id = null;
                            $PayrollContribution->user_id = $User->id;
                            $PayrollContribution->employer_rate = 5;
                            $PayrollContribution->employee_rate = 5;
                            $PayrollContribution->wage_base = 0;
                            $PayrollContribution->employer_total =  $social;
                            $PayrollContribution->employee_total = $social;
                            $PayrollContribution->status = true;

                            $PayrollContribution->save();
                        }
                    }
                }

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                // dd($birthday);
                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        } else {
            return $this->returnErrorData('Data Not Found', 404);
        }
    }

    public function ImportUserBondRequired(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('User information not found. Please login again', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $file = request()->file('file');
        $fileName = $file->getClientOriginalName();

        $Data = Excel::toArray(new UserImport(), $file);
        $data = $Data[0];

        if (count($data) > 0) {

            DB::beginTransaction();

            try {


                // dd($data);

                for ($i = 0; $i < count($data); $i++) {

                    $id        = trim($data[$i][0]);
                    $user_id    = trim($data[$i][1]);
                    $name = trim($data[$i][2]);
                    $total        = trim($data[$i][3]);
                    $pay         = trim($data[$i][4]);


                    $row = $i + 2;

                    if ($user_id == '') {
                        return $this->returnErrorData('Row excel data ' . $row . ' please enter id', 404);
                    }
                    // else if ($contact == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter contact', 404);
                    // } else if ($email == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter email', 404);
                    // } else if ($phone == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter phone', 404);
                    // } else if ($adress == '') {
                    //     return $this->returnErrorData('Row excel data ' . $row . ' please enter adress', 404);
                    // }


                    $User = User::where('user_id', $user_id)->first();
                    if ($User) {
                        $User->is_bond_complete = ($pay == 0 ? true : false);
                        $User->bond_total = ($total > 0 ? $total : 0.00);
                        $User->bond_balance = ($pay > 0 ? $pay : 0.00);
                        $User->save();
                    }
                }

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                // dd($birthday);
                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        } else {
            return $this->returnErrorData('Data Not Found', 404);
        }
    }
}
