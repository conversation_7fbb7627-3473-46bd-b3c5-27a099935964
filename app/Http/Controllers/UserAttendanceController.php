<?php

namespace App\Http\Controllers;

use App\Exports\ZkExport;
use App\Models\Holiday;
use App\Models\Leave_table;
use App\Models\Leave_table_date;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\User_yellow_card;
use App\Models\Work_shift_time;
use App\Models\WorkTime;
use App\Models\Zk_time;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class UserAttendanceController extends Controller
{

    public function getPage(Request $request)
    {
        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $user_id = $request->user_id;
        // $month = $request->month;
        // $year = $request->year;
        $type = $request->type;

        $date_start = $request->date_start;
        $date_end = $request->date_end;

        // if (!isset($user_id)) {
        //     return $this->returnErrorData('กรุณาเลือกพนักงาน', 404);
        // }

        $col = array('id', 'user_id', 'zkt_time_id', 'date', 'type', 'leave_table_id', 'status', 'created_at', 'updated_at');

        $orderby = array('id', 'user_id', 'zkt_time_id', 'date', 'type', 'leave_table_id',  'status', 'created_at', 'updated_at');

        $D = User_attendance::select($col)
            ->with('user.position')
            ->with('user.work_shift.work_shift_times')
            ->with('zk_time')
            ->with('leave_table');

        // if ($user_id) {
            $D->where('user_id', $user_id);
        // }
        if ($type) {
            $D->where('type', $type);
        }

        // if ($month) {
        //     $D->where('date', 'like', '%-' . $month . '-%');
        // }

        // if ($year) {
        //     $D->where('date', 'like', '%' . $year . '-%');
        // }

        if ($date_start && $date_end) {
            $D->where('date', '>=', $date_start);
            $D->where('date', '<=', $date_end);
        }

        // $D->groupBy('date');


        if ($orderby[$order[0]['column']]) {
            $D->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            $D->Where(function ($query) use ($search, $col) {

                //search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$c) {
                        $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                    }
                });

                // search with
                $query = $this->withUser($query, $search);
            });
        }

        $d = $D->orderby('id','desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
                // $d[$i]->time_start = WorkingTime::where('date', $d[$i]->date)->where('user_id', $d[$i]->user_id)->orderBy('time', 'asc')->first();
                // $d[$i]->time_end = WorkingTime::where('date', $d[$i]->date)->where('user_id', $d[$i]->user_id)->orderBy('time', 'desc')->first();
                // $d[$i]->user = User::where('id', $d[$i]->user_id)->get();

                $d[$i]->is_holiday = $this->isHoliday($d[$i]->date);


                //time
                $d[$i]->time_in = null;
                $d[$i]->time_out = null;
                $d[$i]->time_brake_in = null;
                $d[$i]->time_brake_out = null;
                $d[$i]->qty_hour = null;


                $user = $d[$i]->user;


                $time_brake_in_start = '11:55:00';
                $time_brake_in_end = '14:00:00';
                $time_brake_out_start = '12:30:00';
                $time_brake_out_end = '14:00:00';
                if ($user) {

                    if ($user->work_shift) {

                        $strDate = date('D', strtotime($d[$i]->date));

                        $Work_shift_time =  Work_shift_time::where('work_shift_id', $user->work_shift->id)
                            ->where('day', $strDate)
                            ->first();

                        if ($Work_shift_time) {
                            $time_brake_in_start = $Work_shift_time->time_brake_in_start; //in
                            $time_brake_in_end = $Work_shift_time->time_brake_in_end;
                            $time_brake_out_start = $Work_shift_time->time_brake_out_start; //out
                            $time_brake_out_end = $Work_shift_time->time_brake_out_end;
                        }
                    }
                }

                if ($d[$i]->zk_time) {
                    //time_brake_in
                    $zk_time_brake_start = Zk_time::with('user')
                        ->where('personnel_id', $user->personnel_id)
                        // ->where('time', 'like', '%' . $User[$i]->date . '%')
                        ->whereBetween('time', [$d[$i]->date . ' ' . $time_brake_in_start, $d[$i]->date . ' ' . $time_brake_in_end])
                        ->orderby('time', 'asc')
                        ->first();
                    // dd($zk_time_brake_start);
                    if ($zk_time_brake_start) {
                        $d[$i]->time_brake_in = date('H:i:s', strtotime($zk_time_brake_start->time));
                    }

                    //time_brake_out
                    $zk_time_brake_end = Zk_time::with('user')
                        ->where('personnel_id', $user->personnel_id)
                        // ->where('time', 'like', '%' . $User[$i]->date . '%')
                        // ->whereBetween('time', [$d[$i]->date . ' 12:30:00', $d[$i]->date . ' 14:00:00']) // หาตั้งแต่เวลา 13:00:00 ถึงเป็นต้นไป
                        ->whereBetween('time', [$d[$i]->date . ' ' . $time_brake_out_start, $d[$i]->date . ' ' . $time_brake_out_end])
                        ->orderby('time', 'asc')
                        ->first();

                    if ($zk_time_brake_end) {
                        $d[$i]->time_brake_out = date('H:i:s', strtotime($zk_time_brake_end->time));
                    }

                    //time_in
                    $zk_time_in = Zk_time::with('user');

                    $zk_time_in->where('personnel_id', $user->personnel_id);
                    $zk_time_in->where('time', 'like', '%' . $d[$i]->date . '%');

                    $Zk_time_in = $zk_time_in->orderby('time', 'asc')->first();

                    if ($Zk_time_in) {
                        $d[$i]->time_in = date('H:i:s', strtotime($Zk_time_in->time));
                    }

                    //time_out
                    $zk_time_out = Zk_time::with('user');

                    $zk_time_out->where('personnel_id', $user->personnel_id);
                    $zk_time_out->where('time', 'like', '%' . $d[$i]->date . '%');

                    $Zk_time_out = $zk_time_out->orderby('time', 'desc')->first();

                    if ($Zk_time_out) {
                        $d[$i]->time_out = date('H:i:s', strtotime($Zk_time_out->time));
                    }


                    //qty_hour
                    $d[$i]->qty_hour = $this->TimeDiff($d[$i]->time_in, $d[$i]->time_out);
                }
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }

    // public function attendance()
    // {
    //     DB::beginTransaction();

    //     try {

    //         //    //miss
    //         //    $User_attendance =  new User_attendance();
    //         //    $User_attendance->date =  '2024-04-01';
    //         //    $User_attendance->user_id =  28;
    //         //    $User_attendance->zkt_time_id = null;
    //         //    $User_attendance->type = 'miss';
    //         //    $User_attendance->leave_table_id = null;
    //         //    $User_attendance->status = true;
    //         //    $User_attendance->save();

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User = User::with('position')->where('status', 1)->get();


    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime) {
    //                     if ($WorkTime->type == 'Work') {

    //                         //
    //                         $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                             ->where('time', 'like', '%' . $dateYesterday . '%')
    //                             ->orderby('time')
    //                             ->first();

    //                         if ($Zk_time) {

    //                             if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                                 //late
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'late';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             } else {
    //                                 //normal
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'normal';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             }
    //                         } else {

    //                             //miss
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = null;
    //                             $User_attendance->type = 'miss';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {
    //                         //off
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'off';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();
    //                     }
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }

    // public function attendance()
    // {
    //     DB::beginTransaction();

    //     try {

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User =  User::with('position')->where('status', 1)->get();

    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime->type == 'Work') {

    //                     //Zk_time
    //                     $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                         ->where('first_in_time', 'like', '%' . $dateYesterday . '%')
    //                         ->first();

    //                     //check user leave
    //                     $userID =  $User[$i]->id;
    //                     $Leave_table_date =  Leave_table_date::with('leave_table')
    //                         ->where('date', $dateYesterday)
    //                         ->WhereHas('leave_table', function ($query) use ($userID) {
    //                             $query->where('user_id', $userID);
    //                             $query->where('status', 'approved');
    //                         })
    //                         ->first();

    //                     if ($Leave_table_date) {

    //                         if ($Leave_table_date->leave_table->qty_hour >= 8) {
    //                             //leave
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = ($Zk_time ? $Zk_time->id : null);
    //                             $User_attendance->type = 'leave';
    //                             $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         } else {
    //                             //leave_hour
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = ($Zk_time ? $Zk_time->id : null);
    //                             $User_attendance->type = 'leave_hour';
    //                             $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else if ($Zk_time) {

    //                         if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                             //late
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'late';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();

    //                             //add user yellow card
    //                             $TimeDiff =  $this->TimeDiff($WorkTime->time_in, date('H:i', strtotime($Zk_time->time)));
    //                             if (date('H:i', strtotime($Zk_time->time)) >= '13:00') {
    //                                 $qty_hour =  $TimeDiff - 1;
    //                             } else {
    //                                 $qty_hour = $TimeDiff;
    //                             }

    //                             $YellowCard =  $this->calculateYellowCard('late', $qty_hour,  $User_attendance->id);

    //                             $User_yellow_card = new User_yellow_card();
    //                             $User_yellow_card->user_id = $User[$i]->id;
    //                             $User_yellow_card->qty = $YellowCard; //qty
    //                             $User_yellow_card->user_attendance_id = $User_attendance->id;
    //                             $User_yellow_card->status = true;
    //                             $User_yellow_card->save();
    //                         } else {
    //                             //normal
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'normal';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {

    //                         //miss
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'miss';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();

    //                         //add user yellow card
    //                         $qty_hour =  $this->TimeDiff($WorkTime->time_in, $WorkTime->time_out) - 1;
    //                         $YellowCard =  $this->calculateYellowCard('miss', $qty_hour,  $User_attendance->id);

    //                         $User_yellow_card = new User_yellow_card();
    //                         $User_yellow_card->user_id = $User[$i]->id;
    //                         $User_yellow_card->qty = $YellowCard; //qty
    //                         $User_yellow_card->user_attendance_id = $User_attendance->id;
    //                         $User_yellow_card->status = true;
    //                         $User_yellow_card->save();
    //                         //
    //                     }
    //                 } else {
    //                     //off
    //                     $User_attendance =  new User_attendance();
    //                     $User_attendance->date =  $dateYesterday;
    //                     $User_attendance->user_id =  $User[$i]->id;
    //                     $User_attendance->zkt_time_id = null;
    //                     $User_attendance->type = 'off';
    //                     $User_attendance->leave_table_id = null;
    //                     $User_attendance->status = true;
    //                     $User_attendance->save();
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }

    public function attendance()
    {
        DB::beginTransaction();

        try {

            $dateNow = date('Y-m-d');
            $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

            $User = User::with('position')->where('status', 1)->get();

            for ($i = 0; $i < count($User); $i++) {

                //check duplicate Attendance
                $check = User_attendance::where('user_id', $User[$i]->id)
                    ->where('date', $dateYesterday)
                    ->first();

                if (!$check) {


                    // เช็คว่าอยู่กลุ่ม ผู้บริหาร ป่าว
                    if($User[$i]->work_shift_group_id == 2){

                        //normal
                        $User_attendance =  new User_attendance();
                        $User_attendance->date =  $dateYesterday;
                        $User_attendance->user_id =  $User[$i]->id;
                        $User_attendance->zkt_time_id = null;
                        $User_attendance->type = 'normal';
                        $User_attendance->leave_table_id = null;
                        $User_attendance->status = true;
                        $User_attendance->save();

                        continue; // ข้ามไป user อื่น

                    }


                    $strDate = date('D', strtotime($dateYesterday));

                    //check working day
                    $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                    if ($User[$i]->work_shift_id) {
                        $workTime->where('work_shift_id', $User[$i]->work_shift_id);
                    }
                    $WorkTime =  $workTime->first();

                    if ($WorkTime) {

                        //check holiday
                        $Holiday =  Holiday::where('date', $dateYesterday)->first();
                        if (!$Holiday) {

                            if ($WorkTime->status == true) {
                                //
                                $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
                                    ->where('time', 'like', '%' . $dateYesterday . '%')
                                    ->orderby('time')
                                    ->first();

                                //check leave
                                $userID =  $User[$i]->id;
                                $Leave_table_date =  Leave_table_date::with('leave_table')
                                    ->where('date', $dateYesterday)
                                    ->WhereHas('leave_table', function ($query) use ($userID) {
                                        $query->where('user_id', $userID);
                                        $query->where('status', 'approved');
                                    })
                                    ->first();

                                if ($Leave_table_date) {
                                    //leave
                                    $User_attendance =  new User_attendance();
                                    $User_attendance->date =  $dateYesterday;
                                    $User_attendance->user_id =  $User[$i]->id;
                                    $User_attendance->zkt_time_id = null;
                                    $User_attendance->type = 'leave';
                                    $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                    $User_attendance->status = true;
                                    $User_attendance->save();
                                } else if ($Zk_time) {

                                    if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                        //late
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User[$i]->id;
                                        $User_attendance->zkt_time_id = $Zk_time->id;
                                        $User_attendance->type = 'late';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    } else {
                                        //normal
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User[$i]->id;
                                        $User_attendance->zkt_time_id = $Zk_time->id;
                                        $User_attendance->type = 'normal';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    }
                                } else {

                                    //miss
                                    $User_attendance =  new User_attendance();
                                    $User_attendance->date =  $dateYesterday;
                                    $User_attendance->user_id =  $User[$i]->id;
                                    $User_attendance->zkt_time_id = null;
                                    $User_attendance->type = 'miss';
                                    $User_attendance->leave_table_id = null;
                                    $User_attendance->status = true;
                                    $User_attendance->save();
                                }
                            } else {
                                //off
                                $User_attendance =  new User_attendance();
                                $User_attendance->date =  $dateYesterday;
                                $User_attendance->user_id =  $User[$i]->id;
                                $User_attendance->zkt_time_id = null;
                                $User_attendance->type = 'off';
                                $User_attendance->leave_table_id = null;
                                $User_attendance->status = true;
                                $User_attendance->save();
                            }
                        } else {
                            //off
                            $User_attendance =  new User_attendance();
                            $User_attendance->date =  $dateYesterday;
                            $User_attendance->user_id =  $User[$i]->id;
                            $User_attendance->zkt_time_id = null;
                            $User_attendance->type = 'off';
                            $User_attendance->leave_table_id = null;
                            $User_attendance->status = true;
                            $User_attendance->save();
                        }
                    }
                }else {
                    // กรณีนี้: มีข้อมูลอยู่แล้ว
                    return $this->returnErrorData("User ID {$User[$i]->id} already has attendance for {$dateYesterday}.", 404);
                }
            }

            DB::commit();
            return $this->returnSuccess('สำเร็จ', []);
        } catch (\Throwable $e) {
            DB::rollback();
        }
    }

    public function ExportTimeInOut(Request $request)
    {

        $date_start = $request->date_start;
        if ($date_start) {
            $date_start = $date_start . ' 00:00:00';
        }

        $date_end = $request->date_end;
        if ($date_end) {
            $date_end = $date_end . ' 23:59:59';
        }
        $personnel_id = $request->personnel_id;


        $user = Zk_time::select('personnel_id', DB::raw('DATE(time) as date'))
            ->with('user');

        if ($personnel_id) {
            $user->where('personnel_id', $personnel_id);
        }

        if ($date_start && $date_end) {
            $user->where('time', '>=', $date_start);
            $user->where('time', '<=', $date_end);
        }
        $data = $user->groupBy('personnel_id', 'date')
            ->get()->toArray();


        for ($i = 0; $i < count($data); $i++) {

            $data[$i]['time_in'] = null;
            $data[$i]['time_out'] = null;
            $data[$i]['time_brake_in'] = null;
            $data[$i]['time_brake_out'] = null;

            //time_in
            $zk_time_in = Zk_time::with('user');

            $zk_time_in->where('personnel_id', $data[$i]['personnel_id']);
            $zk_time_in->where('time', 'like', '%' . $data[$i]['date'] . '%');

            $Zk_time_in  =  $zk_time_in->orderby('time', 'asc')->first();

            if ($Zk_time_in) {
                $data[$i]['time_in'] = date('H:i:s', strtotime($Zk_time_in->time));
            }

            //time_out
            $zk_time_out = Zk_time::with('user');

            $zk_time_out->where('personnel_id', $data[$i]['personnel_id']);
            $zk_time_out->where('time', 'like', '%' . $data[$i]['date'] . '%');

            $Zk_time_out  =  $zk_time_out->orderby('time', 'desc')->first();

            if ($Zk_time_out) {
                $data[$i]['time_out'] = date('H:i:s', strtotime($Zk_time_out->time));
            }

            //time_brake_in
            $zk_time_brake_start = Zk_time::with('user')
                ->where('personnel_id', $data[$i]['personnel_id'])
                ->whereBetween('time', [$data[$i]['date'] . ' 12:00:00', $data[$i]['date'] . ' 14:00:00']) // หาตั้งแต่เวลา 12:00:00 เป็นต้นไป
                ->orderby('time', 'asc')
                ->first();
            // dd($zk_time_brake_start);
            if ($zk_time_brake_start) {
                $data[$i]['time_brake_in'] = date('H:i:s', strtotime($zk_time_brake_start->time));
            }

            //time_brake_out
            $zk_time_brake_end = Zk_time::with('user')
                ->where('personnel_id', $data[$i]['personnel_id'])
                ->whereBetween('time', [$data[$i]['date'] . ' 13:00:00', $data[$i]['date'] . ' 14:00:00']) // หาตั้งแต่เวลา 13:00:00 ถึงเป็นต้นไป
                ->orderby('time', 'asc')
                ->first();

            if ($zk_time_brake_end) {
                $data[$i]['time_brake_out'] = date('H:i:s', strtotime($zk_time_brake_end->time));
            }
        }


        if (!empty($data)) {

            for ($i = 0; $i < count($data); $i++) {

                $export_data[] = array(
                    'personnel_id' => trim($data[$i]['personnel_id']),
                    'first_name' => trim($data[$i]['user'] ? $data[$i]['user']['first_name'] : null),
                    'last_name' => trim($data[$i]['user'] ? $data[$i]['user']['last_name'] : null),
                    'date' => trim($data[$i]['date']),
                    'time_in' => trim($data[$i]['time_in']),
                    'time_out' => trim($data[$i]['time_out']),
                    'time_brake_in' => trim($data[$i]['time_brake_in']),
                    'time_brake_out' => trim($data[$i]['time_brake_out']),
                );
            }

            $result = new ZkExport($export_data);
            return Excel::download($result, 'เวลาเข้าออกพนักงาน' . ' ' . $date_start . '.xlsx');
        } else {

            $export_data[] = array(
                'personnel_id' => null,
                'first_name' => null,
                'last_name' => null,
                'date' => null,
                'time_in' => null,
                'time_out' => null,
                'time_brake_in' => null,
                'time_brake_out' => null,
            );

            $result = new ZkExport($export_data);
            return Excel::download($result, 'เวลาเข้าออกพนักงาน' . ' ' . $date_start . '.xlsx');
        }
    }

    public function putAttendance(Request $request)
    {
        DB::beginTransaction();

        try {

            $date_start =  $request->date_start;
            $date_end =  $request->date_end;

            //check diff date
            $dateInPeriod = $this->dateInPeriod($date_start, $date_end);

            for ($a = 0; $a < count($dateInPeriod); $a++) {

                $dateYesterday = $dateInPeriod[$a];

                $User = User::with('position')->where('status', 1)->get();


                for ($i = 0; $i < count($User); $i++) {

                    //check duplicate Attendance
                    $check = User_attendance::where('user_id', $User[$i]->id)
                        ->where('date', $dateYesterday)
                        ->first();

                    if (!$check) {

                        //add

                        $strDate = date('D', strtotime($dateYesterday));

                        //check working day
                        $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                        if ($User[$i]->work_shift_id) {
                            $workTime->where('work_shift_id', $User[$i]->work_shift_id);
                        }
                        $WorkTime =  $workTime->first();

                        if ($WorkTime) {

                            //check holiday
                            $Holiday =  Holiday::where('date', $dateYesterday)->first();
                            if (!$Holiday) {

                                if ($WorkTime->status == true) {
                                    //
                                    $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
                                        ->where('time', 'like', '%' . $dateYesterday . '%')
                                        ->orderby('time')
                                        ->first();

                                    //check leave
                                    $userID =  $User[$i]->id;
                                    $Leave_table_date =  Leave_table_date::with('leave_table')
                                        ->where('date', $dateYesterday)
                                        ->WhereHas('leave_table', function ($query) use ($userID) {
                                            $query->where('user_id', $userID);
                                            $query->where('status', 'approved');
                                        })
                                        ->first();

                                    if ($Leave_table_date) {
                                        //leave
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User[$i]->id;
                                        $User_attendance->zkt_time_id = null;
                                        $User_attendance->type = 'leave';
                                        $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    } else if ($Zk_time) {

                                        if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                            //late
                                            $User_attendance =  new User_attendance();
                                            $User_attendance->date =  $dateYesterday;
                                            $User_attendance->user_id =  $User[$i]->id;
                                            $User_attendance->zkt_time_id = $Zk_time->id;
                                            $User_attendance->type = 'late';
                                            $User_attendance->leave_table_id = null;
                                            $User_attendance->status = true;
                                            $User_attendance->save();
                                        } else {
                                            //normal
                                            $User_attendance =  new User_attendance();
                                            $User_attendance->date =  $dateYesterday;
                                            $User_attendance->user_id =  $User[$i]->id;
                                            $User_attendance->zkt_time_id = $Zk_time->id;
                                            $User_attendance->type = 'normal';
                                            $User_attendance->leave_table_id = null;
                                            $User_attendance->status = true;
                                            $User_attendance->save();
                                        }
                                    } else {

                                        //miss
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User[$i]->id;
                                        $User_attendance->zkt_time_id = null;
                                        $User_attendance->type = 'miss';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    }
                                } else {
                                    //off
                                    $User_attendance =  new User_attendance();
                                    $User_attendance->date =  $dateYesterday;
                                    $User_attendance->user_id =  $User[$i]->id;
                                    $User_attendance->zkt_time_id = null;
                                    $User_attendance->type = 'off';
                                    $User_attendance->leave_table_id = null;
                                    $User_attendance->status = true;
                                    $User_attendance->save();
                                }
                            } else {
                                //off
                                $User_attendance =  new User_attendance();
                                $User_attendance->date =  $dateYesterday;
                                $User_attendance->user_id =  $User[$i]->id;
                                $User_attendance->zkt_time_id = null;
                                $User_attendance->type = 'off';
                                $User_attendance->leave_table_id = null;
                                $User_attendance->status = true;
                                $User_attendance->save();
                            }
                        }
                    } else {

                        //update

                        $strDate = date('D', strtotime($dateYesterday));

                        //check working day
                        $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                        if ($User[$i]->work_shift_id) {
                            $workTime->where('work_shift_id', $User[$i]->work_shift_id);
                        }
                        $WorkTime =  $workTime->first();

                        if ($WorkTime) {

                            //check holiday
                            $Holiday =  Holiday::where('date', $dateYesterday)->first();
                            if (!$Holiday) {

                                if ($WorkTime->status == true) {
                                    //
                                    $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
                                        ->where('time', 'like', '%' . $dateYesterday . '%')
                                        ->orderby('time')
                                        ->first();

                                    //check leave
                                    $userID =  $User[$i]->id;
                                    $Leave_table_date =  Leave_table_date::with('leave_table')
                                        ->where('date', $dateYesterday)
                                        ->WhereHas('leave_table', function ($query) use ($userID) {
                                            $query->where('user_id', $userID);
                                            $query->where('status', 'approved');
                                        })
                                        ->first();

                                    if ($Leave_table_date) {
                                        //leave
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User[$i]->id;
                                        $User_attendance->zkt_time_id = null;
                                        $User_attendance->type = 'leave';
                                        $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    } else if ($Zk_time) {

                                        // dd(date('H:i', strtotime($Zk_time->time)).'>'. $WorkTime->time_in_end);

                                        if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                            //late
                                            $check->date =  $dateYesterday;
                                            $check->user_id =  $User[$i]->id;
                                            $check->zkt_time_id = $Zk_time->id;
                                            $check->type = 'late';
                                            $check->leave_table_id = null;
                                            $check->status = true;
                                            $check->save();
                                        } else {
                                            //normal
                                            $check->date =  $dateYesterday;
                                            $check->user_id =  $User[$i]->id;
                                            $check->zkt_time_id = $Zk_time->id;
                                            $check->type = 'normal';
                                            $check->leave_table_id = null;
                                            $check->status = true;
                                            $check->save();
                                        }
                                    } else {

                                        //miss
                                        $check->date =  $dateYesterday;
                                        $check->user_id =  $User[$i]->id;
                                        $check->zkt_time_id = null;
                                        $check->type = 'miss';
                                        $check->leave_table_id = null;
                                        $check->status = true;
                                        $check->save();
                                    }
                                } else {
                                    //off
                                    $check->date =  $dateYesterday;
                                    $check->user_id =  $User[$i]->id;
                                    $check->zkt_time_id = null;
                                    $check->type = 'off';
                                    $check->leave_table_id = null;
                                    $check->status = true;
                                    $check->save();
                                }
                            } else {
                                //off
                                $check->date =  $dateYesterday;
                                $check->user_id =  $User[$i]->id;
                                $check->zkt_time_id = null;
                                $check->type = 'off';
                                $check->leave_table_id = null;
                                $check->status = true;
                                $check->save();
                            }
                        }
                    }
                }
            }


            DB::commit();
            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {
            DB::rollback();
            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }
}
