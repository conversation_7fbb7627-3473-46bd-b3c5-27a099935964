<?php

namespace App\Http\Controllers;

use App\Models\Warning;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class WarningController extends Controller
{
    public function WarningPage(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->Page($request);
    }

    public function WarningPageLine(Request $request)
    {

        $line_id = $request->line_id;
        $line_head_id = $request->line_head_id;

        if ($line_id) {
            $loginBy = User::where('line_id', $line_id)->first();
        } else if ($line_head_id) {
            $loginBy = User::where('line_id', $line_head_id)->first();
        }

        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $getPermissionViewData = $this->getPermissionViewData($loginBy);

        $request->request->add([
            'login_id' => $getPermissionViewData['login_id'],
            'login_by' => $getPermissionViewData['login_by'],
            'login_permission_view' => $getPermissionViewData['login_permission_view'],
            'login_user_id' => $getPermissionViewData['login_user_id'],
            'login_branch_id' => $getPermissionViewData['login_branch_id'],
            'login_company_id' => $getPermissionViewData['login_company_id'],
        ]);


        return $this->Page($request);
    }

    public function Page($request)
    {
        //
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_branch_id = $request->login_branch_id;


        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        if ($length > 0) {
            $page = intval($start / $length) + 1;
        } else {
            $page = 1;
        }

        $user_id = $request->user_id;
        $status = $request->status;
        $head_id = $request->head_id;


        $col = array('id', 'branch_id', 'user_id', 'date', 'title', 'description', 'punishment', 'status', 'approved_by', 'approved_at', 'acknowledged_by', 'acknowledged_at', 'created_at', 'updated_at');

        $orderby = array('id', 'branch_id', 'user_id', 'date', 'title', 'description', 'punishment', 'status', 'approved_by', 'approved_at', 'acknowledged_by', 'acknowledged_at', 'created_at', 'updated_at');


        $d = Warning::select($col);

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        if ($user_id) {
            $d->where('user_id', $user_id);
        }

        if ($head_id) {
            $d->whereHas('user', function ($q) use ($head_id) {
                $q->where('head_id', $head_id);
            });
        }


        if ($status) {
            $d->where('status', $status);
        }

        if (isset($order[0]['column'], $order[0]['dir'])) {
            $columnIndex = $order[0]['column'];
            $direction = $order[0]['dir'];

            if (isset($orderby[$columnIndex])) {
                $d->orderBy($orderby[$columnIndex], $direction);
            }
        }

        if (isset($search['value']) && $search['value'] !== '') {

            $d->Where(function ($query) use ($search, $col) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });

                // search with
                $query = $this->withUser($query, $search);
            });
        }


        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
                $d[$i]->user = User::where('id', $d[$i]->user_id)->first();
                $d[$i]->approved_by = User::where('id', $d[$i]->approved_by)->first();
                $d[$i]->acknowledged_by = User::where('id', $d[$i]->acknowledged_by)->first();
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }



    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาระบุ user_id ให้เรียบร้อย', 404);
        } else if (!isset($request->title)) {
            return $this->returnErrorData('กรุณาระบุ title ให้เรียบร้อย', 404);
        } else if (!isset($request->description)) {
            return $this->returnErrorData('กรุณาระบุ description ให้เรียบร้อย', 404);
        }

        $user_id = $request->user_id;
        $title = $request->title;
        $description = $request->description;
        $branch_id = $request->branch_id;
        $punishment = $request->punishment;


        DB::beginTransaction();

        try {

            $Item = new Warning();
            $Item->branch_id = $login_branch_id;
            $Item->user_id = $user_id;
            $Item->date = now()->format('Y-m-d');
            $Item->title = $title;
            $Item->description = $description;
            $Item->punishment = $punishment ?? "ตักเตือน";

            $Item->save();


            //log
            $userId = "admin";
            $type = 'เพิ่มรายการใบเตือน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $request->name;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', $Item);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        //
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $Item = Warning::with([
            'user:id,branch_id,first_name,last_name,sex,head_id',        // ต้อง include branch_id
            'user.branch',
            'user.branch.company',
            'branch.company',
            'approver:id,branch_id,first_name,last_name,sex',
            'acknowledger:id,branch_id,first_name,last_name,sex',
        ])->find($id);
        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }




    public function update(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->updateWarning($id, $request, $loginBy);
    }

    public function updateWarningLine(Request $request, $id)
    {
        $line_id = $request->line_id;

        if (!isset($line_id)) {
            return $this->returnErrorData('[line_id] Data Not Found', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->updateWarning($id, $request, $loginBy);
    }

    public function updateWarning($id, $request, $loginBy)
    {
        //

        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('ไม่พบข้อมูล id', 404);
        }


        DB::beginTransaction();
        try {

            $Item = Warning::with('user')->find($id);
            if (!$Item) {
                return $this->returnErrorData('ไม่พบข้อมูลในระบบ', 404);
            }

            if (!$Item->user->head_id) {
                return $this->returnErrorData('กรุณาเลือกหัวหน้างานของพนักงานคนนี้ ก่อนดำเนินการ', 404);
            }

            $Item->title = $request->title ?? $Item->title;
            $Item->description = $request->description ?? $Item->description;
            $Item->punishment = $request->punishment ?? $Item->punishment;


            if (isset($request->approved_by)) {
                $Item->approved_by =  $loginBy->user_id;
                $Item->status = "approved";
                $Item->approved_at = now();
            }
            if (isset($request->acknowledged_by)) {
                $Item->acknowledged_by = $loginBy->user_id;
                $Item->status = "finish";
                $Item->acknowledged_at = now();
            }


            $Item->save();

            //log
            $userId = "admin";
            $type = 'แก้ไขรายการใบเตือน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $Item->username;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('ดำเนินการสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        //
        $loginBy = $request->login_by;
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();
        try {
            $Item = Warning::find($id);
            if (!$Item) {
                return $this->returnErrorData('ไม่พบข้อมูลในระบบ', 404);
            }

            $Item->delete();

            //log
            $userId = "admin";
            $type = 'ลบรายการใบเตือน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $Item->username;
            $this->Log($userId, $description, $type);
            //

            DB::commit();
            return $this->returnUpdate('ลบข้อมูลสำเร็จ');
        } catch (\Throwable $e) {
            DB::rollback();
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }
    }


    public function reportWarnings(Request $request)
    {

        $year = $request->year ?? now()->year;
        $user_id = $request->user_id ?? null;


        $loginBy = $request->login_by;


        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_branch_id = $request->login_branch_id;


        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;

        if ($length > 0) {
            $page = intval($start / $length) + 1;
        } else {
            $page = 1;
        }



        $col = array('id', 'branch_id', 'department_id', 'position_id', 'permission_id', 'user_id', 'phone_no', 'first_name', 'last_name', 'email', 'sex', 'work_type', 'created_at', 'updated_at');

        $orderby = array('id', 'branch_id', 'department_id', 'position_id', 'permission_id', 'user_id', 'phone_no', 'first_name', 'last_name', 'email', 'sex', 'work_type', 'created_at', 'updated_at');


        // ใบเตือนทั้งหมด
        $userWarnings = Warning::where('status', 'finish')
            ->whereYear('date', $year);

        if ($user_id) {
            $userWarnings->where('user_id', $user_id);
        }

        if ($login_branch_id) {
            $userWarnings->where('branch_id', $login_branch_id);
        }


        // ดึง user_id ที่มีใบเตือน finish
        $getuserIdWarning = $userWarnings->groupBy('user_id')
            ->pluck('user_id'); //array


        $d = User::whereIn('id', $getuserIdWarning);



        if (isset($order[0]['column'], $order[0]['dir'])) {
            $columns = $orderby;
            $colIndex = $order[0]['column'];
            $dir = $order[0]['dir'];

            if (isset($columns[$colIndex])) {
                $d->orderBy($columns[$colIndex], $dir);
            }
        } else {
            $d->orderBy('id', 'desc');
        }


        if (isset($search['value']) && $search['value'] !== '') {

            $d->Where(function ($query) use ($search, $col) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });

                // search with
                $query = $this->withUser($query, $search);
            });
        }


        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);


        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;


                // ดึงใบเตือนของ user คนนี้
                $warninglist = Warning::where('user_id', $d[$i]->id)
                    ->where('branch_id', $login_branch_id)
                    ->where('status', 'finish')
                    ->whereYear('date', $year)
                    ->orderBy('date', 'desc')
                    ->get();


                //  ข้อมูล approved_by / acknowledged_by
                foreach ($warninglist as $warn) {
                    $warn->approved_by = $warn->approver->first_name . ' ' . $warn->approver->last_name;
                    $warn->acknowledged_by = $warn->acknowledger->first_name . ' ' . $warn->acknowledger->last_name;

                    unset($warn->approver, $warn->acknowledger);
                }

                $count = $warninglist->count();

                $d[$i]->warning_count = $count;
                $d[$i]->status_message = $count >= 2 ? 'ใบเตือนครบแล้ว' : null;
                $d[$i]->warnings = $warninglist;
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }
}
