<?php

namespace App\Http\Controllers;

use App\Imports\WorkShiftImport;
use App\Models\Holiday;
use App\Models\Leave_table_date;
use App\Models\User_attendance;
use App\Models\Work_shift;
use App\Models\Work_shift_time;
use App\Models\Zk_time;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class WorkShiftController extends Controller
{

    public function getWorkShiftCalendar(Request $request)
    {
        $year = $request->year;
        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        if (!isset($year)) {
            return $this->returnErrorData('กรุณาระบุปี', 404);
        } elseif (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $userID = $loginBy->id;
        $personnelID = $loginBy->user_id;
        $workShiftID = $loginBy->work_shift_id;

        $startOfYear = Carbon::create($year, 1, 1)->toDateString();
        $endOfYear = Carbon::create($year, 12, 31)->toDateString();

        $dateInPeriod = $this->dateInPeriod($startOfYear, $endOfYear);

        // Preload all necessary data
        $holiday = Holiday::whereBetween('date', [$startOfYear, $endOfYear]);

        if ($login_branch_id) {
            $holiday->where('branch_id', $login_branch_id);
        }

        $holidays = $holiday->get()->keyBy('date');

        $leaveDates = Leave_table_date::with(['leave_table.leave_type'])
            ->whereBetween('date', [$startOfYear, $endOfYear])
            ->whereHas('leave_table', function ($q) use ($userID) {
                $q->where('user_id', $userID)->where('status', 'approved');
            })->get()->keyBy('date');

        $misses = User_attendance::where('user_id', $userID)
            ->whereBetween('date', [$startOfYear, $endOfYear])
            ->where('type', 'miss')->get()->keyBy('date');

        $workTimes = Work_shift_time::with('work_shift')
            ->where('work_shift_id', $workShiftID)
            ->get()->keyBy('day');

        $zkTimes = Zk_time::where('personnel_id', $personnelID)
            ->whereBetween('time', [$startOfYear . ' 00:00:00', $endOfYear . ' 23:59:59'])
            ->orderBy('time')
            ->get()
            ->groupBy(function ($item) {
                return Carbon::parse($item->time)->toDateString();
            });

        $workData = [];

        foreach ($dateInPeriod as $date) {
            $dayAbbr = date('D', strtotime($date));
            $status = 'normal';
            $remark = null;
            $timeIn = null;
            $timeOut = null;

            if (isset($holidays[$date])) {
                $status = 'holiday';
                $remark = $holidays[$date]->name;
            } elseif (isset($leaveDates[$date])) {
                $leave = $leaveDates[$date]->leave_table;
                $status = 'leave';
                $remark = $leave->leave_type->name ?? null;
                $timeIn = $leave->time_start;
                $timeOut = $leave->time_end;
            } else {
                $workTime = $workTimes[$dayAbbr] ?? null;
                if ($workTime && $workTime->status) {
                    $timeIn = $workTime->time_in;
                    $timeOut = $workTime->time_out;
                    if ($loginBy->work_shift_group_id == 1) {
                        $status = isset($misses[$date]) ? 'miss' : 'normal';
                    } else {
                        $status = 'normal'; // ผู้บริหาร
                    }
                } else {
                    $status = 'off';
                }
            }

            $timeCheckIn = null;
            $timeCheckOut = null;

            if (isset($zkTimes[$date])) {
                $logs = $zkTimes[$date];
                $timeCheckIn = isset($logs[0]) ? date('H:i:s', strtotime($logs[0]->time)) : null;
                $timeCheckOut = isset($logs[count($logs) - 1]) ? date('H:i:s', strtotime($logs[count($logs) - 1]->time)) : null;
            }

            $workData[] = [
                'date' => $date,
                'status' => $status,
                'remark' => $remark,
                'time_in' => $timeIn,
                'time_out' => $timeOut,
                'time_check_in' => $timeCheckIn,
                'time_check_out' => $timeCheckOut,
            ];
        }

        return $this->returnSuccess('Successful', $workData);
    }


    public function getWorkShift(Request $request)
    {

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $WorkShift = Work_shift::with('user_create')
            ->with('work_shift_times')
            ->where('status', 1);

        if ($login_branch_id) {
            $WorkShift->where('branch_id', $login_branch_id);
        }

        $WorkShift = $WorkShift->get()->toarray();

        if (!empty($WorkShift)) {

            for ($i = 0; $i < count($WorkShift); $i++) {
                $WorkShift[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $WorkShift);
    }

    public function WorkShiftPage(Request $request)
    {
        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $col = array('id', 'name', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Work_shift::select($col)->with('user_create')
            ->with('work_shift_times')
            ->with('users.position');

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }


        $d->orderby($col[$order[0]['column']], $order[0]['dir']);

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $work_shift_time = $request->work_shift_time;
        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        if (!isset($request->name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อกลุ่มการทำงานด้วย', 404);
        } else if (empty($work_shift_time)) {
            return $this->returnErrorData('กรุณาระบุรายละเอียดวันทำงานด้วย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        $checkName = Work_shift::where('name', $name)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('มีชื่อกลุ่มการทำงานนี้ในระบบแล้ว', 404);
        } else {

            DB::beginTransaction();

            try {

                $WorkShift = new Work_shift();
                $WorkShift->branch_id = $login_branch_id;
                $WorkShift->name = $name;
                $WorkShift->status = 1;

                $WorkShift->create_by = $loginBy->user_id;

                $WorkShift->save();

                if (!empty($work_shift_time)) {

                    //add work shift time
                    for ($i = 0; $i < count($work_shift_time); $i++) {

                        //add
                        $WorkShiftTime  = new  Work_shift_time();
                        $WorkShiftTime->work_shift_id = $WorkShift->id;
                        $WorkShiftTime->day = $work_shift_time[$i]['day'];

                        $WorkShiftTime->time_in = $work_shift_time[$i]['time_in'];
                        $WorkShiftTime->time_in_start = $work_shift_time[$i]['time_in_start'];
                        $WorkShiftTime->time_in_end = $work_shift_time[$i]['time_in_end'];

                        $WorkShiftTime->time_out = $work_shift_time[$i]['time_out'];
                        $WorkShiftTime->time_out_start = $work_shift_time[$i]['time_out_start'];
                        $WorkShiftTime->time_out_end = $work_shift_time[$i]['time_out_end'];

                        $WorkShiftTime->time_brake_in = $work_shift_time[$i]['time_brake_in'];
                        $WorkShiftTime->time_brake_in_start = $work_shift_time[$i]['time_brake_in_start'];
                        $WorkShiftTime->time_brake_in_end = $work_shift_time[$i]['time_brake_in_end'];

                        $WorkShiftTime->time_brake_out = $work_shift_time[$i]['time_brake_out'];
                        $WorkShiftTime->time_brake_out_start = $work_shift_time[$i]['time_brake_out_start'];
                        $WorkShiftTime->time_brake_out_end = $work_shift_time[$i]['time_brake_out_end'];
                        $WorkShiftTime->status = $work_shift_time[$i]['status'];

                        $WorkShiftTime->save();
                    }
                }

                //log
                $userId = $loginBy->user_id;
                $type = 'Add WorkShift';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $WorkShift = Work_shift::with('user_create')
            ->with('work_shift_times')
            ->find($id);
        return $this->returnSuccess('Successful', $WorkShift);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $work_shift_time = $request->work_shift_time;
        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;


        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        $checkName = Work_shift::where('id', '!=', $id)
            ->where('name', $name);

        if ($login_branch_id) {
            $checkName->where('branch_id', $login_branch_id);
        }

        $checkName = $checkName->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $WorkShift = Work_shift::with('work_shift_times')->find($id);

                $WorkShift->name = $name;
                $WorkShift->status = $request->status;

                $WorkShift->update_by = $loginBy->user_id;
                $WorkShift->updated_at = Carbon::now()->toDateTimeString();

                $WorkShift->save();

                if (!empty($work_shift_time)) {

                    //del
                    for ($i = 0; $i < count($WorkShift->work_shift_times); $i++) {
                        $WorkShift->work_shift_times[$i]->delete();
                    }

                    //add work shift time
                    for ($i = 0; $i < count($work_shift_time); $i++) {

                        //add
                        $WorkShiftTime  = new  Work_shift_time();
                        $WorkShiftTime->work_shift_id = $WorkShift->id;
                        $WorkShiftTime->day = $work_shift_time[$i]['day'];

                        $WorkShiftTime->time_in = $work_shift_time[$i]['time_in'];
                        $WorkShiftTime->time_in_start = $work_shift_time[$i]['time_in_start'];
                        $WorkShiftTime->time_in_end = $work_shift_time[$i]['time_in_end'];

                        $WorkShiftTime->time_out = $work_shift_time[$i]['time_out'];
                        $WorkShiftTime->time_out_start = $work_shift_time[$i]['time_out_start'];
                        $WorkShiftTime->time_out_end = $work_shift_time[$i]['time_out_end'];

                        $WorkShiftTime->time_brake_in = $work_shift_time[$i]['time_brake_in'];
                        $WorkShiftTime->time_brake_in_start = $work_shift_time[$i]['time_brake_in_start'];
                        $WorkShiftTime->time_brake_in_end = $work_shift_time[$i]['time_brake_in_end'];

                        $WorkShiftTime->time_brake_out = $work_shift_time[$i]['time_brake_out'];
                        $WorkShiftTime->time_brake_out_start = $work_shift_time[$i]['time_brake_out_start'];
                        $WorkShiftTime->time_brake_out_end = $work_shift_time[$i]['time_brake_out_end'];

                        $WorkShiftTime->status = $work_shift_time[$i]['status'];

                        $WorkShiftTime->save();
                    }
                }

                //log
                $userId = $loginBy->user_id;
                $type = 'Edit WorkShift';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $WorkShift->name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnUpdate('Successful operation');
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $WorkShift = Work_shift::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete WorkShift';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $WorkShift->name;
            $this->Log($userId, $description, $type);
            //

            $WorkShift->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    // public function ImportWorkShift(Request $request)
    // {

    //     $loginBy = $request->login_by;

    //     if (!isset($loginBy)) {
    //         return $this->returnErrorData('User information not found. Please login again', 404);
    //     }

    //     $file = request()->file('file');
    //     $fileName = $file->getClientOriginalName();

    //     $Data = Excel::toArray(new WorkShiftImport(), $file);
    //     $data = $Data[0];

    //     if (count($data) > 0) {

    //         $insert_data = [];

    //         for ($i = 0; $i < count($data); $i++) {

    //             $name = trim($data[$i]['name']);

    //             $row = $i + 2;

    //             if ($name == '') {
    //                 return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
    //             }

    //             //check row sample
    //             if ($name == 'SIMPLE-000') {
    //                 //
    //             } else {

    //                 // //check name
    //                 // $WorkShift = Work_shift::where('name', $name)->first();
    //                 // if ($WorkShift) {
    //                 //     return $this->returnErrorData('WorkShift ' . $name . ' was information information is already in the system', 404);
    //                 // }

    //                 //check dupicate data form file import
    //                 for ($j = 0; $j < count($insert_data); $j++) {

    //                     if ($name == $insert_data[$j]['name']) {
    //                         return $this->returnErrorData('WorkShift ' . $name . ' There is duplicate data in the import file', 404);
    //                     }
    //                 }
    //                 ///

    //                 $insert_data[] = array(
    //                     'name' => $name,
    //                     'status' => 1,
    //                     'created_at' => date('Y-m-d H:i:s'),
    //                     'updated_at' => date('Y-m-d H:i:s'),
    //                 );
    //             }

    //         }

    //         if (!empty($insert_data)) {

    //             DB::beginTransaction();

    //             try {

    //                 //updateOrInsert
    //                 for ($i = 0; $i < count($insert_data); $i++) {

    //                     DB::table('WorkShift')
    //                         ->updateOrInsert(
    //                             [
    //                                 'id' => trim($data[$i]['id']), //id
    //                             ],
    //                             $insert_data[$i]
    //                         );
    //                 }
    //                 //

    //                 DB::commit();

    //                 //log
    //                 $userId = $loginBy->user_id;
    //                 $type = 'Import WorkShift';
    //                 $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
    //                 $this->Log($userId, $description, $type);
    //                 //

    //                 DB::commit();

    //                 return $this->returnSuccess('Successful operation', []);

    //             } catch (\Throwable $e) {

    //                 DB::rollback();

    //                 return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
    //             }

    //         } else {
    //             return $this->returnErrorData('Data Not Found', 404);
    //         }

    //     } else {
    //         return $this->returnErrorData('Data Not Found', 404);
    //     }
    // }
}
