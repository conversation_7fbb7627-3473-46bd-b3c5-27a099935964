<?php

namespace App\Http\Controllers;

use App\Imports\GradeYellowCardImport;
use App\Models\Grade_yellow_card;
use App\Models\GradeYellowCard;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class GradeYellowCardController extends Controller
{

    public function getGradeYellowCard()
    {

        $GradeYellowCard = Grade_yellow_card::where('status', 1)->with('user_create')->get()->toarray();

        if (!empty($GradeYellowCard)) {

            for ($i = 0; $i < count($GradeYellowCard); $i++) {
                $GradeYellowCard[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $GradeYellowCard);
    }

    public function GradeYellowCardPage(Request $request)
    {

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $col = array('id', 'name', 'qty_start', 'qty_end', 'deduct', 'rate_up_salary', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Grade_yellow_card::select($col)->with('user_create')
            ->orderby($col[$order[0]['column']], $order[0]['dir']);
        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id','desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($request->name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อเกรดด้วย', 404);
        } else if (!isset($request->qty_start)) {
            return $this->returnErrorData('จำนวนใบเหลืองเริ่มต้น', 404);
        } else if (!isset($request->qty_end)) {
            return $this->returnErrorData('จำนวนใบเหลืองสิ้นสุด', 404);
        } else if (!isset($request->deduct)) {
            return $this->returnErrorData('จำนวนเงินหัก (บาท)', 404);
        } else if (!isset($request->rate_up_salary)) {
            return $this->returnErrorData('อัตราการปรับเงินเดือน', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        $checkName = Grade_yellow_card::where('name', $name)->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $GradeYellowCard = new Grade_yellow_card();
                $GradeYellowCard->name = $name;
                $GradeYellowCard->qty_start = $request->qty_start;
                $GradeYellowCard->qty_end = $request->qty_end;
                $GradeYellowCard->deduct = $request->deduct;
                $GradeYellowCard->rate_up_salary = $request->rate_up_salary;
                $GradeYellowCard->status = 1;

                $GradeYellowCard->create_by = $loginBy->user_id;

                $GradeYellowCard->save();

                //log
                $userId = $loginBy->user_id;
                $type = 'Add เกณฑ์ใบเหลืองสะสม';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $GradeYellowCard = Grade_yellow_card::find($id);
        return $this->returnSuccess('Successful', $GradeYellowCard);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        $checkName = Grade_yellow_card::where('id', '!=', $id)
            ->where('name', $name)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $GradeYellowCard = Grade_yellow_card::find($id);

                $GradeYellowCard->name = $name;
                $GradeYellowCard->qty_start = $request->qty_start;
                $GradeYellowCard->qty_end = $request->qty_end;
                $GradeYellowCard->deduct = $request->deduct;
                $GradeYellowCard->rate_up_salary = $request->rate_up_salary;
                $GradeYellowCard->status = $request->status;

                $GradeYellowCard->update_by = $loginBy->user_id;
                $GradeYellowCard->updated_at = Carbon::now()->toDateTimeString();

                $GradeYellowCard->save();
                //log
                $userId = $loginBy->user_id;
                $type = 'Edit เกณฑ์ใบเหลืองสะสม';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $GradeYellowCard->name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnUpdate('Successful operation');
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $GradeYellowCard = Grade_yellow_card::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete เกณฑ์ใบเหลืองสะสม';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $GradeYellowCard->name;
            $this->Log($userId, $description, $type);
            //

            $GradeYellowCard->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    // public function ImportGradeYellowCard(Request $request)
    // {

    //     $loginBy = $request->login_by;

    //     if (!isset($loginBy)) {
    //         return $this->returnErrorData('User information not found. Please login again', 404);
    //     }

    //     $file = request()->file('file');
    //     $fileName = $file->getClientOriginalName();

    //     $Data = Excel::toArray(new GradeYellowCardImport(), $file);
    //     $data = $Data[0];

    //     if (count($data) > 0) {

    //         $insert_data = [];

    //         for ($i = 0; $i < count($data); $i++) {

    //             $name = trim($data[$i]['name']);

    //             $row = $i + 2;

    //             if ($name == '') {
    //                 return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
    //             }

    //             //check row sample
    //             if ($name == 'SIMPLE-000') {
    //                 //
    //             } else {

    //                 // //check name
    //                 // $GradeYellowCard = Grade_yellow_card::where('name', $name)->first();
    //                 // if ($GradeYellowCard) {
    //                 //     return $this->returnErrorData('GradeYellowCard ' . $name . ' was information information is already in the system', 404);
    //                 // }

    //                 //check dupicate data form file import
    //                 for ($j = 0; $j < count($insert_data); $j++) {

    //                     if ($name == $insert_data[$j]['name']) {
    //                         return $this->returnErrorData('GradeYellowCard ' . $name . ' There is duplicate data in the import file', 404);
    //                     }
    //                 }
    //                 ///

    //                 $insert_data[] = array(
    //                     'name' => $name,
    //                     'status' => 1,
    //                     'created_at' => date('Y-m-d H:i:s'),
    //                     'updated_at' => date('Y-m-d H:i:s'),
    //                 );
    //             }
    //         }

    //         if (!empty($insert_data)) {

    //             DB::beginTransaction();

    //             try {

    //                 //updateOrInsert
    //                 for ($i = 0; $i < count($insert_data); $i++) {

    //                     DB::table('เกณฑ์ใบเหลืองสะสม')
    //                         ->updateOrInsert(
    //                             [
    //                                 'id' => trim($data[$i]['id']), //id
    //                             ],
    //                             $insert_data[$i]
    //                         );
    //                 }
    //                 //

    //                 DB::commit();

    //                 //log
    //                 $userId = $loginBy->user_id;
    //                 $type = 'Import เกณฑ์ใบเหลืองสะสม';
    //                 $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
    //                 $this->Log($userId, $description, $type);
    //                 //

    //                 DB::commit();

    //                 return $this->returnSuccess('Successful operation', []);
    //             } catch (\Throwable $e) {

    //                 DB::rollback();

    //                 return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
    //             }
    //         } else {
    //             return $this->returnErrorData('Data Not Found', 404);
    //         }
    //     } else {
    //         return $this->returnErrorData('Data Not Found', 404);
    //     }
    // }
}
