<?php

namespace App\Http\Controllers;

use App\Imports\LeaveTableImport;
use App\Models\Holiday;
use App\Models\Leave_table;
use App\Models\Leave_table_date;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\User_yellow_card;
use App\Models\Work_shift_time;
use App\Models\WorkTime;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class LeaveTableController extends Controller
{

    public function getLeaveTable(Request $request)
    {

        $user_id = $request->user_id;
        $status = $request->status;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $LeaveTable = Leave_table::with('user')
            ->with('leave_type')
            ->with('user_status')
            ->with('user_head')
            ->with('user_hr');
        if ($user_id) {
            $LeaveTable->where('user_id', $user_id);
        }

        if ($status) {
            $LeaveTable->where('status', $status);
        }
        if($login_branch_id){
            $LeaveTable->where('branch_id', $login_branch_id);
        }

        $LeaveTable =  $LeaveTable->get()->toarray();

        if (!empty($LeaveTable)) {

            for ($i = 0; $i < count($LeaveTable); $i++) {
                $LeaveTable[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $LeaveTable);
    }

    public function LeaveTablePage(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->Page($request);
    }

    public function LeaveTablePageLine(Request $request)
    {

        $line_id = $request->line_id;
        $line_head_id = $request->line_head_id;

        if ($line_id) {
            $loginBy = User::where('line_id', $line_id)->first();
        } else if ($line_head_id) {
            $loginBy = User::where('line_id', $line_head_id)->first();
        }

        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $getPermissionViewData = $this->getPermissionViewData($loginBy);

        $request->request->add([
            'login_id' => $getPermissionViewData['login_id'],
            'login_by' => $getPermissionViewData['login_by'],
            'login_permission_view' => $getPermissionViewData['login_permission_view'],
            'login_user_id' => $getPermissionViewData['login_user_id'],
            'login_branch_id' => $getPermissionViewData['login_branch_id'],
            'login_company_id' => $getPermissionViewData['login_company_id'],
        ]);


        return $this->Page($request);
    }

    public function Page($request)
    {
        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $user_id = $request->user_id;
        $status = $request->status;
        $head_id = $request->head_id;

        $line_id = $request->line_id;
        $line_head_id = $request->line_head_id;

        $date = $request->date;
        $date_start = $request->date_start;
        $date_end = $request->date_end;

        $leave_type_id = $request->leave_type_id;
        $type = $request->type;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        $col = array('id', 'branch_id', 'user_id', 'leave_type_id', 'type', 'date_start', 'date_end', 'time_start', 'time_end', 'description', 'file', 'qty_hour', 'status', 'status_by', 'status_at', 'remark', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Leave_table::select($col)->with('user')
            ->with('leave_type')
            ->with('user_status')
            ->with('user_head')
            ->with('user_hr');

        // if ($login_company_id) {
        //     $d->WhereHas('user', function ($query) use ($login_company_id) {
        //         $query->WhereHas('branch', function ($query) use ($login_company_id) {
        //             $query->where('company_id', $login_company_id);
        //         });
        //     });
        // }

        if($login_branch_id){
            $d->where('branch_id', $login_branch_id);
        }

        if ($line_id) {
            $d->WhereHas('user', function ($query) use ($line_id) {
                $query->where('line_id', $line_id);
            });
        }

        if ($line_head_id) {
            $d->WhereHas('user', function ($query) use ($line_head_id) {
                $query->where('line_id', $line_head_id);
            });
        }

        if ($user_id) {
            $d->where('user_id', $user_id);
        }

        if ($status) {
            $d->where('status', $status);
        }

        if ($head_id) {
            $d->where('head_id', $head_id);
        }

        if ($date_start && $date_end) {
            $d->whereBetween('date_start', [$date_start, $date_end]);
        }

        if ($date) {
            $d->where('created_at', 'like', '%' . $date . '%');
        }

        if ($leave_type_id) {
            $d->where('leave_type_id', $leave_type_id);
        }
        if ($type) {
            $d->where('type', $type);
        }

        $d->orderby($col[$order[0]['column']], $order[0]['dir']);
        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;

                $d[$i]->qty_day = $d[$i]->qty_hour / 8;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $user_id = $request->user_id;
        $loginBy = $request->login_by;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาเลือกพนักงาน', 404);
        } else  if (!isset($request->leave_type_id)) {
            return $this->returnErrorData('กรุณาระบุประเภทการลา', 404);
        } else  if (!isset($request->type)) {
            return $this->returnErrorData('กรุณาระบุประเภท', 404);
        } else  if (!isset($request->date_start)) {
            return $this->returnErrorData('กรุณาระบุวันที่ลา', 404);
        } else  if (!isset($request->date_end)) {
            return $this->returnErrorData('กรุณาระบุวันที่สิ้นสุดการลา', 404);
        } else  if (!isset($request->time_start) && $request->type == 'hour') {
            return $this->returnErrorData('กรุณาระบุเวลาเริ่มต้น', 404);
        } else  if (!isset($request->time_end) && $request->type == 'hour') {
            return $this->returnErrorData('กรุณาระบุเวลาสิ้นสุด', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        } else if (!isset($loginBy->head_id)) {
            return $this->returnErrorData('กรุณาแจ้งเจ้าหน้าที่ HR เพื่อระบุหัวหน้างานก่อนดำเนินการ', 404);
        } else if (!isset($loginBy->work_shift_id)) {
            return $this->returnErrorData('กรุณาแจ้งเจ้าหน้าที่ HR เพื่อระบุกลุ่มกะการทำงานก่อนดำเนินการ', 404);
        }

        return $this->addLeave($user_id, $request, $loginBy);
    }

    public function addLine(Request $request)
    {

        $line_id = $request->line_id;

        if (!isset($request->line_id)) {
            return $this->returnErrorData('กรุณาส่ง line_id', 404);
        } else  if (!isset($request->leave_type_id)) {
            return $this->returnErrorData('กรุณาระบุประเภทการลา', 404);
        } else  if (!isset($request->type)) {
            return $this->returnErrorData('กรุณาระบุประเภท', 404);
        } else  if (!isset($request->date_start)) {
            return $this->returnErrorData('กรุณาระบุวันที่ลา', 404);
        } else  if (!isset($request->date_end)) {
            return $this->returnErrorData('กรุณาระบุวันที่สิ้นสุดการลา', 404);
        } else  if (!isset($request->time_start) && $request->type == 'hour') {
            return $this->returnErrorData('กรุณาระบุเวลาเริ่มต้น', 404);
        } else  if (!isset($request->time_end) && $request->type == 'hour') {
            return $this->returnErrorData('กรุณาระบุเวลาสิ้นสุด', 404);
        }


        $loginBy = User::where('line_id', $line_id)->first();

        if (!$loginBy) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่', 404);
        } else if (!isset($loginBy->head_id)) {
            return $this->returnErrorData('กรุณาแจ้งเจ้าหน้าที่ HR เพื่อระบุหัวหน้างานก่อนดำเนินการ', 404);
        } else if (!isset($loginBy->work_shift_id)) {
            return $this->returnErrorData('กรุณาแจ้งเจ้าหน้าที่ HR เพื่อระบุกลุ่มกะการทำงานก่อนดำเนินการ', 404);
        }

        $user_id = $loginBy->id;
        return  $this->addLeave($user_id, $request, $loginBy);
    }

    public function addLeave($user_id, $request, $loginBy)
    {

        DB::beginTransaction();

        try {
            //check user
            $user = User::with('position')->find($user_id);
            if (!$user) {
                return $this->returnErrorData('ไม่พบ user ในระบบ', 404);
            }
            //

            //check diff date
            $dateInPeriod = $this->dateInPeriod($request->date_start, $request->date_end);


            $qty_hour_work_shift = 0;
            $qty_hour = 0;
            for ($i = 0; $i < count($dateInPeriod); $i++) {

                //check working day
                $strDate = date('D', strtotime($dateInPeriod[$i]));

                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                if ($user->work_shift_id) {
                    $workTime->where('work_shift_id', $user->work_shift_id);
                }
                $WorkTime =  $workTime->first();

                if ($WorkTime) {

                    //check holiday
                    if ($WorkTime->status == true) {

                        $time1 = null;
                        $time2 = null;

                        //check type day or half_day_pm or half_day_am
                        if ($request->type == 'day') {
                            $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                            $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;
                        } else if ($request->type == 'half_day_pm') {
                            $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                            $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_brake_in;
                        } else if ($request->type == 'half_day_am') {
                            $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_brake_out;
                            $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;
                        } else if ($request->type == 'hour') {
                            $time1 = $dateInPeriod[$i] . ' ' .  $request->time_start;
                            $time2 = $dateInPeriod[$i] . ' ' .  $request->time_end;
                        }

                        //check type day or half_day
                        if ($request->type == 'day') {
                            //day
                            $qty_hour += ($this->TimeDiff($time1, $time2) - 1);
                        } else {
                            //half_day or hour
                            $qty_hour += $this->TimeDiff($time1, $time2);
                        }

                        //qty_hour_work_shift
                        if ($i == 0) {
                            $time_1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                            $time_2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;

                            $qty_hour_work_shift += ($this->TimeDiff($time_1, $time_2) - 1);
                        }
                    }
                }
            }

            if ($qty_hour <= 0) {
                return $this->returnErrorData('ไม่สามารถลาได้เนื่องจากเป็นวันหยุด', 404);
            }

            $checkLeavePermissionUser = $this->checkLeavePermissionUser($user_id, $qty_hour, $request->leave_type_id, $qty_hour_work_shift, date('Y', strtotime($request->date_start)));
            if ($checkLeavePermissionUser < 0) {
                return $this->returnErrorData('สิทธิ์การลาของคุณไม่เพียงพอ', 404);
            }
            //

            //duplicate leave

            //check type day or half_day_pm or half_day_am
            if ($request->type == 'day') {
                $time_start = $WorkTime->time_in;
                $time_end = $WorkTime->time_out;
            } else if ($request->type == 'half_day_pm') {
                $time_start = $WorkTime->time_in;
                $time_end = $WorkTime->time_brake_in;
            } else if ($request->type == 'half_day_am') {
                $time_start = $WorkTime->time_brake_out;
                $time_end = $WorkTime->time_out;
            } else if ($request->type == 'hour') {
                $time_start = $request->time_start;
                $time_end = $request->time_end;
            }

            for ($i = 0; $i < count($dateInPeriod); $i++) {

                $Leave_table =  Leave_table_date::where('date', $dateInPeriod[$i])
                    ->where(function ($query) use ($time_start, $time_end) {
                        $query->orwhere(function ($query) use ($time_start) {
                            $query->where('time_start', '<=', $time_start)
                                ->where('time_end', '>=', $time_start);
                        });

                        $query->orwhere(function ($query) use ($time_end) {
                            $query->where('time_start', '<=', $time_end)
                                ->where('time_end', '>=', $time_end);
                        });
                    })
                    ->WhereHas('leave_table', function ($query) use ($user_id) {
                        $query->where('user_id', $user_id);
                        $query->where('status', '!=', 'cancel');
                    })

                    ->first();

                if ($Leave_table) {
                    return $this->returnErrorData('คุณได้ทำการลาในช่วงเวลานี้ไปแล้ว', 404);
                }
            }

            $LeaveTable = new Leave_table();
            $LeaveTable->branch_id = $user->branch_id ?? $request->login_branch_id;
            $LeaveTable->user_id = $user_id;
            $LeaveTable->leave_type_id = $request->leave_type_id;
            $LeaveTable->date_start = $request->date_start;
            $LeaveTable->date_end = $request->date_end;
            $LeaveTable->type = $request->type;
            $LeaveTable->time_start = $time_start;
            $LeaveTable->time_end = $time_end;
            $LeaveTable->description = $request->description;
            $LeaveTable->qty_hour = $qty_hour;
            $LeaveTable->file = $request->file;

            $LeaveTable->status = 'open';

            $LeaveTable->create_by = $loginBy->user_id;
            $LeaveTable->head_id = $loginBy->head_id; //id หัวหน้้า

            $LeaveTable->save();

            for ($i = 0; $i < count($dateInPeriod); $i++) {

                $strDate = date('D', strtotime($dateInPeriod[$i]));

                //check working day
                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                if ($LeaveTable->user->work_shift_id) {
                    $workTime->where('work_shift_id', $LeaveTable->user->work_shift_id);
                }
                $WorkTime =  $workTime->first();

                if ($WorkTime) {

                    //check holiday
                    $Holiday =  Holiday::where('date', $dateInPeriod[$i])->first();
                    if (!$Holiday) {

                        if ($WorkTime->status == true) {

                            $Leave_table_date = new Leave_table_date();
                            $Leave_table_date->leave_table_id = $LeaveTable->id;
                            $Leave_table_date->date = $dateInPeriod[$i];
                            $Leave_table_date->time_start = $LeaveTable->time_start;
                            $Leave_table_date->time_end = $LeaveTable->time_end;
                            $Leave_table_date->save();
                        }
                    }
                }
            }

            DB::commit();

            return $this->returnSuccess('Successful operation', $LeaveTable);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $LeaveTable = Leave_table::with('user')
            ->with('leave_type')
            ->with('user_status')
            ->with('user_head')
            ->with('user_hr')
            ->find($id);

        if ($LeaveTable) {
            $LeaveTable->qty_day = $LeaveTable->qty_hour / 8;
        }
        return $this->returnSuccess('Successful', $LeaveTable);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $user_id = $request->user_id;
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data NLeaveTable Found', 404);
        }

        DB::beginTransaction();

        try {

            //check user
            $user = User::with('position')->find($request->user_id);
            if (!$user) {
                return $this->returnErrorData('ไม่พบ user ในระบบ', 404);
            }
            //

            //check user
            $user = User::with('position')->find($request->user_id);
            if (!$user) {
                return $this->returnErrorData('ไม่พบ user ในระบบ', 404);
            }
            //

            //check diff date
            $dateInPeriod = $this->dateInPeriod($request->date_start, $request->date_end);

            $qty_hour = 0;
            $qty_hour_work_shift =  0;
            for ($i = 0; $i < count($dateInPeriod); $i++) {

                //check working day
                $strDate = date('D', strtotime($dateInPeriod[$i]));

                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                if ($user->work_shift_id) {
                    $workTime->where('work_shift_id', $user->work_shift_id);
                }
                $WorkTime =  $workTime->first();

                if ($WorkTime) {

                    //check holiday
                    if ($WorkTime->status == true) {

                        $time1 = null;
                        $time2 = null;

                        //check type day or half_day_pm or half_day_am
                        if ($request->type == 'day') {
                            $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                            $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;
                        } else if ($request->type == 'half_day_pm') {
                            $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                            $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_brake_in;
                        } else if ($request->type == 'half_day_am') {
                            $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_brake_out;
                            $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;
                        }

                        //check type day or half_day
                        if ($request->type == 'day') {
                            //day
                            $qty_hour += ($this->TimeDiff($time1, $time2) - 1);
                        } else {
                            //half_day
                            $qty_hour += $this->TimeDiff($time1, $time2);
                        }

                        //qty_hour_work_shift
                        if ($i == 0) {
                            $time_1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                            $time_2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;

                            $qty_hour_work_shift += ($this->TimeDiff($time_1, $time_2) - 1);
                        }
                    }
                }
            }

            $checkLeavePermissionUser = $this->checkLeavePermissionUser($request->user_id, $qty_hour, $request->leave_type_id, $qty_hour_work_shift, date('Y', strtotime($request->date_start)));

            if ($checkLeavePermissionUser < 0) {
                return $this->returnErrorData('สิทธิ์การลาของคุณไม่เพียงพอ', 404);
            }
            //

            //duplicate leave

            //check type day or half_day_pm or half_day_am
            if ($request->type == 'day') {
                $time_start = $WorkTime->time_in;
                $time_end = $WorkTime->time_out;
            } else if ($request->type == 'half_day_pm') {
                $time_start = $WorkTime->time_in;
                $time_end = $WorkTime->time_brake_in;
            } else if ($request->type == 'half_day_am') {
                $time_start = $WorkTime->time_brake_out;
                $time_end = $WorkTime->time_out;
            }

            for ($i = 0; $i < count($dateInPeriod); $i++) {

                $Leave_table =  Leave_table_date::where('date', $dateInPeriod[$i])
                    ->where(function ($query) use ($time_start, $time_end) {
                        $query->orwhere(function ($query) use ($time_start) {
                            $query->where('time_start', '<=', $time_start)
                                ->where('time_end', '>=', $time_start);
                        });

                        $query->orwhere(function ($query) use ($time_end) {
                            $query->where('time_start', '<=', $time_end)
                                ->where('time_end', '>=', $time_end);
                        });
                    })
                    ->WhereHas('leave_table', function ($query) use ($user_id) {
                        $query->where('user_id', $user_id);
                        $query->where('status', '!=', 'cancel');
                    })

                    ->first();

                if ($Leave_table) {
                    return $this->returnErrorData('คุณได้ทำการลาในช่วงเวลานี้ไปแล้ว', 404);
                }
            }

            //


            // //check diff date
            // $dateInPeriod = $this->dateInPeriod($request->date_start, $request->date_end);

            // $qty_hour = 0;
            // for ($i = 0; $i < count($dateInPeriod); $i++) {

            //     //check working day
            //     $strDate = date('D', strtotime($dateInPeriod[$i]));

            //     $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
            //     if ($user->work_shift_id) {
            //         $workTime->where('work_shift_id', $user->work_shift_id);
            //     }
            //     $WorkTime =  $workTime->first();

            //     if ($WorkTime) {

            //         //check holiday
            //         if ($WorkTime->status == true) {

            //             if ($request->time_start < $WorkTime->time_in) {
            //                 return $this->returnErrorData('กรุณาระบุเวลาให้ถูกต้อง', 404);
            //             }

            //             if ($request->time_end > $WorkTime->time_out) {
            //                 return $this->returnErrorData('กรุณาระบุเวลาให้ถูกต้อง', 404);
            //             }

            //             $time1 = $dateInPeriod[$i] . ' ' .  $request->time_start;
            //             $time2 = $dateInPeriod[$i] . ' ' .  $request->time_end;

            //             if ('12:00' >= $request->time_start && '12:00' <= $request->time_end) {
            //                 $qty_hour += $this->TimeDiff($time1, $time2) - 1;
            //             } else {
            //                 $qty_hour += $this->TimeDiff($time1, $time2);
            //             }
            //         }
            //     }
            // }

            // // dd($qty_hour);

            // $checkLeavePermissionUser = $this->checkLeavePermissionUser($request->user_id, $qty_hour, $request->leave_type_id);

            // if ($checkLeavePermissionUser < 0) {
            //     return $this->returnErrorData('สิทธิ์การลาของคุณไม่เพียงพอ', 404);
            // }
            // //

            // //duplicate leave
            // $time_start = $request->time_start;
            // $time_end = $request->time_end;

            // for ($i = 0; $i < count($dateInPeriod); $i++) {

            //     $Leave_table =  Leave_table_date::where('date', $dateInPeriod[$i])
            //         ->where(function ($query) use ($time_start, $time_end) {
            //             $query->orwhere(function ($query) use ($time_start) {
            //                 $query->where('time_start', '<=', $time_start)
            //                     ->where('time_end', '>=', $time_start);
            //             });

            //             $query->orwhere(function ($query) use ($time_end) {
            //                 $query->where('time_start', '<=', $time_end)
            //                     ->where('time_end', '>=', $time_end);
            //             });
            //         })
            //         ->WhereHas('leave_table', function ($query) use ($user_id, $id) {
            //             $query->where('user_id', $user_id);
            //             $query->where('status', '!=', 'cancel');
            //             $query->where('id', '!=', $id);
            //         })

            //         ->first();

            //     if ($Leave_table) {
            //         return $this->returnErrorData('คุณได้ทำการลาในช่วงเวลานี้ไปแล้ว', 404);
            //     }
            // }

            // if ($Leave_table) {
            //     return $this->returnErrorData('คุณได้ทำการลาในช่วงเวลานี้ไปแล้ว', 404);
            // }
            // //

            $LeaveTable = Leave_table::with('leave_table_dates')->find($id);

            $LeaveTable->leave_type_id = $request->leave_type_id;
            $LeaveTable->date_start = $request->date_start;
            $LeaveTable->date_end = $request->date_end;
            $LeaveTable->type = $request->type;
            // $LeaveTable->time_start = $time_start;
            // $LeaveTable->time_end = $time_end;
            $LeaveTable->description = $request->description;
            $LeaveTable->qty_hour = $qty_hour;
            $LeaveTable->file = $request->file;

            $LeaveTable->update_by = $loginBy->user_id;
            $LeaveTable->updated_at = Carbon::now()->toDateTimeString();

            $LeaveTable->save();


            //del
            for ($i = 0; $i < count($LeaveTable->leave_table_dates); $i++) {
                $LeaveTable->leave_table_dates[$i]->delete();
            }

            //add
            for ($i = 0; $i < count($dateInPeriod); $i++) {

                $strDate = date('D', strtotime($dateInPeriod[$i]));

                //check working day
                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                if ($LeaveTable->user->work_shift_id) {
                    $workTime->where('work_shift_id', $LeaveTable->user->work_shift_id);
                }
                $WorkTime =  $workTime->first();

                if ($WorkTime) {

                    //check holiday
                    $Holiday =  Holiday::where('date', $dateInPeriod[$i])->first();
                    if (!$Holiday) {

                        if ($WorkTime->status == true) {

                            $Leave_table_date = new Leave_table_date();
                            $Leave_table_date->leave_table_id = $LeaveTable->id;
                            $Leave_table_date->date = $dateInPeriod[$i];
                            $Leave_table_date->time_start = $LeaveTable->time_start;
                            $Leave_table_date->time_end = $LeaveTable->time_end;
                            $Leave_table_date->save();
                        }
                    }
                }
            }


            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data NLeaveTable Found', 404);
        }

        DB::beginTransaction();

        try {

            $LeaveTable = Leave_table::find($id);

            $LeaveTable->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }


    public function ApprovedLeaveTable(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data NLeaveTable Found', 404);
        }

        return $this->ApprovedLeave($id, $request, $loginBy);
    }

    public function ApprovedLeaveTableLine(Request $request, $id)
    {
        $line_id = $request->line_id;

        if (!isset($line_id)) {
            return $this->returnErrorData('[line_id] Data NLeaveTable Found', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }
        return $this->ApprovedLeave($id, $request, $loginBy);
    }


    public function ApprovedLeave($id, $request, $loginBy)
    {
        DB::beginTransaction();

        try {

            $LeaveTable = Leave_table::with('user.position')
                ->with('leave_type')
                ->with('user_status')
                ->find($id);

            $LeaveTable->status = $request->status;

            if ($LeaveTable->status == 'process') {
                $LeaveTable->head_by = $loginBy->user_id;
                $LeaveTable->head_at = Carbon::now()->toDateTimeString();
            }

            if ($LeaveTable->status == 'approved') {

                //check diff date
                $dateInPeriod = $this->dateInPeriod($LeaveTable->date_start, $LeaveTable->date_end);

                $qty_hour = 0;
                $qty_hour_work_shift = 0;
                for ($i = 0; $i < count($dateInPeriod); $i++) {

                    $strDate = date('D', strtotime($dateInPeriod[$i]));

                    //check working day
                    $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                    if ($LeaveTable->user->work_shift_id) {
                        $workTime->where('work_shift_id', $LeaveTable->user->work_shift_id);
                    }
                    $WorkTime =  $workTime->first();


                    if ($WorkTime) {

                        //check holiday
                        $Holiday =  Holiday::where('date', $dateInPeriod[$i])->first();
                        if (!$Holiday) {

                            if ($WorkTime->status == true) {

                                $time1 = null;
                                $time2 = null;

                                //check type day or half_day_pm or half_day_am
                                if ($LeaveTable->type == 'day') {
                                    $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                                    $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;
                                } else if ($LeaveTable->type == 'half_day_pm') {
                                    $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                                    $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_brake_in;
                                } else if ($LeaveTable->type == 'half_day_am') {
                                    $time1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_brake_out;
                                    $time2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;
                                }

                                //check type day or half_day
                                if ($LeaveTable->type == 'day') {
                                    //day
                                    $qty_hour += ($this->TimeDiff($time1, $time2) - 1);
                                } else {
                                    //half_day
                                    $qty_hour += $this->TimeDiff($time1, $time2);
                                }

                                //qty_hour_work_shift
                                if ($i == 0) {
                                    $time_1 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_in;
                                    $time_2 = $dateInPeriod[$i] . ' ' .  $WorkTime->time_out;

                                    $qty_hour_work_shift = ($this->TimeDiff($time_1, $time_2) - 1);
                                }
                            }
                        }
                    }
                }

                $LeaveTable->qty_hour = $qty_hour;
                $checkLeavePermissionUser = $this->checkLeavePermissionUser($LeaveTable->user_id, $qty_hour, $LeaveTable->leave_type_id, $qty_hour_work_shift, date('Y', strtotime($LeaveTable->date_start)));

                if ($checkLeavePermissionUser < 0) {
                    return $this->returnErrorData('สิทธิ์การลาไม่เพียงพอ', 404);
                }
                //

                $LeaveTable->hr_id = $loginBy->id;
                $LeaveTable->status_by = $loginBy->user_id;
                $LeaveTable->status_at = Carbon::now()->toDateTimeString();
            }

            // if ($LeaveTable->status == 'approved') {

            //     //check diff date
            //     $dateInPeriod = $this->dateInPeriod($LeaveTable->date_start, $LeaveTable->date_end);

            //     $qty_hour = 0;
            //     for ($i = 0; $i < count($dateInPeriod); $i++) {

            //         //check working day
            //         $workTime = WorkTime::where('date', $dateInPeriod[$i]);

            //         if ($LeaveTable->user->position) {
            //             if ($LeaveTable->user->position->set_by_emp == true) {
            //                 $workTime->where('position_id', $LeaveTable->user->position_id);
            //                 $workTime->where('user_id', $LeaveTable->user->id);
            //             } else {
            //                 $workTime->where('position_id', $LeaveTable->user->position_id);
            //             }
            //         }
            //         $WorkTime =  $workTime->first();
            //         //

            //         if ($WorkTime->type == 'Work') {

            //             if ($LeaveTable->time_start < $WorkTime->time_in) {
            //                 return $this->returnErrorData('กรุณาระบุเวลาให้ถูกต้อง', 404);
            //             }

            //             if ($LeaveTable->time_end > $WorkTime->time_out) {
            //                 return $this->returnErrorData('กรุณาระบุเวลาให้ถูกต้อง', 404);
            //             }

            //             $time1 = $dateInPeriod[$i] . ' ' .  $LeaveTable->time_start;
            //             $time2 = $dateInPeriod[$i] . ' ' .  $LeaveTable->time_end;

            //             if ('12:00' >= $LeaveTable->time_start && '12:00' <= $LeaveTable->time_end) {
            //                 $qty_hour += $this->TimeDiff($time1, $time2) - 1;
            //             } else {
            //                 $qty_hour += $this->TimeDiff($time1, $time2);
            //             }
            //         }
            //     }
            //     $LeaveTable->qty_hour = $qty_hour;
            //     $checkLeavePermissionUser = $this->checkLeavePermissionUser($LeaveTable->user_id, $qty_hour, $LeaveTable->leave_type_id);

            //     if ($checkLeavePermissionUser < 0) {
            //         return $this->returnErrorData('สิทธิ์การลาไม่เพียงพอ', 404);
            //     }
            //     //

            //     //add user attendance
            //     for ($i = 0; $i < count($dateInPeriod); $i++) {

            //         $check = User_attendance::where('user_id', $LeaveTable->user_id)
            //             ->where('date', $dateInPeriod[$i])
            //             ->first();

            //         if ($check) {

            //             if ($qty_hour >= 8) {
            //                 //leave
            //                 $check->type = 'leave';
            //                 $check->leave_table_id = $LeaveTable->id;
            //                 $check->save();
            //             } else {
            //                 //leave_hour
            //                 $check->type = 'leave_hour';
            //                 $check->leave_table_id = $LeaveTable->id;
            //                 $check->save();
            //             }

            //             //update user yellow card
            //             $upUser_yellow_card = User_yellow_card::where('user_attendance_id', $check->id)->first();
            //             if ($upUser_yellow_card) {
            //                 $upUser_yellow_card->qty = 0;
            //                 $upUser_yellow_card->leave_table_id = $LeaveTable->id;
            //                 $upUser_yellow_card->save();
            //             }
            //             //
            //         }
            //     }


            //     //add user yellow card
            //     $YellowCard =  $this->calculateYellowCard('leave', $qty_hour, $LeaveTable->leave_type_id);

            //     $User_yellow_card = new User_yellow_card();
            //     $User_yellow_card->user_id = $LeaveTable->user_id;
            //     $User_yellow_card->qty = $YellowCard; //qty
            //     $User_yellow_card->leave_table_id = $LeaveTable->id;
            //     $User_yellow_card->user_attendance_id = null;
            //     $User_yellow_card->status = true;
            //     $User_yellow_card->save();
            //     //

            //     $LeaveTable->hr_id = $loginBy->user_id;
            //     $LeaveTable->status_by = $loginBy->user_id;
            //     $LeaveTable->status_at = Carbon::now()->toDateTimeString();
            // }

            //หัวหน้า cancel
            if ($request->status == 'head_cancel') {
                $LeaveTable->head_remark = $request->remark;

                $LeaveTable->status_by = $loginBy->user_id;
                $LeaveTable->status_at = Carbon::now()->toDateTimeString();
            }

            if ($LeaveTable->status == 'cancel') {
                $LeaveTable->remark = $request->remark;

                $LeaveTable->status_by = $loginBy->user_id;
                $LeaveTable->status_at = Carbon::now()->toDateTimeString();
            }


            $LeaveTable->save();


            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
}
