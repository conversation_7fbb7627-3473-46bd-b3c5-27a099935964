<?php

namespace App\Http\Controllers;

use App\Exports\TiktokshopsalesExport;
use App\Models\Branch;
use App\Models\Commission;
use App\Models\Config;
use App\Models\Customer;
use App\Models\Grade_yellow_card;
use App\Models\Item;
use App\Models\Item_trans;
use App\Models\LeaveType;
use App\Models\Location;
use App\Models\Sale_order;
use App\Models\Sale_order_line;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\User_yellow_card;
use App\Models\UserIncome;
use App\Models\Employee_salary;
use App\Models\Page;
use App\Models\UserDeduct;
use App\Models\Work_times;
use App\Models\WorkTelesale;
use App\Models\Zk_time;
use App\Models\Ot;
use App\Models\Leave_table;
use App\Models\Work_shift;
use App\Models\Work_shift_time;
use App\Models\Zkt_time_firstin_lastout;
use App\Models\IncomePaid;
use App\Models\DeductPaid;
use App\Models\Holiday;
use App\Models\Payroll;
use App\Models\PayrollRound;
use App\Models\WorkAds;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\CarbonPeriod;
use DateTime;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\URL;

class PdfController extends Controller
{

    public function previewDoc(Request $request)
    {
        $type = $request->type;

        $loginBy = $request->login_by;
        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $round = $request->round;
        $year = $request->year;
        $month = $request->month;

        $token = $this->generatePreviewToken($login_branch_id, $login_user_id, $round, $year, $month);

        $previewUrl = '';
        if ($type == 'tax_book_fifty') {
            $previewUrl = URL::to("/api/tax_book_fifty?token={$token}");
        }

        if ($type == 'attached_pnk') {
            $previewUrl = URL::to("/api/attached_pnk?token={$token}");
        }

        if ($type == 'export_bank') {
            $previewUrl = URL::to("/api/export_bank?token={$token}");
        }

         if ($type == 'export_social') {
            $previewUrl = URL::to("/api/export_social?token={$token}");
        }




        return redirect()->away($previewUrl);
    }


    public function tax_book_fifty(Request $request) // หนังสือรับรองการหักภาษี 50 ทวิ
    {

        $token = $request->token;
        $verifyPreviewToken =  $this->verifyPreviewToken($token);

        if (!$verifyPreviewToken) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_branch_id =  $verifyPreviewToken['login_branch_id'];
        $login_user_id =  $verifyPreviewToken['login_user_id'];
        $year =  $verifyPreviewToken['year'];

        $Branch = Branch::with('company')->find($login_branch_id);


        $hr_employee_id = $request->hr_employee_id;
        $year = $request->year;
        $month = $request->month;

        $hr_employee_type_id = $request->hr_employee_type_id;
        $hr_ci_mission_id = $request->hr_ci_mission_id;
        $hr_ci_department_id = $request->hr_ci_department_id;
        $hr_ci_work_id = $request->hr_ci_work_id;
        $hr_ci_unit_id = $request->hr_ci_unit_id;


        $Employee = [];

        // return ($Employee);

        // if (!$Employee->isNotEmpty()) {
        //     return $this->returnError('ไม่พบข้อมูลพนักงาน');
        // }

        //PDF
        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $mpdf = new \Mpdf\Mpdf([
            'fontDir' => array_merge($fontDirs, [
                base_path() . '/custom/font/directory',
            ]),
            'fontdata' => $fontData + [ // lowercase letters only in font key
                'th-sarabun-it' => [
                    'R' => 'THSarabunIT๙.ttf',
                    'I' => 'THSarabunIT๙ Italic.ttf',
                    'B' => 'THSarabunIT๙ Bold.ttf',
                    'BI' => 'THSarabunIT๙ BoldItalic.ttf',
                ],
                'th-sarabun' => [
                    'R' => 'THSarabun.ttf',
                    'I' => 'THSarabun Italic.ttf',
                    'B' => 'THSarabun Bold.ttf',
                    'BI' => 'THSarabun Bold Italic.ttf',
                ],
            ],
            'default_font' => 'th-sarabun',
            'mode' => 'utf-8',
            'format' => 'A4',
            // 'default_font_size' => 12,
            // 'default_font' => 'sarabun',
            // 'margin_left' => 5,
            // 'margin_right' => 5,
            // 'margin_top' => 5,
            // 'margin_bottom' => 5,
            // 'margin_header' => 5,
            // 'margin_footer' => 5,
        ]);

        $mpdf->SetTitle('หนังสือรับรองการหักภาษี');
        // $mpdf->AddPage();
        $html = '';
        ////////////////////////////////////////////////////////////// หน้าเปล่า //////////////////////////////////////////////////////////
        if (count($Employee) == 0) {
            $mpdf->AddPage();
            $html = '
                    <div style="text-align: center; font-size: 22px; padding-bottom: 5px; "><b> หนังสือรับรองการหักภาษี ณ ที่จ่าย ตามมาตรา 50 ทวิ แห่งประมวลรัษฎากร </div>
                    <div style="text-align: left; font-size: 18px;"> ผู้มีหน้าที่หักภาษี ณ ที่จ่าย : </div>
                    <div style="text-align: left; font-size: 18px;"> ชื่อ' . ($Branch->company->name ?? null) . ' </div>
                    <div style="text-align: left; font-size: 18px;">' . ($Branch->company->address ?? null) . ' </div>

                    <div style="text-align: left; font-size: 18px; padding-top: 10px;"> ผู้ถูกหักภาษี ณ ที่จ่าย : </div>
                    <div style="text-align: left; font-size: 18px;"> &nbsp;</div>
                    <div style="text-align: left; font-size: 18px; padding-bottom: 5px;"> ที่อยู่ </div>
                    <div style="position:absolute; top: 100; left: 88px; width: 650px; text-align: right; font-size:16px;"> เลขประจำตัวผู้เสียภาษีอากร </div>
                    <div style="position:absolute; top: 120; left: 88px; width: 650px; text-align: right; font-size:16px;"> 0105549013079 </div>

                    <div style="position:absolute; top: 190; left: 88px; width: 650px; text-align: right; font-size:16px;"> เลขประจำตัวผู้เสียภาษีอากร </div>
                    <div style="position:absolute; top: 210; left: 88px; width: 650px; text-align: right; font-size:16px;"> &nbsp;</div>

                    <style>
                        table {
                            width:100%;
                            border-collapse: collapse;
                        }
                        th, td {
                            border: 1px solid black;
                        }
                    </style>

                    <table>
                        <tr>
                            <th> ประเภทเงินได้ที่จ่าย </th>
                            <th> วัน เดือน ปี ที่จ่าย </th>
                            <th> จำนวนเงินที่จ่าย </th>
                            <th> ภาษีที่หักและนำส่งไว้ </th>
                        </tr>
                        <tr>
                            <td style="border-top: none; border-bottom: none;"> ค่าตอบแทน </td>
                            <td style="border-top: none; border-bottom: none;">1 มกราคม ........... - 31 ธันวาคม ........... </td>
                            <td style="border-top: none; border-bottom: none; text-align:right;"> &nbsp; </td>
                            <td style="border-top: none; border-bottom: none; text-align:right;"> &nbsp;</td>
                        </tr>
                        <tr>
                            <td style="border-top: none;"> &nbsp; </td>
                            <td style="border-top: none;"> </td>
                            <td style="border-top: none; border-bottom: none;"> </td>
                            <td style="border-top: none; border-bottom: none;"> </td>
                        </tr>
                        <tr>
                            <td style="border: none;" colspan="2"> รวมเงินที่จ่ายและภาษีที่หักจ่าย </td>
                            <td style="text-align:right;"> &nbsp;</td>
                            <td style="text-align:right;"> &nbsp;</td>
                        </tr>
                    </table>

                    <div style="position:absolute; left: 60px; width: 650px; text-align: left; font-size:16px; top: 353;"> รวมภาษีที่หักและนำส่ง </div>
                    <div style="position:absolute; left: 60px; width: 650px; text-align: left; font-size:16px; top: 373;"> ผู้จ่ายเงิน </div>
                    <div style="position:absolute; left: 200px; width: 650px; text-align: left; font-size:16px; top: 373;"> ออกให้ครั้งเดียว </div>
                    <div style="position:absolute; left: 400px; width: 650px; text-align: left; font-size:16px; top: 373;"> ออกภาษีให้ตลอดไป </div>
                    <div style="position:absolute; left: 600px; width: 650px; text-align: left; font-size:16px; top: 373;"> /หักภาษี ณ ที่จ่าย </div>

                    <table style="margin-top: 40px; border-collapse: collapse; border: 1px solid black;">
                    <tr>
                        <td style="text-align: center; border: none; padding-top: 5px;"> ขอรับรองว่าข้อความและตัวเลขดังกล่าวข้างต้นถูกต้องตรงกับความจริงทุกประการ </td>
                    </tr>
                    <br/>
                    <br/>
                    <tr>
                        <td style="text-align: center; border: none;"> ลงชื่อ ........................................................................................ ผู้มีหน้าที่หักภาษี ณ ที่จ่าย</td>
                    </tr>
                    <tr>
                        <td style="text-align: center; border: none;"> นายทดสอบ ทดสอบ </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; border: none; padding-bottom: 5px;"> </td>
                    </tr>
                </table>
                <div style="position:absolute; top: 400; left: 60px; width: 650px; text-align: center; font-size:16px;">   </div>

                ';
            $mpdf->WriteHTML($html);
        }

        foreach ($Employee as $employee) {

            $html .= '
            <div style="text-align: center; font-size: 22px; padding-bottom: 5px; "><b> หนังสือรับรองการหักภาษี ณ ที่จ่าย ตามมาตรา 50 ทวิ แห่งประมวลรัษฎากร </div>
            <div style="text-align: left; font-size: 18px;"> ผู้มีหน้าที่หักภาษี ณ ที่จ่าย : </div>
            <div style="text-align: left; font-size: 18px;"> ' . ($Branch->company->name ?? null) . ' </div>
            <div style="text-align: left; font-size: 18px;"> ' . ($Branch->company->address ?? null) . ' </div>

            <div style="text-align: left; font-size: 18px; padding-top: 10px;"> ผู้ถูกหักภาษี ณ ที่จ่าย : </div>
            <div style="text-align: left; font-size: 18px;"> ' . ($employee && $employee['hrEmployee'] &&  $employee['hrEmployee']['prefix'] ? $employee['hrEmployee']['prefix']['name']  : null) . ($employee &&  $employee['hrEmployee'] ? $employee['hrEmployee']['fname']  : null) . ' ' . ($employee &&  $employee['hrEmployee'] ? $employee['hrEmployee']['lname'] : null) . ' &nbsp;</div>
            <div style="text-align: left; font-size: 18px; padding-bottom: 5px;"> ที่อยู่ ' . ($employee && $employee['hrEmployee'] ? $employee['hrEmployee']['current_address'] : null) . '
            &nbsp;' . ($employee && $employee['hrEmployee'] ? $employee['hrEmployee']['current_alley'] : null) . '
            &nbsp;' . ($employee && $employee['hrEmployee'] ? $employee['hrEmployee']['current_road'] : null) . '
            &nbsp;' . ($employee && $employee['hrEmployee'] && $employee['hrEmployee']['currentThaiTambon'] ? $employee['hrEmployee']['currentThaiTambon']['name_th'] : null) . '
            &nbsp;' . ($employee && $employee['hrEmployee'] && $employee['hrEmployee']['currentThaiAmphure'] ? $employee['hrEmployee']['currentThaiAmphure']['name_th']  : null) . '
            &nbsp;' . ($employee && $employee['hrEmployee'] && $employee['hrEmployee']['currentThaiProvince'] ? $employee['hrEmployee']['currentThaiProvince']['name_th']  : null) . '
            &nbsp;' . ($employee && $employee['hrEmployee'] ? $employee['hrEmployee']['current_zip_code'] : null) .
                ' </div>
            <div style="position:absolute; top: 100; left: 88px; width: 650px; text-align: right; font-size:16px;"> เลขประจำตัวผู้เสียภาษีอากร </div>
            <div style="position:absolute; top: 120; left: 88px; width: 650px; text-align: right; font-size:16px;"> 0105549013079 </div>

            <div style="position:absolute; top: 190; left: 88px; width: 650px; text-align: right; font-size:16px;"> เลขประจำตัวผู้เสียภาษีอากร </div>
            <div style="position:absolute; top: 210; left: 88px; width: 650px; text-align: right; font-size:16px;"> ' . ($employee && $employee['hrEmployee'] ? $employee['hrEmployee']['citizen_no'] : null) . ' &nbsp;</div>

            <style>
                table {
                    width:100%;
                    border-collapse: collapse;
                }
                th, td {
                    border: 1px solid black;
                }
            </style>

            <table>
                <tr>
                    <th> ประเภทเงินได้ที่จ่าย </th>
                    <th> วัน เดือน ปี ที่จ่าย </th>
                    <th> จำนวนเงินที่จ่าย </th>
                    <th> ภาษีที่หักและนำส่งไว้ </th>
                </tr>
                <tr>
                    <td style="border-top: none; border-bottom: none;"> ค่าตอบแทน </td>
                    <td style="border-top: none; border-bottom: none;">1 มกราคม ' . ($year) . ' - 31 ธันวาคม ' . ($year) . ' </td>
                    <td style="border-top: none; border-bottom: none; text-align:right;">' . ($employee ? number_format($employee['sum_price1'], 2) : null) . '&nbsp; </td>
                    <td style="border-top: none; border-bottom: none; text-align:right;"> ' . ($employee ?  number_format($employee['sum_price2'], 2) : null) . '&nbsp;</td>
                </tr>
                <tr>
                    <td style="border-top: none;"> &nbsp; </td>
                    <td style="border-top: none;"> </td>
                    <td style="border-top: none; border-bottom: none;"> </td>
                    <td style="border-top: none; border-bottom: none;"> </td>
                </tr>
                <tr>
                    <td style="border: none;" colspan="2"> รวมเงินที่จ่ายและภาษีที่หักจ่าย </td>
                    <td style="text-align:right;">' . ($employee ?   number_format($employee['sum_price1'], 2) : null) . ' &nbsp;</td>
                    <td style="text-align:right;"> ' . ($employee ?   number_format($employee['sum_price2'], 2) : null) . '&nbsp;</td>
                </tr>
            </table>

            <div style="position:absolute; left: 60px; width: 650px; text-align: left; font-size:16px; top: 353;"> รวมภาษีที่หักและนำส่ง </div>
            <div style="position:absolute; left: 60px; width: 650px; text-align: left; font-size:16px; top: 373;"> ผู้จ่ายเงิน </div>
            <div style="position:absolute; left: 200px; width: 650px; text-align: left; font-size:16px; top: 373;"> ออกให้ครั้งเดียว </div>
            <div style="position:absolute; left: 400px; width: 650px; text-align: left; font-size:16px; top: 373;"> ออกภาษีให้ตลอดไป </div>
            <div style="position:absolute; left: 600px; width: 650px; text-align: left; font-size:16px; top: 373;"> /หักภาษี ณ ที่จ่าย </div>

            <table style="margin-top: 40px; border-collapse: collapse; border: 1px solid black;">
            <tr>
                <td style="text-align: center; border: none; padding-top: 5px;"> ขอรับรองว่าข้อความและตัวเลขดังกล่าวข้างต้นถูกต้องตรงกับความจริงทุกประการ </td>
            </tr>
            <br/>
            <br/>
            <tr>
                <td style="text-align: center; border: none;"> ลงชื่อ ........................................................................................ ผู้มีหน้าที่หักภาษี ณ ที่จ่าย</td>
            </tr>
            <tr>
                <td style="text-align: center; border: none;"> นายทดสอบ ทดสอบ </td>
            </tr>
            <tr>
                <td style="text-align: center; border: none; padding-bottom: 5px;">' . ($this->dateThaiFormat(date('Y-m-d'))) . '. </td>
            </tr>
        </table>
        <div style="position:absolute; top: 400; left: 60px; width: 650px; text-align: center; font-size:16px;">  </div>

        ';

            $mpdf->AddPage();
            $mpdf->WriteHTML($html);
        }

        $pdfContent = $mpdf->Output('', 'S');

        $contentLength = strlen($pdfContent);

        $headers = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename=mpdf.pdf',
            'Access-Control-Expose-Headers' => 'Accept-Ranges',
            'Access-Control-Allow-Headers' => 'Accept-Ranges,range',
            'Accept-Ranges' => 'bytes',
            'Content-Length' => $contentLength,
        ];

        return Response::make($pdfContent, 200, $headers);
    }


    public function attached_pnk(Request $request) // รายงาน ภ.ง.ด. 1ก (พตส.ข้าราชการ)
    {
        $employee_type_id = $request->employee_type_id;
        $year = $request->year;

        $HrEmployeePayroll = Payroll::select('user_id', DB::raw('sum(total) as total_summary'), DB::raw('sum(total) as total_tax'))
            ->with('user')
            ->where('year', $year)
            ->groupby('user_id')
            ->get();

        // return $HrEmployeePayroll;

        //PDF
        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $mpdf = new \Mpdf\Mpdf([
            'fontDir' => array_merge($fontDirs, [
                base_path() . '/custom/font/directory',
            ]),
            'fontdata' => $fontData + [ // lowercase letters only in font key
                'th-sarabun-it' => [
                    'R' => 'THSarabunIT๙.ttf',
                    'I' => 'THSarabunIT๙ Italic.ttf',
                    'B' => 'THSarabunIT๙ Bold.ttf',
                    'BI' => 'THSarabunIT๙ BoldItalic.ttf',
                ],
                'th-sarabun' => [
                    'R' => 'THSarabun.ttf',
                    'I' => 'THSarabun Italic.ttf',
                    'B' => 'THSarabun Bold.ttf',
                    'BI' => 'THSarabun Bold Italic.ttf',
                ]
            ],
            'default_font' => 'th-sarabun',
            'mode' => 'utf-8',
            'format' => 'A4',
            // 'default_font_size' => 12,
            // 'default_font' => 'sarabun',
            // 'margin_left' => 5,
            // 'margin_right' => 5,
            // 'margin_top' => 5,
            // 'margin_bottom' => 5,
            // 'margin_header' => 5,
            // 'margin_footer' => 5,
        ]);
        // function getThaiMonthName($monthNumber)
        // {
        //     // กำหนด locale เป็นภาษาไทย
        //     setlocale(LC_TIME, 'th_TH.UTF-8');
        //     // แปลงเดือนเป็นชื่อเดือนภาษาไทย
        //     return strftime("%B", mktime(0, 0, 0, $monthNumber, 1));
        // }

        // return $proj;

        /// checkBox
        // if(($proj['project_form']['action_plan_of_mph'])){

        // };


        //     $page = 1;
        //     // return $eiei;
        //     $mpdf->SetTitle('รายงาน ภ.ง.ด. 1ก (พตส.ข้าราชการ)');
        //     $mpdf->AddPage('L');

        //     $html = '
        //     <style>
        //         table {
        //             width:100%;
        //             border-collapse: collapse;
        //             border: 1px solid black;
        //         }
        //         th, td {
        //             border: 1px solid black;
        //         }
        //     </style>

        //     <div style="position:absolute; top: 20px; left: 33%; text-align: center; font-size: 20px;"> <b>เลขประจำตัวผู้เสียภาษีอากร</b> (ของผู้มีหน้าที่หักภาษี ณ ที่จ่าย) </div>
        //     <div style="position:absolute; top: 30px; left: 75px; text-align: left; font-size: 20px;"> <b>ใบแนบ ภ.ง.ค. 1 ก</b> </div>
        //     <div style="position:absolute; top: 30px; left: 63.5%; text-align: right; font-size: 20px; width: 350px;"> <b>สาขาที่ 1 0 0 0 2 0</b> แผ่นที่ 1 ในจำนวน ' . (ceil(count($HrEmployeePayroll) / 8)) . ' แผ่น</div>

        //     <div style="position:absolute; top: 675px; left: 70px; text-align: right; font-size: 18px;"> (ให้กรอกลําดับที่ต่อเนื่องกันไปทุกแผ่นตามเงินได้แต่ละประเภท)</div>
        //     <div style="position:absolute; top: 680px; left: 72%; text-align: center; font-size: 18px;"> ลงชื่อ........................................................ผู้จ่ายเงิน </div>
        //     <div style="position:absolute; top: 700px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> (นางสาววรารัตน กันอรุณภูวิสิฐ) </div>
        //     <div style="position:absolute; top: 720px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> ตําแหน่ง หัวหน้ากลุ่มงานการเงินและบัญชี </div>

        //     <div style="position:absolute; top: 670.1px; left: 56.6px; text-align: center; width: 1009.2px;"> <table><tr><th style="height: 80px; width:70%;"></th><th></th></tr></table> </div>

        //     <table>
        //         <tr>
        //             <th> ลำดับที่ </th>
        //             <th> เลขประจําตัวประชาชน(ของผู้มีเงินได้)<br>เลขประจําตัวผู้เสียภาษีอากร(ของผู้มีเงินได้) </th>
        //             <th> ชื่อผู้มีเงินได้ (ให้ระบุให้ชัดเจนว่าเป็น นาย นาง นางสาว หรือยศ) <br>ที่อยู่ผู้มีเงินได้ (ให้ระบุเลขที่ ตรอก/ซอย ถนน ตําบล/แขวง อําเภอ/เขต จังหวัด)</th>
        //             <th> รายการวมรับทั้งปี </th>
        //             <th> จำนวนเงินที่จ่ายทั้งปี </th>
        //             <th> จำนวนเงินภาษีที่หัก <br> และนำส่งทั้งปี </th>
        //         </tr>
        //     ';
        //     $sumMoney = 0;
        //     $sumTax = 0;
        //     for ($i = 0; $i < (count($HrEmployeePayroll)); $i++) {
        //         $minNumber = 4000;
        //         $maxNumber = 110000;

        //         //hrEmployee
        //         $HrEmployee[$i] = $HrEmployeePayroll[$i]->hrEmployee;

        //         $summary =  $HrEmployeePayroll[$i]->total_summary;
        //         $sumMoney = $sumMoney + $summary;
        //         // // สุ่มเลขจำนวนเต็ม
        //         // $randomNumber = rand($minNumber, $maxNumber);
        //         // $sumMoney = $sumMoney + $randomNumber;

        //         $tax =  $HrEmployeePayroll[$i]->total_tax;
        //         $sumTax = $sumTax + $tax;


        //         $html .= '
        //             <tr>
        //                 <td style="text-align:center; height: 65px;">' . ($no = $i + 1) . '</td>
        //                 <td style="border-left: none; border-right: none; line-height: 1"> ' . implode(' ', str_split(!empty($HrEmployee[$i]['citizen_no']) ? $HrEmployee[$i]['citizen_no'] : null)) . ' </td>
        //                 <td style="border-left: none; border-right: none; line-height: 1; font-size: 18px;"> ชื่อ <b>' . (!empty($HrEmployee[$i]['prefix']['name']) ? $HrEmployee[$i]['prefix']['name'] : null) . '' . (!empty($HrEmployee[$i]['fname']) ? $HrEmployee[$i]['fname'] : null) . ' ' . (!empty($HrEmployee[$i]['lname']) ? $HrEmployee[$i]['lname'] : null) . '</b> <br> ที่อยู่ ' . (!empty($HrEmployee[$i]['current_address']) ? $HrEmployee[$i]['current_address'] : null) . ' แขวง' . (!empty($HrEmployee[$i]['currentThaiTambon']) ? $HrEmployee[$i]['currentThaiTambon']['name_th'] : null) . ' <br> เขต' . (!empty($HrEmployee[$i]['currentThaiAmphure']) ? $HrEmployee[$i]['currentThaiAmphure']['name_th'] : null) . ' ' . (!empty($HrEmployee[$i]['currentThaiProvince']) ? $HrEmployee[$i]['currentThaiProvince']['name_th'] : null) . ' ' . (!empty($HrEmployee[$i]['current_zip_code']) ? $HrEmployee[$i]['current_zip_code'] : null) . '</td>
        //                 <td style="border-left: none; border-right: none; line-height: 1"> เงินเดือน,ค่าจ้าง,ตกเบิก,เต็มขั้น <br> เงินตำแหน่ง,ค่าตอบแทน <br> ค่าล่วงเวลา <br> รับอื่นๆ,เงินพิเศษอื่นๆ </td>
        //                 <td style="text-align: right; line-height: 1"> ' . number_format($summary, 2) . ' <br> 0 <br> 0 <br> 0</td>
        //                 <td style="text-align: right; line-height: 1"> ' . number_format($tax, 2) . ' <br>  <br>  <br> </td>
        //             </tr>
        //         ';

        //         if (($no = $i + 1) % 8 == 0) {
        //             $html .= '
        //                 <tr>
        //                     <td style="text-align:center; height: 50px;"> </td>
        //                     <td style="text-align:right; vertical-align: bottom; padding-bottom: 0px;" colspan="3"> รวมยอดเงินได้และภาษีที่นําส่ง <b> ใบต่อ ภ.ง.ด. 1 </b> (นําไปรวมกับ ฉบับอื่น (ถ้ามี)) </td>
        //                     <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumMoney, 2) . '</td>
        //                     <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumTax, 2) . ' </td>
        //                 </tr>
        //             ';

        //             if ($i + 1 < (count($HrEmployee))) {
        //                 $html .= '</table>';
        //                 $mpdf->WriteHTML($html);
        //                 $mpdf->AddPage('L');
        //                 $page = $page + 1;
        //                 $html = '
        //                     <div style="position:absolute; top: 20px; left: 33%; text-align: center; font-size: 20px;"> <b>เลขประจำตัวผู้เสียภาษีอากร</b> (ของผู้มีหน้าที่หักภาษี ณ ที่จ่าย) </div>
        //                     <div style="position:absolute; top: 30px; left: 75px; text-align: left; font-size: 20px;"> <b>ใบแนบ ภ.ง.ค. 1 ก</b> </div>
        //                     <div style="position:absolute; top: 30px; left: 63.5%; text-align: right; font-size: 20px; width: 350px;"> <b>สาขาที่ 1 0 0 0 2 0 </b> แผ่นที่ ' . $page . ' ในจำนวน ' . (ceil(count($HrEmployeePayroll) / 8)) . ' แผ่น</div>

        //                     <div style="position:absolute; top: 675px; left: 70px; text-align: right; font-size: 18px;"> (ให้กรอกลําดับที่ต่อเนื่องกันไปทุกแผ่นตามเงินได้แต่ละประเภท)</div>
        //                     <div style="position:absolute; top: 680px; left: 72%; text-align: center; font-size: 18px;"> ลงชื่อ........................................................ผู้จ่ายเงิน </div>
        //                     <div style="position:absolute; top: 700px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> (นายธรรมปพน พุทธคำ) </div>
        //                     <div style="position:absolute; top: 720px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> ตําแหน่ง หัวหน้ากลุ่มงานการเงนิและบัญชี </div>

        //                     <div style="position:absolute; top: 670.1px; left: 56.6px; text-align: center; width: 1009.2px;"> <table><tr><th style="height: 80px; width:70%;"></th><th></th></tr></table> </div>
        //                         <table>
        //                             <tr>
        //                                 <th> ลำดับที่ </th>
        //                                 <th> เลขประจําตัวประชาชน(ของผู้มีเงินได้)<br>เลขประจําตัวผู้เสียภาษีอากร(ของผู้มีเงินได้) </th>
        //                                 <th> ชื่อผู้มีเงินได้ (ให้ระบุให้ชัดเจนว่าเป็น นาย นาง นางสาว หรือยศ) <br>ที่อยู่ผู้มีเงินได้ (ให้ระบุเลขที่ ตรอก/ซอย ถนน ตําบล/แขวง อําเภอ/เขต จังหวัด)</th>
        //                                 <th> รายการวมรับทั้งปี </th>
        //                                 <th> จำนวนเงินที่จ่ายทั้งปี </th>
        //                                 <th> จำนวนเงินภาษีที่หัก <br> และนำส่งทั้งปี </th>
        //                             </tr>';
        //             }
        //         }
        //     }
        //     $page_less = ($page * 8) - (count($HrEmployeePayroll));
        //     if (ceil($page_less)) {
        //         for ($i = 0; $i < $page_less; $i++) {
        //             $html .= '
        //         <tr>
        //             <td style="border-bottom: none; border-top: none; line-height: 1; height: 65px;"> </td>
        //             <td style="border-left: none; border-right: none; border-bottom: none; border-top: none; line-height: 1; height: 65px;" colspan="3"> </td>
        //             <td style="border-bottom: none; border-top: none; line-height: 1; height: 65px;"> </td>
        //             <td style="border-bottom: none; border-top: none; line-height: 1; height: 65px;"> </td>
        //         </tr>
        //     ';
        //         }
        //         $html .= '
        //         <tr>
        //             <td style="text-align:center; height: 50px;"> </td>
        //             <td style="text-align:right; vertical-align: bottom; padding-bottom: 0px;" colspan="3"> รวมยอดเงินได้และภาษีที่นําส่ง <b> ใบต่อ ภ.ง.ด. 1 </b> (นําไปรวมกับ ฉบับอื่น (ถ้ามี)) </td>
        //             <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumMoney, 2) . '</td>
        //             <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumTax, 2) . ' </td>
        //         </tr>

        //     ';
        //     }
        //     $html .= '
        // </table>

        // ';

        $page = 0;
        $page_total = ceil((count($HrEmployeePayroll ?? []) / 8));
        $no = 0;
        $total = 0;
        $row_fix = 0;
        $final_row = count($HrEmployeePayroll ?? []);
        $content = '

            <style>
                table {
                    width:100%;
                    border-collapse: collapse;
                }
                th, td {
                    border: 1px solid black;
                }
            </style>

            <table>
                <tr>
                    <th> ลำดับที่ </th>
                    <th> เลขประจําตัวประชาชน(ของผู้มีเงินได้)<br>เลขประจําตัวผู้เสียภาษีอากร(ของผู้มีเงินได้) </th>
                    <th> ชื่อผู้มีเงินได้ (ให้ระบุให้ชัดเจนว่าเป็น นาย นาง นางสาว หรือยศ) <br>ที่อยู่ผู้มีเงินได้ (ให้ระบุเลขที่ ตรอก/ซอย ถนน ตําบล/แขวง อําเภอ/เขต จังหวัด)</th>
                    <th> รายการวมรับทั้งปี </th>
                    <th> จำนวนเงินที่จ่ายทั้งปี </th>
                    <th> จำนวนเงินภาษีที่หัก <br> และนำส่งทั้งปี </th>
                </tr>
        ';

        $sumMoney = 0;
        $sumTax = 0;

        for ($i = 0; $i < (count($HrEmployeePayroll ?? [])); $i++) {
            $row_fix++;

            $minNumber = 4000;
            $maxNumber = 110000;

            //hrEmployee
            $HrEmployee[$i] = $HrEmployeePayroll[$i]->user;

            $summary =  $HrEmployeePayroll[$i]->total_summary;
            $sumMoney = $sumMoney + $summary;
            // // สุ่มเลขจำนวนเต็ม
            // $randomNumber = rand($minNumber, $maxNumber);
            // $sumMoney = $sumMoney + $randomNumber;

            $tax =  $HrEmployeePayroll[$i]->total_tax;
            $sumTax = $sumTax + $tax;


            $content .= '

                <tr>
                    <td style="text-align:center; height: 65px;">' . ($no = $i + 1) . '</td>
                    <td style="border-left: none; border-right: none; line-height: 1"> ' . implode(' ', str_split(!empty($HrEmployee[$i]['citizen_no']) ? $HrEmployee[$i]['citizen_no'] : null)) . ' </td>
                    <td style="border-left: none; border-right: none; line-height: 1; font-size: 18px;"> ชื่อ <b>' . (!empty($HrEmployee[$i]['prefix']['name']) ? $HrEmployee[$i]['prefix']['name'] : null) . '' . (!empty($HrEmployee[$i]['first_name']) ? $HrEmployee[$i]['first_name'] : null) . ' ' . (!empty($HrEmployee[$i]['last_name']) ? $HrEmployee[$i]['last_name'] : null) . '</b> <br> ที่อยู่ ' . (!empty($HrEmployee[$i]['current_address']) ? $HrEmployee[$i]['current_address'] : null) . ' แขวง' . (!empty($HrEmployee[$i]['currentThaiTambon']) ? $HrEmployee[$i]['currentThaiTambon']['name_th'] : null) . ' <br> เขต' . (!empty($HrEmployee[$i]['currentThaiAmphure']) ? $HrEmployee[$i]['currentThaiAmphure']['name_th'] : null) . ' ' . (!empty($HrEmployee[$i]['currentThaiProvince']) ? $HrEmployee[$i]['currentThaiProvince']['name_th'] : null) . ' ' . (!empty($HrEmployee[$i]['current_zip_code']) ? $HrEmployee[$i]['current_zip_code'] : null) . '</td>
                    <td style="border-left: none; border-right: none; line-height: 1"> เงินเดือน,ค่าจ้าง,ตกเบิก,เต็มขั้น <br> เงินตำแหน่ง,ค่าตอบแทน <br> ค่าล่วงเวลา <br> รับอื่นๆ,เงินพิเศษอื่นๆ </td>
                    <td style="text-align: right; line-height: 1"> ' . number_format($summary, 2) . ' <br> 0 <br> 0 <br> 0</td>
                    <td style="text-align: right; line-height: 1"> ' . number_format($tax, 2) . ' <br>  <br>  <br> </td>
                </tr>

            ';

            $total = $total + (!empty($HrEmployeePayroll[$i]['summary']) ? $HrEmployeePayroll[$i]['summary'] : null);

            if ($row_fix % 8 == 0 && ($i !== (count($HrEmployeePayroll ?? []) - 1))) {
                $page = $page + 1;
                $content .= '
                    <tr>
                        <td style="text-align:center; height: 50px;"> </td>
                        <td style="text-align:right; vertical-align: bottom; padding-bottom: 0px;" colspan="3"> รวมยอดเงินได้และภาษีที่นําส่ง <b> ใบต่อ ภ.ง.ด. 1 </b> (นําไปรวมกับ ฉบับอื่น (ถ้ามี)) </td>
                        <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumMoney, 2) . '</td>
                        <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumTax, 2) . ' </td>
                    </tr>
                </table>

                <div style="position:absolute; top: 20px; left: 33%; text-align: center; font-size: 20px;"> <b>เลขประจำตัวผู้เสียภาษีอากร</b> (ของผู้มีหน้าที่หักภาษี ณ ที่จ่าย) </div>
                <div style="position:absolute; top: 30px; left: 75px; text-align: left; font-size: 20px;"> <b>ใบแนบ ภ.ง.ค. 1 ก</b> </div>
                <div style="position:absolute; top: 30px; left: 63.5%; text-align: right; font-size: 20px; width: 350px;"> <b>สาขาที่ 1 0 0 0 2 0</b> แผ่นที่ ' . $page . ' ในจำนวน ' . (ceil(count($HrEmployeePayroll) / 8)) . ' แผ่น</div>

                <div style="position:absolute; top: 675px; left: 70px; text-align: right; font-size: 18px;"> (ให้กรอกลําดับที่ต่อเนื่องกันไปทุกแผ่นตามเงินได้แต่ละประเภท)</div>
                <div style="position:absolute; top: 710px; left: 72%; text-align: center; font-size: 18px;"> ลงชื่อ........................................................ผู้จ่ายเงิน </div>
                <div style="position:absolute; top: 730px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> (นายทดสอบ ทดสอบ) </div>
                <div style="position:absolute; top: 750px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> ตําแหน่ง หัวหน้าการเงินและบัญชี </div>

                <table>
                    <tr>
                        <th style="border: none; height: 60px;">  </th>
                        <th style="border: none;">  </th>
                        <th style="border: none;">  </th>
                        <th style="border: none;">  </th>
                        <th style="border: none;">  </th>
                        <th style="border: none;"> &nbsp; </th>
                    </tr>
                    <tr>
                        <th> ลำดับที่ </th>
                        <th> เลขประจําตัวประชาชน(ของผู้มีเงินได้)<br>เลขประจําตัวผู้เสียภาษีอากร(ของผู้มีเงินได้) </th>
                        <th> ชื่อผู้มีเงินได้ (ให้ระบุให้ชัดเจนว่าเป็น นาย นาง นางสาว หรือยศ) <br>ที่อยู่ผู้มีเงินได้ (ให้ระบุเลขที่ ตรอก/ซอย ถนน ตําบล/แขวง อําเภอ/เขต จังหวัด)</th>
                        <th> รายการวมรับทั้งปี </th>
                        <th> จำนวนเงินที่จ่ายทั้งปี </th>
                        <th> จำนวนเงินภาษีที่หัก <br> และนำส่งทั้งปี </th>
                    </tr>
                ';
            } elseif ($i == ($final_row - 1)) {
                $page = $page + 1;
                $content .= '
                    <tr>
                        <td style="text-align:center; height: 50px;"> </td>
                        <td style="text-align:right; vertical-align: bottom; padding-bottom: 0px;" colspan="3"> รวมยอดเงินได้และภาษีที่นําส่ง <b> ใบต่อ ภ.ง.ด. 1 </b> (นําไปรวมกับ ฉบับอื่น (ถ้ามี)) </td>
                        <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumMoney, 2) . '</td>
                        <td style="vertical-align: bottom; padding-bottom: 0px; text-align:right;"> ' . number_format($sumTax, 2) . ' </td>
                    </tr>

                </table>

                    <div style="position:absolute; top: 20px; left: 33%; text-align: center; font-size: 20px;"> <b>เลขประจำตัวผู้เสียภาษีอากร</b> (ของผู้มีหน้าที่หักภาษี ณ ที่จ่าย) </div>
                    <div style="position:absolute; top: 30px; left: 75px; text-align: left; font-size: 20px;"> <b>ใบแนบ ภ.ง.ค. 1 ก</b> </div>
                    <div style="position:absolute; top: 30px; left: 63.5%; text-align: right; font-size: 20px; width: 350px;"> <b>สาขาที่ 1 0 0 0 2 0</b> แผ่นที่ ' . $page . ' ในจำนวน ' . (ceil(count($HrEmployeePayroll) / 8)) . ' แผ่น</div>

                    <div style="position:absolute; top: 675px; left: 70px; text-align: right; font-size: 18px;"> (ให้กรอกลําดับที่ต่อเนื่องกันไปทุกแผ่นตามเงินได้แต่ละประเภท)</div>
                    <div style="position:absolute; top: 710px; left: 72%; text-align: center; font-size: 18px;"> ลงชื่อ........................................................ผู้จ่ายเงิน </div>
                    <div style="position:absolute; top: 730px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> (นายทดสอบ ทดสอบ) </div>
                    <div style="position:absolute; top: 750px; left: 764x; text-align: center; width: 300px; font-size: 18px;"> ตําแหน่ง หัวหน้าการเงินและบัญชี </div>

                <table>
                ';
            }
        }

        $content .= '

        </table>
        ';


        $mpdf->SetTitle('รายงานรายการรับ');
        $mpdf->AddPage('L');
        $mpdf->WriteHTML($content);
        $pdfContent = $mpdf->Output('', 'S');

        $contentLength = strlen($pdfContent);

        $headers = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename=mpdf.pdf',
            'Access-Control-Expose-Headers' => 'Accept-Ranges',
            'Access-Control-Allow-Headers' => 'Accept-Ranges,range',
            'Accept-Ranges' => 'bytes',
            'Content-Length' => $contentLength,
        ];

        return Response::make($pdfContent, 200, $headers);
    }
}
