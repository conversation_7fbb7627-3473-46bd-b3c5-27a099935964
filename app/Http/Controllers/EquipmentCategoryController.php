<?php

namespace App\Http\Controllers;

use App\Models\EquipmentCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class EquipmentCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = EquipmentCategory::withCount('equipments');

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status == 'true');
            }

            // Search by name
            if ($request->has('search')) {
                $search = $request->search;
                $query->where('name', 'like', "%{$search}%");
            }

            // Get active categories only if requested
            if ($request->has('active') && $request->active == 'true') {
                $query->active();
            }

            $perPage = $request->get('per_page', 15);
            $categories = $query->orderBy('name')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $categories,
                'message' => 'Equipment categories retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve equipment categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:equipment_categories,name',
                'description' => 'nullable|string',
                'status' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['create_by'] = auth()->user()->id ?? null;
            $data['status'] = $data['status'] ?? true;

            $category = EquipmentCategory::create($data);

            return response()->json([
                'success' => true,
                'data' => $category,
                'message' => 'Equipment category created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create equipment category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): JsonResponse
    {
        try {
            $category = EquipmentCategory::withCount('equipments')
                                       ->with(['equipments' => function($query) {
                                           $query->select('id', 'name', 'code', 'status', 'category_id');
                                       }])
                                       ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $category,
                'message' => 'Equipment category retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Equipment category not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $category = EquipmentCategory::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:equipment_categories,name,' . $id,
                'description' => 'nullable|string',
                'status' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['update_by'] = auth()->user()->id ?? null;

            $category->update($data);

            return response()->json([
                'success' => true,
                'data' => $category,
                'message' => 'Equipment category updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update equipment category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
            $category = EquipmentCategory::withCount('equipments')->findOrFail($id);

            // Check if category has equipments
            if ($category->equipments_count > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete category that has equipments assigned to it'
                ], 400);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => 'Equipment category deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete equipment category',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
