<?php

namespace App\Http\Controllers;

use App\Models\LineRichMenu;
use App\Models\Payroll;
use App\Models\User;
use App\Services\LineRichMenuService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use LINE\LINEBot\Event\MessageEvent\TextMessage;
use LINE\LINEBot\MessageBuilder\Flex\ComponentBuilder\BoxComponentBuilder;
use LINE\LINEBot\MessageBuilder\Flex\ComponentBuilder\TextComponentBuilder;
use LINE\LINEBot\MessageBuilder\Flex\ContainerBuilder\BubbleContainerBuilder;
use LINE\LINEBot\MessageBuilder\FlexMessageBuilder;
use Revolution\Line\Facades\Bot;

class LineWebhookController extends Controller
{
    protected $richMenuService;

    public function __construct(LineRichMenuService $richMenuService)
    {
        $this->richMenuService = $richMenuService;
    }

    static public function handleTextMessage(TextMessage $event)
    {
        $text = $event->getText();
        $userId = $event->getUserId();
        $replyToken = $event->getReplyToken();

        $user = User::where('line_id', $userId)->first();
        if (!$user) {
            $user = User::where('line_key', $text)->first();
            if ($user) {
                $user->line_id = $userId;
                $user->save();

                self::registerLineRichMenu($user);

                $fullName = $user->first_name . ' ' . $user->last_name;

                return self::helloUser($fullName);
            } else {
                //
            }
        }

        $langCode = $user->line_lang ?? 'TH';

        //rich menu msg
        if ($text == 'ทดสอบจ้า') {
            return self::MenuRequest($userId, $langCode);
        } else if ($text == 'ขออนุมัติ') {
            return self::MenuRequest($userId, $langCode);
        } else if ($text == 'ประวัติการขออนุมัติ') {
            return self::MenuHistoryRequest($userId, $langCode);
        } else if ($text == 'ข้อมูลการลงเวลา') {
            return self::MenuHistoryCheckIn($userId, $langCode);
        } else if ($text == 'อื่นๆ') {
            return self::MenuOther($userId, $langCode);
        } else if ($text == 'สลิปเงินเดือน') {
            return self::MenuSlip($userId, $langCode);
        } else if ($text == 'ลงเวลา') {
            return self::MenuCheckIn($userId, $langCode);
        }

        //msg
        else if ($text == 'วันนี้') {
            $fullName = $user->first_name . ' ' . $user->last_name;
            return self::helloUser($fullName);
        } else if ($text == 'เวลาเข้าออก') {
            return self::TimeInOutUser($userId);
        } else if ($text == 'เวลาเข้าออกวันนี้') {
            return self::TimeInOutToDayUser($userId);
        } else if ($text == 'เมนูอนุมัติ') {
            return self::MenuApprove($userId, $langCode);
        } else if ($text == 'เปลื่ยนภาษา') {
            return self::MenuChangeLang($userId, $langCode);
        } else if ($text == 'TH') {
            return self::ChangeLangRichMenu($userId, 'TH');
        } else if ($text == 'EN') {
            return self::ChangeLangRichMenu($userId, 'EN');
        } else if ($text == 'KM') {
            return self::ChangeLangRichMenu($userId, 'KM');
        } else if ($text == 'LO') {
            return self::ChangeLangRichMenu($userId, 'LO');
        } else if ($text == 'MY') {
            return self::ChangeLangRichMenu($userId, 'MY');
        }
    }

    public static function registerLineRichMenu(User $user)
    {
        try {
            $richMenu = LineRichMenu::first();

            if (!$richMenu) {
                Log::warning('No rich menu found to link to user: ' . $user->id);
                return false;
            }

            $richMenuService = app(LineRichMenuService::class);
            return $richMenuService->linkRichMenuToUser($user->line_id, $richMenu->rich_menu_id);
        } catch (Exception $e) {
            Log::error('Error linking rich menu to user: ' . $e->getMessage());
            return false;
        }
    }

    static public function helloUser($name)
    {
        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => [
                    [
                        'type' => 'text',
                        'text' => $name
                    ]
                ],
            ]
        ];

        // 2. ส่งด้วย CURL
        $data = [
            'type' => 'flex',
            'altText' => 'hello',
            'contents' => $flexContent
        ];

        return  $data;
    }

    public function TestLine()
    {
        $userId = 'U0aa3e4b8cf4b4b3b8aed3fc61565b8ef';
        // return self::MenuRequest($userId);
        // return self::MenuHistoryRequest($userId);
        // return self::MenuHistoryCheckIn($userId);

        // return self::leaveUser($userId);
        // return self::TimeInOutUser($userId);
        // return self::TimeInOutToDayUser($userId);
    }

    public static function getLangText($langCode)
    {
        $path = resource_path("project/" . config('project.customer') . "/lang/flex_lang/" . $langCode . ".json");

        if (!file_exists($path)) {
            $path = resource_path("project/" . config('project.customer') . "/lang/flex_lang/TH.json"); // fallback
        }

        return json_decode(file_get_contents($path), true);
    }

    static public function MenuRequest($userId, $langCode)
    {
        $lang = self::getLangText($langCode);

        $controller = new UserLeavePermissionController();
        $leavePermissions = $controller->getUserLeavePermissions($userId); // JsonResponse

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" => $lang['menu_request_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" =>  $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];

        $arr_data_header[2] = ['type' => 'separator', 'margin' => 'xxl'];

        for ($i = 0; $i < count($leavePermissions); $i++) {

            $leave_type_name = (!empty($leavePermissions[$i]['leave_type']) ? $leavePermissions[$i]['leave_type']['name'] : '-');
            $leave_qty = (!empty($leavePermissions[$i]['qty']) ? $leavePermissions[$i]['qty'] . ' วัน' : '-');
            $arr_data[$i] = [
                'type' => 'box',
                'layout' => 'horizontal',
                'contents' => [
                    ['type' => 'text', 'text' => $leave_type_name, 'size' => 'sm', 'color' => '#555555', 'flex' => 0],
                    ['type' => 'text', 'text' =>  $leave_qty, 'size' => 'sm', 'color' => '#111111', 'align' => 'end']
                ]
            ];
        }

        $arr_data_header[2] = [
            'type' => 'box',
            'layout' => 'vertical',
            "margin" => "xxl",
            "spacing" => "sm",
            'contents' =>  $arr_data
        ];

        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" => [
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            "label" => $lang['menu_request_label_1'],
                            "uri" => config('line.liff.line_liff_get_leave')
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            "label" => $lang['menu_request_label_2'],
                            "uri" => config('line.liff.line_liff_get_ot')
                        ]
                    ],
                    [
                        "type" => "box",
                        "layout" => "vertical",
                        "contents" => [],
                        "margin" => "sm"
                    ]
                ],
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL

        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_request_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }

    static public function MenuHistoryRequest($userId, $langCode)
    {

        $lang = self::getLangText($langCode);

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" => $lang['menu_history_request_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" =>  $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];


        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" => [
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            'label' => $lang['menu_history_request_label_1'],
                            "uri" => config('line.liff.line_liff_level_list')
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            'label' => $lang['menu_history_request_label_2'],
                            "uri" => config('line.liff.line_liff_ot_list')
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            'label' => $lang['menu_history_request_label_3'],
                            "uri" => config('line.liff.line_liff_warning_view')
                        ]
                    ],
                    [
                        "type" => "box",
                        "layout" => "vertical",
                        "contents" => [],
                        "margin" => "sm"
                    ]
                ],
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL

        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_history_request_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }

    static public function MenuHistoryCheckIn($userId, $langCode)
    {
        $lang = self::getLangText($langCode);

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" =>  $lang['menu_history_check_in_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" => $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];


        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" => [
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => $lang['menu_history_check_in_label_1'],
                            'text' => 'เวลาเข้าออกวันนี้'
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => $lang['menu_history_check_in_label_2'],
                            'text' => 'เวลาเข้าออก'
                        ]
                    ],
                    [
                        "type" => "box",
                        "layout" => "vertical",
                        "contents" => [],
                        "margin" => "sm"
                    ]
                ],
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL

        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_history_check_in_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }


    static public function MenuOther($userId, $langCode)
    {

        $lang = self::getLangText($langCode);

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" => $lang['menu_other_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" =>  $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];


        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" => [
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "message",
                            "label" => $lang['menu_history_request_title'],
                            "text" => "ประวัติการขออนุมัติ"

                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "message",
                            "label" => $lang['menu_slip_title'],//สลิปเงินเดือน
                            "text" => "สลิปเงินเดือน"
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            'label' => $lang['menu_other_label_1'],
                            "uri" => config('line.liff.line_liff_user_profile')
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'uri',
                            'label' => $lang['menu_other_label_2'],
                            "uri" => config('line.liff.line_liff_warning_list')
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => $lang['menu_other_label_3'],
                            'text' => 'เมนูอนุมัติ'
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => $lang['menu_other_label_4'],
                            'text' => 'เปลื่ยนภาษา'
                        ]
                    ],
                    [
                        "type" => "box",
                        "layout" => "vertical",
                        "contents" => [],
                        "margin" => "sm"
                    ]
                ],
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL


        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_other_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }

    static public function MenuSlip($userId, $langCode)
    {

        $lang = self::getLangText($langCode);

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" => $lang['menu_slip_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" =>  $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];

        $Payroll = Payroll::select('year', 'month', 'round')
            ->where('user_id', $User->id)
            ->groupby('year', 'month', 'round')
            ->orderby('year')
            ->orderby('month')
            ->orderby('round')
            ->get();

        Log::info(json_encode($Payroll));

        $arr_data = [];
        for ($i = 0; $i < count($Payroll); $i++) {

            // $payroll_id = $Payroll[$i]->id;
            // $round = $Payroll[$i]->round;
            $year = $Payroll[$i]->year;
            $month = $Payroll[$i]->month;
            $round = $Payroll[$i]->round;

            $arr_data[] = [
                "type" => "button",
                "style" => "link",
                "height" => "sm",
                "action" => [
                    "type" => "uri",
                    // "label" => $month . '/' . $year . '-' . $round,
                    // "uri" => "https://gs-payroll-api.dev-asha.com:8443/api/report_salary_user?payroll_id=" . $payroll_id
                    "label" => $month . '/' . $year . ' (' . $round . ')',
                    "uri" => url("/api/report_salary_user?user_id=" . $User->id . "&year=" . $year . "&month=" . $month . "&round=" . $round . "&type=all")
                ]
            ];
            // $arr_data[] = [
            //     "type" => "button",
            //     "style" => "link",
            //     "height" => "sm",
            //     "action" => [
            //         "type" => "uri",
            //         // "label" => $month . '/' . $year . '-' . $round,
            //         // "uri" => "https://gs-payroll-api.dev-asha.com:8443/api/report_salary_user?payroll_id=" . $payroll_id
            //         "label" => $month . '/' . $year . ' (2)',
            //         "uri" => url("/api/report_salary_user?user_id=" . $User->id . "&year=" . $year . "&month=" . $month . "&round=2" . "&type=all")
            //     ]
            // ];
            $arr_data[] = ['type' => 'separator', 'margin' => 'sm'];
        }


        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" =>  $arr_data,
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL
        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_slip_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }

    static public function MenuCheckIn($userId, $langCode)
    {

        $lang = self::getLangText($langCode);

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" => $lang['menu_check_in_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" =>  $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];


        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" => [
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            "label" =>  $lang['menu_check_in_label_1'],
                            "uri" => config('line.liff.line_liff_check_in')
                        ]
                    ],
                    // ['type' => 'separator', 'margin' => 'sm'],
                    // [
                    //     "type" => "button",
                    //     "style" => "link",
                    //     "height" => "sm",
                    //     "action" => [
                    //         "type" => "uri",
                    //         "label" => $lang['menu_check_in_label_2'],
                    //         "uri" => config('line.liff.line_liff_check_in')
                    //     ]
                    // ],
                    [
                        "type" => "box",
                        "layout" => "vertical",
                        "contents" => [],
                        "margin" => "sm"
                    ]
                ],
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL

        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_check_in_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }

    static public function MenuApprove($userId, $langCode)
    {

        $lang = self::getLangText($langCode);

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" => $lang['menu_change_lang_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" => $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];


        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" => [
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            "label" => $lang['menu_approve_leave'],
                            "uri" => config('line.liff.line_liff_leave_approve')
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            "label" =>  $lang['menu_approve_ot'],
                            "uri" => config('line.liff.line_liff_ot_approve')
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            "type" => "uri",
                            "label" =>  $lang['menu_approve_warnings'],
                            "uri" => config('line.liff.line_liff_warning_approve')
                        ]
                    ],
                ],
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL


        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_change_lang_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }

    static public function MenuChangeLang($userId, $langCode)
    {

        $lang = self::getLangText($langCode);

        $User = User::where('line_id', $userId)->first();

        $arr_data_header = [];
        $arr_data = [];

        $arr_data_header[0] =   [
            "type" => "text",
            "text" => $lang['menu_change_lang_title'],
            "weight" => "bold",
            "color" => "#1DB446",
            "size" => "md"
        ];

        $arr_data_header[1] =   [
            "type" => "text",
            "text" =>  $User->first_name . ' ' . $User->last_name,
            "weight" => "bold",
            "size" => "lg",
            "margin" => "md"
        ];


        // 1. เตรียม Flex Message JSON ตรง ๆ
        $flexContent = [
            'type' => 'bubble',
            'body' => [
                'type' => 'box',
                'layout' => 'vertical',
                'contents' => $arr_data_header,
            ],
            'styles' => ['footer' => ['separator' => true]],
            'footer' => [
                "type" => "box",
                "layout" => "vertical",
                "spacing" => "sm",
                "contents" => [
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => 'Thai',
                            'text' => 'TH'
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => 'English',
                            'text' => 'EN'
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => 'Khmer',
                            'text' => 'KM'
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => 'Lao',
                            'text' => 'LO'
                        ]
                    ],
                    ['type' => 'separator', 'margin' => 'sm'],
                    [
                        "type" => "button",
                        "style" => "link",
                        "height" => "sm",
                        "action" => [
                            'type' => 'message',
                            'label' => 'Myanmar',
                            'text' => 'MY'
                        ]
                    ],
                    [
                        "type" => "spacer",
                        "size" => "sm"
                    ]
                ],
                "flex" => 0
            ]

        ];

        // 2. ส่งด้วย CURL


        $data = [
            'type' => 'flex',
            'altText' => $lang['menu_change_lang_title'],
            'contents' => $flexContent
        ];

        return  $data;
        // self::SendLineFlex($userId, $data);
    }


    // static public function leaveUser($userId)
    // {
    //     $controller = new UserLeavePermissionController();
    //     $leavePermissions = $controller->getUserLeavePermissions($userId); // JsonResponse

    //     $User = User::where('line_id', $userId)->first();

    //     $arr_data_header = [];
    //     $arr_data = [];

    //     $arr_data_header[0] =   [
    //         "type" => "text",
    //         "text" => "สิทธ์การลาของคุณ",
    //         "weight" => "bold",
    //         "color" => "#1DB446",
    //         "size" => "md"
    //     ];

    //     $arr_data_header[1] =   [
    //         "type" => "text",
    //         "text" => "พนักงาน: " . $User->first_name . ' ' . $User->last_name,
    //         "weight" => "bold",
    //         "size" => "lg",
    //         "margin" => "md"
    //     ];

    //     $arr_data_header[2] = ['type' => 'separator', 'margin' => 'xxl'];

    //     for ($i = 0; $i < count($leavePermissions); $i++) {

    //         $leave_type_name = (!empty($leavePermissions[$i]['leave_type']) ? $leavePermissions[$i]['leave_type']['name'] : null);
    //         $leave_qty = (!empty($leavePermissions[$i]['qty']) ? $leavePermissions[$i]['qty'] . ' วัน' : null);
    //         $arr_data[$i] = [
    //             'type' => 'box',
    //             'layout' => 'horizontal',
    //             'contents' => [
    //                 ['type' => 'text', 'text' => $leave_type_name, 'size' => 'sm', 'color' => '#555555', 'flex' => 0],
    //                 ['type' => 'text', 'text' =>  $leave_qty, 'size' => 'sm', 'color' => '#111111', 'align' => 'end']
    //             ]
    //         ];
    //     }

    //     $arr_data_header[2] = [
    //         'type' => 'box',
    //         'layout' => 'vertical',
    //         "margin" => "xxl",
    //         "spacing" => "sm",
    //         'contents' =>  $arr_data
    //     ];

    //     // 1. เตรียม Flex Message JSON ตรง ๆ
    //     $flexContent = [
    //         'type' => 'bubble',
    //         'body' => [
    //             'type' => 'box',
    //             'layout' => 'vertical',
    //             'contents' => $arr_data_header,
    //         ],
    //         'styles' => ['footer' => ['separator' => true]],
    //         'footer' => [
    //             "type" => "box",
    //             "layout" => "vertical",
    //             "spacing" => "sm",
    //             "contents" => [
    //                 [
    //                     "type" => "button",
    //                     "style" => "link",
    //                     "height" => "sm",
    //                     "action" => [
    //                         "type" => "uri",
    //                         "label" => "ยืนลา",
    //                         "uri" => config('line.liff.line_liff_get_leave')
    //                     ]
    //                 ],
    //                 // [
    //                 //     "type" => "button",
    //                 //     "style" => "link",
    //                 //     "height" => "sm",
    //                 //     "action" => [
    //                 //         "type" => "uri",
    //                 //         "label" => "WEBSITE",
    //                 //         "uri" => "https://line.me/"
    //                 //     ]
    //                 // ],
    //                 [
    //                     "type" => "box",
    //                     "layout" => "vertical",
    //                     "contents" => [],
    //                     "margin" => "sm"
    //                 ]
    //             ],
    //             "flex" => 0
    //         ]

    //     ];

    //     // 2. ส่งด้วย CURL
    //     $data = [
    //         'to' => $userId,
    //         'messages' => [[
    //             'type' => 'flex',
    //             'altText' => 'สิทธ์การลาของคุณ',
    //             'contents' => $flexContent
    //         ]]
    //     ];

    //     self::SendLineFlex($userId, $data);
    // }

    static public function TimeInOutUser($userId)
    {
        $User = User::where('line_id', $userId)->first();

        $date_start = date('Y-m-01'); // วันที่เริ่มต้นของเดือนปัจจุบัน
        $date_end = date('Y-m-t'); // วันที่สิ้นสุดของเดือนปัจจุบัน
        $personnel_id = $User->personnel_id;
        // $personnel_id = 'GB0004';

        $ReportController = new ReportController();
        $TimeAttendance = $ReportController->TimeAttendance($date_start, $date_end, $personnel_id, null);

        $arr_data = [];
        $sum_date = 0;

        $arr_data[0] =   [
            "type" => "box",
            "layout" => "horizontal",
            "contents" => [
                ["type" => "text", "text" => "วันที่", "weight" => "bold", "flex" => 2],
                ["type" => "text", "text" => "เข้า", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "พัก", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "กลับ", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "ออก", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "#", "weight" => "bold", "flex" => 2, "align" => "center"]
            ]
        ];

        $dateInPeriod = self::dateInPeriodFlex($date_start, $date_end);

        for ($a = 0; $a < count($dateInPeriod); $a++) {

            $dateA = date('d', strtotime($dateInPeriod[$a]));

            $date = $dateA;
            $time_in = '-';
            $time_out = '-';
            $time_brake_in = '-';
            $time_brake_out = '-';
            $attendance = '-';
            $color = "#B0B0B0"; // สีเทาพาสเทล (เทาอ่อน)

            for ($i = 0; $i < count($TimeAttendance); $i++) {
                if ($dateA == date('d', strtotime($TimeAttendance[$i]['date']))) {

                    $time_in = !empty($TimeAttendance[$i]['time_in']) ? date('H:i', strtotime($TimeAttendance[$i]['time_in'])) : '-';
                    $time_out = !empty($TimeAttendance[$i]['time_out']) ? date('H:i', strtotime($TimeAttendance[$i]['time_out'])) : '-';
                    $time_brake_in = !empty($TimeAttendance[$i]['time_brake_in']) ? date('H:i', strtotime($TimeAttendance[$i]['time_brake_in'])) : '-';
                    $time_brake_out = !empty($TimeAttendance[$i]['time_brake_out']) ? date('H:i', strtotime($TimeAttendance[$i]['time_brake_out'])) : '-';


                    $attendance = !empty($TimeAttendance[$i]['attendance']) ? $TimeAttendance[$i]['attendance'] : '-';
                    if ($attendance == 'normal') {
                        $attendance = 'ปกติ';
                        $color = "#1DB446"; // เขียวสด (ไม่เปลี่ยน)
                    } else if ($attendance == 'late') {
                        $attendance = 'สาย';
                        $color = "#FFA500"; // สีส้มพาสเทล (ส้มอมเหลือง)
                    } else if ($attendance == 'off') {
                        $attendance = 'หยุด';
                        $color = "#B0B0B0"; // สีเทาพาสเทล (เทาอ่อน)
                    } else if ($attendance == 'miss') {
                        $attendance = 'ขาด';
                        $color = "#FFFF99"; // สีเหลืองพาสเทล (เหลืองอ่อน)
                    } else if ($attendance == 'leave') {
                        $attendance = 'ลา';
                        $color = "#ADD8E6"; // สีฟ้าพาสเทล (ฟ้าอ่อน)
                    }

                    $sum_date += 1;
                }
            }

            $arr_data[] =  [
                "type" => "box",
                "layout" => "horizontal",
                "contents" => [
                    ["type" => "text", "text" => $date, "flex" => 2, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $time_in, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $time_brake_in, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $time_brake_out, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $time_out, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $attendance, "flex" => 3, "align" => "center", "size" => "sm", "color" => $color]

                ]
            ];
        }

        // 1. เตรียม Flex Message JSON ตรง ๆ

        // ดึงเลขเดือน
        $month_num = date('m', strtotime($date_start));

        // สร้างอาร์เรย์ชื่อเดือนภาษาไทย
        $thai_months = [
            '01' => 'มกราคม',
            '02' => 'กุมภาพันธ์',
            '03' => 'มีนาคม',
            '04' => 'เมษายน',
            '05' => 'พฤษภาคม',
            '06' => 'มิถุนายน',
            '07' => 'กรกฎาคม',
            '08' => 'สิงหาคม',
            '09' => 'กันยายน',
            '10' => 'ตุลาคม',
            '11' => 'พฤศจิกายน',
            '12' => 'ธันวาคม',
        ];

        // แปลงเลขเดือนเป็นชื่อเดือนภาษาไทย
        $thai_month = $thai_months[$month_num];

        $flexContent = [
            "type" => "bubble",
            "body" => [
                "type" => "box",
                "layout" => "vertical",
                "contents" => [
                    [
                        "type" => "text",
                        "text" => "เวลาการเข้าออกงาน",
                        "weight" => "bold",
                        "color" => "#1DB446",
                        "size" => "md"
                    ],
                    [
                        "type" => "text",
                        "text" => "พนักงาน: " . $User->first_name . ' ' . $User->last_name,
                        "weight" => "bold",
                        "size" => "lg",
                        "margin" => "md"
                    ],
                    [
                        "type" => "text",
                        "text" => "เดือน: " . $thai_month . " " . date('Y', strtotime($date_start)),
                        "size" => "sm",
                        "color" => "#666666",
                        "margin" => "md"
                    ],
                    [
                        "type" => "separator",
                        "margin" => "md"
                    ],
                    [
                        "type" => "box",
                        "layout" => "vertical",
                        "margin" => "md",
                        "spacing" => "sm",
                        "contents" => $arr_data,
                    ],
                    [
                        "type" => "separator",
                        "margin" => "md"
                    ],
                    [
                        "type" => "box",
                        "layout" => "horizontal",
                        "margin" => "md",
                        "contents" => [
                            [
                                "type" => "text",
                                "text" => "จำนวนวันทำงาน",
                                "size" => "sm",
                                "color" => "#555555",
                                "flex" => 6
                            ],
                            [
                                "type" => "text",
                                "text" => $sum_date . " วัน",
                                "size" => "sm",
                                "color" => "#111111",
                                "align" => "end",
                                "flex" => 3
                            ]
                        ]
                    ]
                ]
            ]
        ];


        // 2. ส่งด้วย CURL

        $data = [
            'type' => 'flex',
            'altText' => 'เวลาการเข้าออกงาน',
            'contents' => $flexContent
        ];

        return  $data;
    }


    static public function TimeInOutToDayUser($userId)
    {
        $User = User::where('line_id', $userId)->first();

        $date_start = date('Y-m-d'); // วันที่เริ่มต้นของเดือนปัจจุบัน
        $date_end = date('Y-m-d'); // วันที่สิ้นสุดของเดือนปัจจุบัน
        $personnel_id = $User->personnel_id;
        // $personnel_id = 'GB0004';

        $ReportController = new ReportController();
        $TimeAttendance = $ReportController->TimeAttendance($date_start, $date_end, $personnel_id, null);

        $arr_data = [];
        $sum_date = 0;

        $arr_data[0] =   [
            "type" => "box",
            "layout" => "horizontal",
            "contents" => [
                ["type" => "text", "text" => "เข้า", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "พัก", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "กลับ", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "ออก", "weight" => "bold", "flex" => 3, "align" => "center"],
                ["type" => "text", "text" => "#", "weight" => "bold", "flex" => 2, "align" => "center"]
            ]
        ];

        $dateInPeriod = self::dateInPeriodFlex($date_start, $date_end);

        for ($a = 0; $a < count($dateInPeriod); $a++) {

            $dateA = date('d', strtotime($dateInPeriod[$a]));

            $date = $dateA;
            $time_in = '-';
            $time_out = '-';
            $time_brake_in = '-';
            $time_brake_out = '-';
            $attendance = '-';
            $color = "#B0B0B0"; // สีเทาพาสเทล (เทาอ่อน)

            for ($i = 0; $i < count($TimeAttendance); $i++) {
                if ($dateA == date('d', strtotime($TimeAttendance[$i]['date']))) {

                    $time_in = !empty($TimeAttendance[$i]['time_in']) ? date('H:i', strtotime($TimeAttendance[$i]['time_in'])) : '-';
                    $time_out = !empty($TimeAttendance[$i]['time_out']) ? date('H:i', strtotime($TimeAttendance[$i]['time_out'])) : '-';
                    $time_brake_in = !empty($TimeAttendance[$i]['time_brake_in']) ? date('H:i', strtotime($TimeAttendance[$i]['time_brake_in'])) : '-';
                    $time_brake_out = !empty($TimeAttendance[$i]['time_brake_out']) ? date('H:i', strtotime($TimeAttendance[$i]['time_brake_out'])) : '-';


                    $attendance = !empty($TimeAttendance[$i]['attendance']) ? $TimeAttendance[$i]['attendance'] : '-';
                    if ($attendance == 'normal') {
                        $attendance = 'ปกติ';
                        $color = "#1DB446"; // เขียวสด (ไม่เปลี่ยน)
                    } else if ($attendance == 'late') {
                        $attendance = 'สาย';
                        $color = "#FFA500"; // สีส้มพาสเทล (ส้มอมเหลือง)
                    } else if ($attendance == 'off') {
                        $attendance = 'หยุด';
                        $color = "#B0B0B0"; // สีเทาพาสเทล (เทาอ่อน)
                    } else if ($attendance == 'miss') {
                        $attendance = 'ขาด';
                        $color = "#FFFF99"; // สีเหลืองพาสเทล (เหลืองอ่อน)
                    } else if ($attendance == 'leave') {
                        $attendance = 'ลา';
                        $color = "#ADD8E6"; // สีฟ้าพาสเทล (ฟ้าอ่อน)
                    }

                    $sum_date += 1;
                }
            }

            $arr_data[] =  [
                "type" => "box",
                "layout" => "horizontal",
                "contents" => [
                    ["type" => "text", "text" =>  $time_in, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $time_brake_in, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $time_brake_out, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $time_out, "flex" => 3, "align" => "center", "size" => "sm"],
                    ["type" => "text", "text" =>  $attendance, "flex" => 3, "align" => "center", "size" => "sm", "color" => $color]

                ]
            ];
        }



        // 1. เตรียม Flex Message JSON ตรง ๆ

        // ดึงเลขเดือน
        $month_num = date('m', strtotime($date_start));

        // สร้างอาร์เรย์ชื่อเดือนภาษาไทย
        $thai_months = [
            '01' => 'มกราคม',
            '02' => 'กุมภาพันธ์',
            '03' => 'มีนาคม',
            '04' => 'เมษายน',
            '05' => 'พฤษภาคม',
            '06' => 'มิถุนายน',
            '07' => 'กรกฎาคม',
            '08' => 'สิงหาคม',
            '09' => 'กันยายน',
            '10' => 'ตุลาคม',
            '11' => 'พฤศจิกายน',
            '12' => 'ธันวาคม',
        ];

        // แปลงเลขเดือนเป็นชื่อเดือนภาษาไทย
        $thai_month = $thai_months[$month_num];

        $flexContent = [
            "type" => "bubble",
            "body" => [
                "type" => "box",
                "layout" => "vertical",
                "contents" => [
                    [
                        "type" => "text",
                        "text" => "เวลาเข้าออกวันนี้",
                        "weight" => "bold",
                        "color" => "#1DB446",
                        "size" => "md"
                    ],
                    [
                        "type" => "text",
                        "text" => "พนักงาน: " . $User->first_name . ' ' . $User->last_name,
                        "weight" => "bold",
                        "size" => "lg",
                        "margin" => "md"
                    ],
                    [
                        "type" => "text",
                        "text" => "วันที่: " . date('d', strtotime($date_start)) . $thai_month . " " . date('Y', strtotime($date_start)),
                        "size" => "sm",
                        "color" => "#666666",
                        "margin" => "md"
                    ],
                    [
                        "type" => "separator",
                        "margin" => "md"
                    ],
                    [
                        "type" => "box",
                        "layout" => "vertical",
                        "margin" => "md",
                        "spacing" => "sm",
                        "contents" => $arr_data,
                    ],
                    [
                        "type" => "separator",
                        "margin" => "md"
                    ]
                ]
            ]
        ];


        // 2. ส่งด้วย CURL

        $data = [
            'type' => 'flex',
            'altText' => 'เวลาการเข้าออกงาน',
            'contents' => $flexContent
        ];

        return  $data;
    }

    static public function ChangeLangRichMenu($userId, $lang)
    {
        try {
            $User = User::where('line_id', $userId)->first();

            $richMenu = LineRichMenu::where('lang', $lang)->first();

            if (!$richMenu) {
                Log::warning('No rich menu found to link to user: ' . $User->id);
                return false;
            }

            //update user line lang
            $User->line_lang = $lang;
            $User->save();

            $richMenuService = app(LineRichMenuService::class);
            return $richMenuService->linkRichMenuToUser($User->line_id, $richMenu->rich_menu_id);
        } catch (Exception $e) {
            Log::error('Error linking rich menu to user: ' . $e->getMessage());
            return false;
        }
    }



    // static public function SendLineFlex($userId, $data)
    // {
    //     $accessToken = 'jb38tIMs9PzIPhbY9B2RzUb4LI5mhq0KV9VPqcI8CfKNVc5M2lP/kPK+10dExgQ62rMrJRUkd2rGpyjvFyfUuSeATsjr0Ih2h95wCLKtzTmZ/jTRBYZIfMDl8qDWa0tsK4J5JXhLxdBLBvYeH5jEwAdB04t89/1O/w1cDnyilFU=';
    //     $userId = $userId;

    //     $ch = curl_init('https://api.line.me/v2/bot/message/push');
    //     curl_setopt($ch, CURLOPT_POST, true);
    //     curl_setopt($ch, CURLOPT_HTTPHEADER, [
    //         'Content-Type: application/json',
    //         'Authorization: Bearer ' . $accessToken
    //     ]);
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    //     $response = curl_exec($ch);
    //     curl_close($ch);

    //     echo "Response: $response\n";
    // }



    // static public function SendLineFlex($userId, $data)
    // {
    //     $accessToken = 'jb38tIMs9PzIPhbY9B2RzUb4LI5mhq0KV9VPqcI8CfKNVc5M2lP/kPK+10dExgQ62rMrJRUkd2rGpyjvFyfUuSeATsjr0Ih2h95wCLKtzTmZ/jTRBYZIfMDl8qDWa0tsK4J5JXhLxdBLBvYeH5jEwAdB04t89/1O/w1cDnyilFU=';
    //     $userId = $userId;

    //     $replyToken = 'REPLY_TOKEN_FROM_WEBHOOK';

    //     $postData = [
    //         'replyToken' => $replyToken,
    //         'messages'   => [
    //             [
    //                 'type' => 'text',
    //                 'text' => 'สวัสดี! ขอบคุณที่ติดต่อมา'
    //             ]
    //         ]
    //     ];

    //     $ch = curl_init('https://api.line.me/v2/bot/message/reply');
    //     curl_setopt($ch, CURLOPT_POST, true);
    //     curl_setopt($ch, CURLOPT_HTTPHEADER, [
    //         'Content-Type: application/json',
    //         'Authorization: Bearer ' . $accessToken
    //     ]);
    //     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    //     $result = curl_exec($ch);
    //     $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    //     curl_close($ch);

    //     // Debug
    //     echo "HTTP Status: $httpCode\n";
    //     echo "Response: $result\n";
    // }

    static public function dateInPeriodFlex($start, $end)
    {

        $output = array();

        $days = floor((strtotime($end) - strtotime($start)) / 86400);

        for ($i = 0; $i <= $days; $i++) {

            array_push($output, date("Y-m-d", strtotime("+" . $i . " day", strtotime($start))));
        }

        return $output;
    }
}
