<?php

namespace App\Http\Controllers;

use App\Imports\LeavePermissionImport;
use App\Models\Leave_permission;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class LeavePermissionController extends Controller
{

    public function getLeavePermission(Request $request)
    {

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $LeavePermission = Leave_permission::where('status', 1)
            ->with('leave_type')
            ->with('user_create')
            ->where('branch_id', $login_branch_id)
            ->get()->toarray();

        if (!empty($LeavePermission)) {

            for ($i = 0; $i < count($LeavePermission); $i++) {
                $LeavePermission[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $LeavePermission);
    }

    public function LeavePermissionPage(Request $request)
    {

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $col = array('id', 'leave_type_id', 'year', 'qty', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Leave_permission::select($col)->with('leave_type')
            ->with('user_create');

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        $d->orderby($col[$order[0]['column']], $order[0]['dir']);

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;


        if (!isset($request->leave_type_id)) {
            return $this->returnErrorData('กรุณาเลือกประเภทการลา', 404);
        } else  if (!isset($request->year)) {
            return $this->returnErrorData('กรุณาระบุจำนวนอายุงาน', 404);
        } else  if (!isset($request->qty)) {
            return $this->returnErrorData('กรุณาระบุจำนวนวันลา', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $LeavePermission = new Leave_permission();
            $LeavePermission->branch_id = $login_branch_id;
            $LeavePermission->leave_type_id = $request->leave_type_id;
            $LeavePermission->year = $request->year;
            $LeavePermission->qty = $request->qty;

            $LeavePermission->status = 1;

            $LeavePermission->create_by = $loginBy->user_id;

            $LeavePermission->save();

            //log
            $userId = $loginBy->user_id;
            $type = 'เพิ่มสิทธิ์การลา';
            $description = 'User ' . $userId . ' has ' . $type . ' ';
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $LeavePermission = Leave_permission::with('leave_type')
            ->with('user_create')
            ->find($id);
        return $this->returnSuccess('Successful', $LeavePermission);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $LeavePermission = Leave_permission::find($id);

            $LeavePermission->leave_type_id = $request->leave_type_id;
            $LeavePermission->year = $request->year;
            $LeavePermission->qty = $request->qty;

            $LeavePermission->status = $request->status;

            $LeavePermission->update_by = $loginBy->user_id;
            $LeavePermission->updated_at = Carbon::now()->toDateTimeString();

            $LeavePermission->save();
            //log
            $userId = $loginBy->user_id;
            $type = 'แก้ไขสิทธิ์การลา';
            $description = 'User ' . $userId . ' has ' . $type . ' ';
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $LeavePermission = Leave_permission::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'ลบสิทธิ์การลา';
            $description = 'User ' . $userId . ' has ' . $type . ' ';
            $this->Log($userId, $description, $type);
            //

            $LeavePermission->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
}
