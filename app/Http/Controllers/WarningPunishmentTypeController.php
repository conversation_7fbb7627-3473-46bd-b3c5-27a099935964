<?php

namespace App\Http\Controllers;

use App\Imports\WarningPunishmentTypeImport;
use App\Models\Warning_punishment_type;
use App\Models\WarningPunishmentType;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class WarningPunishmentTypeController extends Controller
{

    public function getWarningPunishmentTypeWeb(Request $request)
    {

        $loginBy = $request->login_by;

        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        return $this->getWarningPunishmentType($request);
    }

    public function getWarningPunishmentTypeLine(Request $request)
    {

        $line_id = $request->line_id;
        $line_head_id = $request->line_head_id;

        $loginBy = null;
        if ($line_id) {
            $loginBy = User::where('line_id', $line_id)->first();
        } else if ($line_head_id) {
            $loginBy = User::where('line_id', $line_head_id)->first();
        }

        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $getPermissionViewData = $this->getPermissionViewData($loginBy);

        $request->request->add([
            'login_id' => $getPermissionViewData['login_id'],
            'login_by' => $getPermissionViewData['login_by'],
            'login_permission_view' => $getPermissionViewData['login_permission_view'],
            'login_user_id' => $getPermissionViewData['login_user_id'],
            'login_branch_id' => $getPermissionViewData['login_branch_id'],
            'login_company_id' => $getPermissionViewData['login_company_id'],
        ]);


        return $this->getWarningPunishmentType($request);
    }

    public function getWarningPunishmentType($request)
    {

        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $q = Warning_punishment_type::where('status', 1)
            ->with('user_create');

        if ($login_branch_id) {
            $q->where('branch_id', $login_branch_id);
        }

        $WarningPunishmentType = $q->get()->toArray();


        if (!empty($WarningPunishmentType)) {

            for ($i = 0; $i < count($WarningPunishmentType); $i++) {
                $WarningPunishmentType[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $WarningPunishmentType);
    }

    public function WarningPunishmentTypePage(Request $request)
    {

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $col = array('id', 'branch_id', 'name', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Warning_punishment_type::select($col)->with('user_create');

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        $d->orderBy($col[$order[0]['column']], $order[0]['dir']);

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        if (!isset($request->name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อประเภท OT ด้วย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        $checkName = Warning_punishment_type::where('name', $name)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $WarningPunishmentType = new Warning_punishment_type();
                $WarningPunishmentType->branch_id = $login_branch_id;
                $WarningPunishmentType->name = $name;
                $WarningPunishmentType->status = 1;
                $WarningPunishmentType->create_by = $loginBy->user_id;

                $WarningPunishmentType->save();

                //log
                $userId = $loginBy->user_id;
                $type = 'เพิ่มหัวข้อบทลงโทษใบแจ้งเตือน';
                $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $WarningPunishmentType = Warning_punishment_type::find($id);
        return $this->returnSuccess('Successful', $WarningPunishmentType);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        $checkName = Warning_punishment_type::where('id', '!=', $id)
            ->where('name', $name)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $WarningPunishmentType = Warning_punishment_type::find($id);

                $WarningPunishmentType->name = $name;
                $WarningPunishmentType->status = $request->status;

                $WarningPunishmentType->update_by = $loginBy->user_id;
                $WarningPunishmentType->updated_at = Carbon::now()->toDateTimeString();

                $WarningPunishmentType->save();
                //log
                $userId = $loginBy->user_id;
                $type = 'แก้ไขหัวข้อบทลงโทษใบแจ้งเตือน';
                $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $WarningPunishmentType->name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnUpdate('Successful operation');
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $WarningPunishmentType = Warning_punishment_type::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete หัวข้อบทลงโทษใบแจ้งเตือน';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $WarningPunishmentType->name;
            $this->Log($userId, $description, $type);
            //

            $WarningPunishmentType->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    // public function ImportWarningPunishmentType(Request $request)
    // {

    //     $loginBy = $request->login_by;

    //     if (!isset($loginBy)) {
    //         return $this->returnErrorData('User information not found. Please login again', 404);
    //     }

    //     $file = request()->file('file');
    //     $fileName = $file->getClientOriginalName();

    //     $Data = Excel::toArray(new WarningPunishmentTypeImport(), $file);
    //     $data = $Data[0];

    //     if (count($data) > 0) {

    //         $insert_data = [];

    //         for ($i = 0; $i < count($data); $i++) {

    //             $name = trim($data[$i]['name']);

    //             $row = $i + 2;

    //             if ($name == '') {
    //                 return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
    //             }

    //             //check row sample
    //             if ($name == 'SIMPLE-000') {
    //                 //
    //             } else {

    //                 // //check name
    //                 // $WarningPunishmentType = Warning_punishment_type::where('name', $name)->first();
    //                 // if ($WarningPunishmentType) {
    //                 //     return $this->returnErrorData('WarningPunishmentType ' . $name . ' was information information is already in the system', 404);
    //                 // }

    //                 //check dupicate data form file import
    //                 for ($j = 0; $j < count($insert_data); $j++) {

    //                     if ($name == $insert_data[$j]['name']) {
    //                         return $this->returnErrorData('WarningPunishmentType ' . $name . ' There is duplicate data in the import file', 404);
    //                     }
    //                 }
    //                 ///

    //                 $insert_data[] = array(
    //                     'name' => $name,
    //                     'status' => 1,
    //                     'created_at' => date('Y-m-d H:i:s'),
    //                     'updated_at' => date('Y-m-d H:i:s'),
    //                 );
    //             }
    //         }

    //         if (!empty($insert_data)) {

    //             DB::beginTransaction();

    //             try {

    //                 //updateOrInsert
    //                 for ($i = 0; $i < count($insert_data); $i++) {

    //                     DB::table('Ot Type')
    //                         ->updateOrInsert(
    //                             [
    //                                 'id' => trim($data[$i]['id']), //id
    //                             ],
    //                             $insert_data[$i]
    //                         );
    //                 }
    //                 //

    //                 DB::commit();

    //                 //log
    //                 $userId = $loginBy->user_id;
    //                 $type = 'Import หัวข้อบทลงโทษใบแจ้งเตือน';
    //                 $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
    //                 $this->Log($userId, $description, $type);
    //                 //

    //                 DB::commit();

    //                 return $this->returnSuccess('Successful operation', []);
    //             } catch (\Throwable $e) {

    //                 DB::rollback();

    //                 return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
    //             }
    //         } else {
    //             return $this->returnErrorData('Data Not Found', 404);
    //         }
    //     } else {
    //         return $this->returnErrorData('Data Not Found', 404);
    //     }
    // }
}
