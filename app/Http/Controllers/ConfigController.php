<?php

namespace App\Http\Controllers;

use App\Models\Config;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ConfigController extends Controller
{
    public function index(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        // dd($request);

        $Config = Config::where('branch_id', $login_branch_id)->first();

        return $this->returnSuccess('Successful', $Config);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $login_permission_view = $request->login_permission_view;
            $login_user_id = $request->login_user_id;
            $login_branch_id = $request->login_branch_id;

            $checkConfig = Config::where('branch_id', $login_branch_id)->first();

            if (!$checkConfig) {

                //add config
                $Config = new Config();
                $Config->branch_id =  $login_branch_id;
                $Config->telesales_limit_req = $request->telesales_limit_req;
                $Config->telesales_percent_req = $request->telesales_percent_req;
                $Config->telesales_period_daycall = $request->telesales_period_daycall;

                $Config->commission_calculate = $request->commission_calculate;

                if ($request->company_name) {
                    $Config->company_name = $request->company_name;
                }
                if ($request->company_address) {
                    $Config->company_address = $request->company_address;
                }

                // กำหนดเงินประกันการทำงาน
                if ($request->bond_required) {
                    $Config->bond_required = $request->bond_required;
                }
                if ($request->bond_deduct_per_month) {
                    $Config->bond_deduct_per_month = $request->bond_deduct_per_month;
                }

                $Config->create_by = $loginBy->user_id;

                $Config->save();
            } else {

                //update config
                $checkConfig->telesales_limit_req = $request->telesales_limit_req;
                $checkConfig->telesales_percent_req = $request->telesales_percent_req;
                $checkConfig->telesales_period_daycall = $request->telesales_period_daycall;

                $checkConfig->commission_calculate = $request->commission_calculate;

                if ($request->company_name) {
                    $checkConfig->company_name = $request->company_name;
                }
                if ($request->company_address) {
                    $checkConfig->company_address = $request->company_address;
                }
                // กำหนดเงินประกันการทำงาน
                if ($request->bond_required) {
                    $checkConfig->bond_required = $request->bond_required;
                }
                if ($request->bond_deduct_per_month) {
                    $checkConfig->bond_deduct_per_month = $request->bond_deduct_per_month;
                }

                $checkConfig->update_by = $loginBy->user_id;

                $checkConfig->save();
            }

            //log
            $userId = $loginBy->user_id;
            $type = 'Setting Config';
            $description = 'User ' . $userId . ' has ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }
}
