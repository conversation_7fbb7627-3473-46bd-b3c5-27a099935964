<?php

namespace App\Http\Controllers;

use App\Models\Config;
use App\Models\DeductType;
use App\Models\User;
use App\Models\Withdraw_salary;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WithDrawSalaryController extends Controller
{
    public function get(Request $request)
    {

        $user_id = $request->user_id;
        $item = Withdraw_salary::with('user')
            ->with('user_create');
        if ($user_id) {
            $item->where('user',  $user_id);
        }
        $Item =  $item->get();

        if (!empty($Item)) {

            for ($i = 0; $i < count($Item); $i++) {
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    public function WithDrawSalaryPage(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->Page($request);
    }

    public function WithDrawSalaryPageLine(Request $request)
    {

        $line_id = $request->line_id;
        $line_head_id = $request->line_head_id;

        if ($line_id) {
            $loginBy = User::where('line_id', $line_id)->first();
        } else if ($line_head_id) {
            $loginBy = User::where('line_id', $line_head_id)->first();
        }

        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $getPermissionViewData = $this->getPermissionViewData($loginBy);

        $request->request->add([
            'login_id' => $getPermissionViewData['login_id'],
            'login_by' => $getPermissionViewData['login_by'],
            'login_permission_view' => $getPermissionViewData['login_permission_view'],
            'login_user_id' => $getPermissionViewData['login_user_id'],
            'login_branch_id' => $getPermissionViewData['login_branch_id'],
            'login_company_id' => $getPermissionViewData['login_company_id'],
        ]);


        return $this->Page($request);
    }


    public function Page(Request $request)
    {
        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;


        $user_id = $request->user_id;
        $line_id = $request->line_id;

        $Status = $request->status;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $col = array('id', 'date', 'user_id', 'month_withdraw', 'objective', 'qty', 'status', 'status_by', 'status_at', 'remark', 'create_by', 'update_by', 'created_at', 'updated_at');

        $orderby = array('id', 'date', 'user_id', 'month_withdraw', 'objective', 'qty', 'status', 'status_by', 'status_at', 'remark', 'create_by', 'update_by', 'created_at', 'updated_at');


        $d = Withdraw_salary::select($col)
            ->with('user')
            ->with('user_create');

        // if ($login_company_id) {
        //     $d->WhereHas('user', function ($query) use ($login_company_id) {
        //         $query->WhereHas('branch', function ($query) use ($login_company_id) {
        //             $query->where('company_id', $login_company_id);
        //         });
        //     });
        // }

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        if ($user_id) {
            $d->where('user_id',  $user_id);
        }

        if ($line_id) {
            $d->WhereHas('user', function ($query) use ($line_id) {
                $query->where('line_id', $line_id);
            });
        }


        if (isset($Status)) {
            $d->where('status', $Status);
        }

        if ($orderby[$order[0]['column']]) {
            $d->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            $d->Where(function ($query) use ($search, $col) {

                //search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$c) {
                        $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                    }
                });

                //search with
                // //$query = $this->withPermission($query, $search);
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาระบุ user_id ให้เรียบร้อย', 404);
        } else if (!isset($request->date)) {
            return $this->returnErrorData('กรุณาระบุ date ให้เรียบร้อย', 404);
        } else if (!isset($request->objective)) {
            return $this->returnErrorData('กรุณาระบุ วัตถุประสงค์ ให้เรียบร้อย', 404);
        } else if (!isset($request->qty)) {
            return $this->returnErrorData('กรุณาระบุ จำนวนเงิน ให้เรียบร้อย', 404);
        }

        $user_id = $loginBy->id;
        return  $this->addWithDrawSalary($user_id, $request, $loginBy);
    }

    public function addLine(Request $request)
    {

        $line_id = $request->line_id;

        if (!isset($request->line_id)) {
            return $this->returnErrorData('กรุณาส่ง line_id', 404);
        } else if (!isset($request->date)) {
            return $this->returnErrorData('กรุณาระบุ date ให้เรียบร้อย', 404);
        } else if (!isset($request->objective)) {
            return $this->returnErrorData('กรุณาระบุ วัตถุประสงค์ ให้เรียบร้อย', 404);
        } else if (!isset($request->qty)) {
            return $this->returnErrorData('กรุณาระบุ จำนวนเงิน ให้เรียบร้อย', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();
        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $user_id = $loginBy->id;
        return  $this->addWithDrawSalary($user_id, $request, $loginBy);
    }

    public function addWithDrawSalary($user_id, $request, $loginBy)
    {
        DB::beginTransaction();

        try {
            $login_user_id = $request->login_user_id;
            $login_branch_id = $request->login_branch_id;
            $login_company_id = $request->login_company_id;

            $Item = new Withdraw_salary();
            $Item->branch_id =  $login_branch_id;
            $Item->date = $request->date;
            $Item->user_id = $request->user_id;


            $checkConfig = Config::where('branch_id',  $login_branch_id)->first();

            if ($checkConfig) {
                if (date('d', strtotime($request->date)) > $checkConfig->salary_pay_date) {
                    $Item->month_withdraw = date("Y-m", strtotime($request->date . " first day of next month"));
                } else {
                    $Item->month_withdraw = date("Y-m");
                }
            }


            $Item->objective = $request->objective;
            $Item->qty = $request->qty;
            $Item->status = 'open';

            $Item->create_by = $loginBy->user_id;

            $Item->save();
            //


            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', $Item);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Withdraw_salary  $Withdraw_salary
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Item = Withdraw_salary::with('user')
            ->with('user_create')
            ->where('id', $id)
            ->first();


        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Withdraw_salary  $Withdraw_salary
     * @return \Illuminate\Http\Response
     */
    public function edit(Withdraw_salary $Withdraw_salary)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Withdraw_salary  $Withdraw_salary
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('ไม่พบข้อมูล id', 404);
        } else
        //

        {
            DB::beginTransaction();
        }

        try {

            $Item = Withdraw_salary::find($id);
            if (!$Item) {
                return $this->returnErrorData('ไม่พบข้อมูลในระบบ', 404);
            }
            $Item->objective = $request->objective;
            $Item->qty = $request->qty;
            $Item->save();

            DB::commit();

            return $this->returnUpdate('ดำเนินการสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Withdraw_salary  $Withdraw_salary
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Item = Withdraw_salary::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Item';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Item->name;
            $this->Log($userId, $description, $type);
            //

            $Item->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function Approved(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data NLeaveTable Found', 404);
        }


        DB::beginTransaction();

        try {

            $Withdraw_salary = Withdraw_salary::with('user.position')
                ->find($id);

            $Withdraw_salary->status = $request->status;
            if ($Withdraw_salary->status == 'approved') {
            }


            if ($Withdraw_salary->status == 'cancel') {
                $Withdraw_salary->remark = $request->remark;
            }

            $Withdraw_salary->status_by = $loginBy->user_id;
            $Withdraw_salary->status_at = Carbon::now()->toDateTimeString();

            $Withdraw_salary->save();


            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
}
