<?php

namespace App\Http\Controllers;

use App\Models\Amphure;
use App\Models\District;
use App\Models\Provice;
use App\Models\Province;
use App\Models\Sub_district;
use App\Models\Tambon;
use Illuminate\Http\Request;

class AddressController extends Controller
{

    public function getProvince()
    {
        $Provice = Province::get();
        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Provice);
    }

    public function getDistrict(Request $request)
    {
        $provinceId = $request->province_id;
        $provinceName = $request->province_name;

        $district = Amphure::query();

        if ($provinceId) {
            $district->where('province_id', $provinceId);
        }
        if ($provinceName) {
            $district->WhereHas('province', function ($query) use ($provinceName) {
                $query->where('name_th', $provinceName);
            });
        }

        $District = $district->get();
        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $District);
    }

    public function getSubDistrict(Request $request)
    {
        $districtId = $request->district_id;
        $districtName = $request->district_name;

        $district = Tambon::query();

        if ($districtId) {
            $district->where('amphure_id', $districtId);
        }
        if ($districtName) {
            $district->WhereHas('amphure', function ($query) use ($districtName) {
                $query->where('name_th', $districtName);
            });
        }

        $District = $district->get();
        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $District);
    }

    public function getAddress(Request $request)
    {

        $search = $request->query('search', "");
        $search1 = $request->query('search1', "");
        $search2 = $request->query('search2', "");
        $search3 = $request->query('search3', "");

        $searchable = (new Tambon)->getTableColumns();

        $district = Tambon::with('amphure.province');

        $district->orWhere(function ($query) use ($search, $search1, $search2, $search3, $searchable) {

            if ($search) {
                $query->where(function ($query) use ($search, $searchable) {

                    // search datatable
                    $query->orWhere(function ($query) use ($search, $searchable) {
                        foreach ($searchable as &$s) {
                            $query->orWhere($s, 'LIKE', '%' . $search . '%');
                        }
                    });

                    // search with
                    $query = $this->withAmphure($query, $search);
                });
            }

            if ($search1) {
                $query->where(function ($query) use ($search1, $searchable) {

                    // search datatable
                    $query->orWhere(function ($query) use ($search1, $searchable) {
                        foreach ($searchable as &$s) {
                            $query->orWhere($s, 'LIKE', '%' . $search1 . '%');
                        }
                    });

                    // search with
                    $query = $this->withAmphure($query, $search1);
                });
            }

            if ($search2) {
                $query->where(function ($query) use ($search2, $searchable) {

                    // search datatable
                    $query->orWhere(function ($query) use ($search2, $searchable) {
                        foreach ($searchable as &$s) {
                            $query->orWhere($s, 'LIKE', '%' . $search2 . '%');
                        }
                    });

                    // search with
                    $query = $this->withAmphure($query, $search2);
                });
            }

            if ($search3) {
                $query->where(function ($query) use ($search3, $searchable) {

                    // search datatable
                    $query->orWhere(function ($query) use ($search3, $searchable) {
                        foreach ($searchable as &$s) {
                            $query->orWhere($s, 'LIKE', '%' . $search3 . '%');
                        }
                    });

                    // search with
                    $query = $this->withAmphure($query, $search3);
                });
            }
        });



        $District = $district->get();

        for ($i = 0; $i < count($District); $i++) {

            $District[$i]->amphure_th = $District[$i]->amphure->name_th;
            $District[$i]->amphure_en = $District[$i]->amphure->name_en;

            $District[$i]->province_en = (!empty($District[$i]->amphure->province) ? $District[$i]->amphure->province->name_en : null);
            $District[$i]->province_th = (!empty($District[$i]->amphure->province) ? $District[$i]->amphure->province->name_th : null);
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $District);
    }
}
