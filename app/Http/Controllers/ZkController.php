<?php

namespace App\Http\Controllers;

use App\Models\Holiday;
use App\Models\Leave_table_date;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\Work_shift_time;
use App\Models\Zk_time;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ZkController extends Controller
{

    // protected $base_url = 'http://zkteco.dev-asha.com'; //prod
    // protected $base_url = 'http://zkteco.dev-asha.com'; //demo
    // protected $access_Key = 'cd0cc6d8e6d64ad8a8f3dd58bec9d3c9';
    // protected $org_code = '000001';

    protected $base_url;
    protected $access_Key;
    protected $org_code;

    public function __construct()
    {
        $this->base_url = config('zk.zk_base_url'); //demo
        $this->access_Key = config('zk.zk_key');
        $this->org_code = config('zk.zk_org_code');
    }


    public function uploadImg($data)
    {


        try {

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Access-Key' => $this->access_Key,
            ])
                ->post($this->base_url . '/api/v1/upload/img', [
                    "orgCode" => $this->org_code,
                    "userNo" => $data['userNo'],
                    "name" => $data['name'],
                    "startTime" => $data['startTime'],
                    "endTime" => $data['endTime'],
                    "file" => $data['file'],
                ]);

            $res = null;
            $olaf_id = null;
            $result = null;

            // return $response;

            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //
                if ($res->status == 'ok') {

                    $deviceCode = array_key_first((array) $res->data);
                    $deviceData = $res->data->{$deviceCode}[0];

                    $result = [
                        'status' => 200,
                        'res' => $res,
                        'cmd_id' => $deviceData->cmdId ?? null,
                        'action' => $deviceData->action ?? null,
                        'sn' => $deviceData->sn ?? null,
                        'device_status' => $deviceData->status ?? null,
                        'device_code' => $deviceCode,
                    ];
                } else {
                    $result = [
                        'status' => 404,
                        'res' => $res->msg,
                        'cmd_id' => null,
                        'action' => null,
                        'sn' => null,
                        'device_status' => null,
                        'device_code' => null,
                    ];
                }
            } else {

                $Res = $response->body();
                $res = json_decode($Res); //

                $result = [
                    'status' => 404,
                    'res' => $res->msg,
                    'cmd_id' => null,
                    'action' => null,
                    'sn' => null,
                    'device_status' => null,
                    'device_code' => null,
                ];
            }
            return $result;
        } catch (\Throwable $e) {

            $result = [
                'status' => 404,
                'res' => $response,
                'cmd_id' => null,
                'action' => null,
                'sn' => null,
                'device_status' => null,
                'device_code' => null,
            ];

            return $result;
        }
    }


    public function delete($data)
    {
        try {

            // $response = Http::withHeaders([
            //     'Content-Type' => 'application/json',
            //     'Access-Key' => $this->access_Key,
            // ])
            //     ->delete($this->base_url . '/api/v1/delete/img', [
            //         "orgCode" => $this->org_code,
            //         "userNo" => $data['userNo'],
            //         "sns" => $data['sns']
            //     ]);


            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Access-Key' => $this->access_Key,
            ])->delete($this->base_url . '/api/v1/delete/imgs', [
                "orgCode" => $this->org_code,
                "users" => [
                    ["userNo" => $data['userNo']]
                ],
            ]);
        } catch (\Throwable $e) {
            //
        }
    }


    public function addZkTime(Request $request)
    {

        $key = md5('ashatechxokt2024!globalrich88');

        $token = $request->header('token');
        if (!$token) {
            return response()->json([
                'code' => '401',
                'status' => false,
                'message' => 'Token Not Found',
                'data' => [],
            ], 401);
        }


        if ($token != $key) {
            return response()->json([
                'code' => '401',
                'status' => false,
                'message' => 'Can not verify identity',
                'data' => [],
            ], 401);
        }

        DB::beginTransaction();

        try {

            $zkTime = new Zk_time();
            $zkTime->event_id = $request->event_id;
            $zkTime->time = $request->time;
            $zkTime->area_name = $request->area_name;
            $zkTime->device_name = $request->device_name;
            $zkTime->event_point = $request->event_point;
            $zkTime->event_description = $request->event_description;
            $zkTime->personnel_id = $request->personnel_id;
            $zkTime->first_name = $request->first_name;
            $zkTime->last_name = $request->last_name;
            $zkTime->card_number = $request->card_number;
            $zkTime->department_number = $request->department_number;
            $zkTime->department_name = $request->department_name;
            $zkTime->reader_name = $request->reader_name;
            $zkTime->verification_mode = $request->verification_mode;

            // Save the model instance to database
            $zkTime->save();

            DB::commit();
        } catch (\Throwable $e) {

            DB::rollback();
        }
    }

    public function putZkTime(Request $request)
    {
        $user_id = $request->user_id;

        $line_id = $request->line_id;
        $latitude = $request->latitude;
        $longitude = $request->longitude;
        $pic = $request->pic;
        $time = $request->time;
        $temperature = $request->temperature;
        $cameraName = $request->cameraName;
        $cameraSn = $request->cameraSn;
        $eventType = $request->eventType;
        $cardNo = $request->cardNo;

        $User = null;
        if ($line_id) {
            $User = User::where('line_id', $line_id)->first();
            if (!$User) {
                return response()->json([
                    'code' => '401',
                    'status' => false,
                    'message' => 'User Not Found',
                    'data' => [],
                ], 401);
            }
        } else {

            $User = User::where('id', $user_id)->first();
            if (!$User) {
                return response()->json([
                    'code' => '401',
                    'status' => false,
                    'message' => 'User Not Found',
                    'data' => [],
                ], 401);
            }
        }

        return $this->putZkTimes($User, $pic, $time, $cameraSn, $cameraName, $eventType, $latitude, $longitude);
    }

    public function putZkTimeCamera(Request $request)
    {
        $user = $request->user;
        $pic = $request->pic;
        $time = $request->time;
        $temperature = $request->temperature;
        $cameraName = $request->cameraName;
        $cameraSn = $request->cameraSn;
        $eventType = $request->eventType;
        $cardNo = $request->cardNo;
        $latitude = $request->latitude;
        $longitude = $request->longitude;

        $User = User::where('user_id', $user)->first();
        if (!$User) {
            return response()->json([
                'code' => '401',
                'status' => false,
                'message' => 'User Not Found',
                'data' => [],
            ], 401);
        }

        return $this->putZkTimes($User, $pic, $time, $cameraSn, $cameraName, $eventType, $latitude, $longitude);
    }

    public function putZkTimes($User, $pic, $time, $cameraSn, $cameraName, $eventType, $latitude, $longitude)
    {

        DB::beginTransaction();

        try {

            $zkTime = new Zk_time();
            $zkTime->event_id = $User->user_id;
            $zkTime->pic = $pic;
            $zkTime->time = $time;
            $zkTime->area_name = $cameraSn;
            $zkTime->device_name = $cameraName;
            $zkTime->event_point = $cameraName;
            $zkTime->event_description = $cameraName;
            $zkTime->personnel_id = $User->user_id;
            $zkTime->first_name = null;
            $zkTime->last_name = null;
            $zkTime->card_number = null;
            $zkTime->department_number = null;
            $zkTime->department_name = null;
            $zkTime->reader_name = null;
            $zkTime->verification_mode = $eventType;
            $zkTime->latitude = $latitude;
            $zkTime->longitude = $longitude;

            // Save the model instance to database
            $zkTime->save();

            //update attendance
            $dateYesterday = explode(' ', $time)[0];

            //check duplicate Attendance
            $check = User_attendance::where('user_id', $User->id)
                ->where('date', $dateYesterday)
                ->first();

            if (!$check) {

                // เช็คว่าอยู่กลุ่ม ผู้บริหาร ป่าว
                if ($User->work_shift_group_id == 2) {

                    //normal
                    $User_attendance =  new User_attendance();
                    $User_attendance->date =  $dateYesterday;
                    $User_attendance->user_id =  $User->id;
                    $User_attendance->zkt_time_id = null;
                    $User_attendance->type = 'normal';
                    $User_attendance->leave_table_id = null;
                    $User_attendance->status = true;
                    $User_attendance->save();

                    // continue; // ข้ามไป user อื่น

                } else {



                    //add

                    $strDate = date('D', strtotime($dateYesterday));

                    //check working day
                    $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                    if ($User->work_shift_id) {
                        $workTime->where('work_shift_id', $User->work_shift_id);
                    }
                    $WorkTime =  $workTime->first();

                    if ($WorkTime) {

                        //check holiday
                        $Holiday =  Holiday::where('date', $dateYesterday)->first();
                        if (!$Holiday) {

                            if ($WorkTime->status == true) {
                                //
                                $Zk_time =  Zk_time::where('personnel_id', $User->personnel_id)
                                    ->where('time', 'like', '%' . $dateYesterday . '%')
                                    ->orderby('time')
                                    ->first();

                                //check leave
                                $userID =  $User->id;
                                $Leave_table_date =  Leave_table_date::with('leave_table')
                                    ->where('date', $dateYesterday)
                                    ->WhereHas('leave_table', function ($query) use ($userID) {
                                        $query->where('user_id', $userID);
                                        $query->where('status', 'approved');
                                    })
                                    ->first();

                                if ($Leave_table_date) {
                                    //leave
                                    $User_attendance =  new User_attendance();
                                    $User_attendance->date =  $dateYesterday;
                                    $User_attendance->user_id =  $User->id;
                                    $User_attendance->zkt_time_id = null;
                                    $User_attendance->type = 'leave';
                                    $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                    $User_attendance->status = true;
                                    $User_attendance->save();
                                } else if ($Zk_time) {

                                    if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                        //late
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User->id;
                                        $User_attendance->zkt_time_id = $Zk_time->id;
                                        $User_attendance->type = 'late';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    } else {
                                        //normal
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User->id;
                                        $User_attendance->zkt_time_id = $Zk_time->id;
                                        $User_attendance->type = 'normal';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    }
                                } else {

                                    //miss
                                    $User_attendance =  new User_attendance();
                                    $User_attendance->date =  $dateYesterday;
                                    $User_attendance->user_id =  $User->id;
                                    $User_attendance->zkt_time_id = null;
                                    $User_attendance->type = 'miss';
                                    $User_attendance->leave_table_id = null;
                                    $User_attendance->status = true;
                                    $User_attendance->save();
                                }
                            } else {
                                //off
                                $User_attendance =  new User_attendance();
                                $User_attendance->date =  $dateYesterday;
                                $User_attendance->user_id =  $User->id;
                                $User_attendance->zkt_time_id = null;
                                $User_attendance->type = 'off';
                                $User_attendance->leave_table_id = null;
                                $User_attendance->status = true;
                                $User_attendance->save();
                            }
                        } else {
                            //off
                            $User_attendance =  new User_attendance();
                            $User_attendance->date =  $dateYesterday;
                            $User_attendance->user_id =  $User->id;
                            $User_attendance->zkt_time_id = null;
                            $User_attendance->type = 'off';
                            $User_attendance->leave_table_id = null;
                            $User_attendance->status = true;
                            $User_attendance->save();
                        }
                    }
                }
            } else {

                //update

                $strDate = date('D', strtotime($dateYesterday));

                //check working day
                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                if ($User->work_shift_id) {
                    $workTime->where('work_shift_id', $User->work_shift_id);
                }
                $WorkTime =  $workTime->first();

                if ($WorkTime) {

                    //check holiday
                    $Holiday =  Holiday::where('date', $dateYesterday)->first();
                    if (!$Holiday) {

                        if ($WorkTime->status == true) {
                            //
                            $Zk_time =  Zk_time::where('personnel_id', $User->personnel_id)
                                ->where('time', 'like', '%' . $dateYesterday . '%')
                                ->orderby('time')
                                ->first();

                            //check leave
                            $userID =  $User->id;
                            $Leave_table_date =  Leave_table_date::with('leave_table')
                                ->where('date', $dateYesterday)
                                ->WhereHas('leave_table', function ($query) use ($userID) {
                                    $query->where('user_id', $userID);
                                    $query->where('status', 'approved');
                                })
                                ->first();

                            if ($Leave_table_date) {
                                //leave
                                $User_attendance =  new User_attendance();
                                $User_attendance->date =  $dateYesterday;
                                $User_attendance->user_id =  $User->id;
                                $User_attendance->zkt_time_id = null;
                                $User_attendance->type = 'leave';
                                $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                $User_attendance->status = true;
                                $User_attendance->save();
                            } else if ($Zk_time) {

                                if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                    //late
                                    $check->date =  $dateYesterday;
                                    $check->user_id =  $User->id;
                                    $check->zkt_time_id = $Zk_time->id;
                                    $check->type = 'late';
                                    $check->leave_table_id = null;
                                    $check->status = true;
                                    $check->save();
                                } else {
                                    //normal
                                    $check->date =  $dateYesterday;
                                    $check->user_id =  $User->id;
                                    $check->zkt_time_id = $Zk_time->id;
                                    $check->type = 'normal';
                                    $check->leave_table_id = null;
                                    $check->status = true;
                                    $check->save();
                                }
                            } else {

                                //miss
                                $check->date =  $dateYesterday;
                                $check->user_id =  $User->id;
                                $check->zkt_time_id = null;
                                $check->type = 'miss';
                                $check->leave_table_id = null;
                                $check->status = true;
                                $check->save();
                            }
                        } else {
                            //off
                            $check->date =  $dateYesterday;
                            $check->user_id =  $User->id;
                            $check->zkt_time_id = null;
                            $check->type = 'off';
                            $check->leave_table_id = null;
                            $check->status = true;
                            $check->save();
                        }
                    } else {
                        //off
                        $check->date =  $dateYesterday;
                        $check->user_id =  $User->id;
                        $check->zkt_time_id = null;
                        $check->type = 'off';
                        $check->leave_table_id = null;
                        $check->status = true;
                        $check->save();
                    }
                }
            }


            DB::commit();
            return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', []);
        } catch (\Throwable $e) {

            Log::error($e);

            DB::rollback();
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    public function punchlog(Request $request)
    {

        $start = $request->start . ' 00:00:00';
        $end = $request->end . ' 23:59:59';

        try {

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Access-Key' => $this->access_Key,
            ])
                ->get($this->base_url . '/api/v1/attendances?sortBy=timeStamp&page=1&pageSize=400&orgCode=' . $this->org_code . '&timeStart=' . $start . '&timeEnd=' . $end);

            $res = null;

            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //

                if ($res->status == 'ok') {
                    //
                    $Data = $res->data->list;

                    for ($i = 0; $i < count($Data); $i++) {

                        $checkTime = Zk_time::where('time', $Data[$i]->timeStamp)->first();

                        if (!$checkTime) {

                            $zkTime = new Zk_time();
                            $zkTime->event_id =  $Data[$i]->userNo;
                            $zkTime->time = $Data[$i]->timeStamp;
                            $zkTime->area_name = $Data[$i]->deviceName;
                            $zkTime->device_name = $Data[$i]->deviceName;
                            $zkTime->event_point = $Data[$i]->deviceName;
                            $zkTime->event_description = $Data[$i]->deviceName;
                            $zkTime->personnel_id = $Data[$i]->userNo;
                            $zkTime->first_name = '';
                            $zkTime->last_name = '';
                            $zkTime->card_number = $Data[$i]->userNo;
                            $zkTime->department_number = '';
                            $zkTime->department_name = '';
                            $zkTime->reader_name = '';
                            $zkTime->verification_mode = '';

                            // Save the model instance to database
                            $zkTime->save();


                            ///////////////////////////////// add Attendance /////////////////////////////////
                            $dateYesterday = date('Y-m-d', strtotime($Data[$i]->timeStamp));

                            $User = User::where('personnel_id', $Data[$i]->userNo)->first();
                            if ($User) {

                                //check duplicate Attendance
                                $check = User_attendance::where('user_id', $User->id)
                                    ->where('date', $dateYesterday)
                                    ->first();

                                if (!$check) {

                                    // เช็คว่าอยู่กลุ่ม ผู้บริหาร ป่าว
                                    if ($User->work_shift_group_id == 2) {

                                        //normal
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User->id;
                                        $User_attendance->zkt_time_id = null;
                                        $User_attendance->type = 'normal';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();

                                        // continue; // ข้ามไป user อื่น

                                    } else {

                                        //add

                                        $strDate = date('D', strtotime($dateYesterday));

                                        //check working day
                                        $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                                        if ($User->work_shift_id) {
                                            $workTime->where('work_shift_id', $User->work_shift_id);
                                        }
                                        $WorkTime =  $workTime->first();

                                        if ($WorkTime) {

                                            //check holiday
                                            $Holiday =  Holiday::where('date', $dateYesterday)->first();
                                            if (!$Holiday) {

                                                if ($WorkTime->status == true) {
                                                    //
                                                    $Zk_time =  Zk_time::where('personnel_id', $User->personnel_id)
                                                        ->where('time', 'like', '%' . $dateYesterday . '%')
                                                        ->orderby('time')
                                                        ->first();

                                                    //check leave
                                                    $userID =  $User->id;
                                                    $Leave_table_date =  Leave_table_date::with('leave_table')
                                                        ->where('date', $dateYesterday)
                                                        ->WhereHas('leave_table', function ($query) use ($userID) {
                                                            $query->where('user_id', $userID);
                                                            $query->where('status', 'approved');
                                                        })
                                                        ->first();

                                                    if ($Leave_table_date) {
                                                        //leave
                                                        $User_attendance =  new User_attendance();
                                                        $User_attendance->date =  $dateYesterday;
                                                        $User_attendance->user_id =  $User->id;
                                                        $User_attendance->zkt_time_id = null;
                                                        $User_attendance->type = 'leave';
                                                        $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                                        $User_attendance->status = true;
                                                        $User_attendance->save();
                                                    } else if ($Zk_time) {

                                                        if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                                            //late
                                                            $User_attendance =  new User_attendance();
                                                            $User_attendance->date =  $dateYesterday;
                                                            $User_attendance->user_id =  $User->id;
                                                            $User_attendance->zkt_time_id = $Zk_time->id;
                                                            $User_attendance->type = 'late';
                                                            $User_attendance->leave_table_id = null;
                                                            $User_attendance->status = true;
                                                            $User_attendance->save();
                                                        } else {
                                                            //normal
                                                            $User_attendance =  new User_attendance();
                                                            $User_attendance->date =  $dateYesterday;
                                                            $User_attendance->user_id =  $User->id;
                                                            $User_attendance->zkt_time_id = $Zk_time->id;
                                                            $User_attendance->type = 'normal';
                                                            $User_attendance->leave_table_id = null;
                                                            $User_attendance->status = true;
                                                            $User_attendance->save();
                                                        }
                                                    } else {

                                                        //miss
                                                        $User_attendance =  new User_attendance();
                                                        $User_attendance->date =  $dateYesterday;
                                                        $User_attendance->user_id =  $User->id;
                                                        $User_attendance->zkt_time_id = null;
                                                        $User_attendance->type = 'miss';
                                                        $User_attendance->leave_table_id = null;
                                                        $User_attendance->status = true;
                                                        $User_attendance->save();
                                                    }
                                                } else {
                                                    //off
                                                    $User_attendance =  new User_attendance();
                                                    $User_attendance->date =  $dateYesterday;
                                                    $User_attendance->user_id =  $User->id;
                                                    $User_attendance->zkt_time_id = null;
                                                    $User_attendance->type = 'off';
                                                    $User_attendance->leave_table_id = null;
                                                    $User_attendance->status = true;
                                                    $User_attendance->save();
                                                }
                                            } else {
                                                //off
                                                $User_attendance =  new User_attendance();
                                                $User_attendance->date =  $dateYesterday;
                                                $User_attendance->user_id =  $User->id;
                                                $User_attendance->zkt_time_id = null;
                                                $User_attendance->type = 'off';
                                                $User_attendance->leave_table_id = null;
                                                $User_attendance->status = true;
                                                $User_attendance->save();
                                            }
                                        }
                                    }
                                } else {

                                    //update

                                    $strDate = date('D', strtotime($dateYesterday));

                                    //check working day
                                    $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                                    if ($User->work_shift_id) {
                                        $workTime->where('work_shift_id', $User->work_shift_id);
                                    }
                                    $WorkTime =  $workTime->first();

                                    if ($WorkTime) {

                                        //check holiday
                                        $Holiday =  Holiday::where('date', $dateYesterday)->first();
                                        if (!$Holiday) {

                                            if ($WorkTime->status == true) {
                                                //
                                                $Zk_time =  Zk_time::where('personnel_id', $User->personnel_id)
                                                    ->where('time', 'like', '%' . $dateYesterday . '%')
                                                    ->orderby('time')
                                                    ->first();

                                                //check leave
                                                $userID =  $User->id;
                                                $Leave_table_date =  Leave_table_date::with('leave_table')
                                                    ->where('date', $dateYesterday)
                                                    ->WhereHas('leave_table', function ($query) use ($userID) {
                                                        $query->where('user_id', $userID);
                                                        $query->where('status', 'approved');
                                                    })
                                                    ->first();

                                                if ($Leave_table_date) {
                                                    //leave
                                                    $User_attendance =  new User_attendance();
                                                    $User_attendance->date =  $dateYesterday;
                                                    $User_attendance->user_id =  $User->id;
                                                    $User_attendance->zkt_time_id = null;
                                                    $User_attendance->type = 'leave';
                                                    $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                                    $User_attendance->status = true;
                                                    $User_attendance->save();
                                                } else if ($Zk_time) {

                                                    if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                                        //late
                                                        $check->date =  $dateYesterday;
                                                        $check->user_id =  $User->id;
                                                        $check->zkt_time_id = $Zk_time->id;
                                                        $check->type = 'late';
                                                        $check->leave_table_id = null;
                                                        $check->status = true;
                                                        $check->save();
                                                    } else {
                                                        //normal
                                                        $check->date =  $dateYesterday;
                                                        $check->user_id =  $User->id;
                                                        $check->zkt_time_id = $Zk_time->id;
                                                        $check->type = 'normal';
                                                        $check->leave_table_id = null;
                                                        $check->status = true;
                                                        $check->save();
                                                    }
                                                } else {

                                                    //miss
                                                    $check->date =  $dateYesterday;
                                                    $check->user_id =  $User->id;
                                                    $check->zkt_time_id = null;
                                                    $check->type = 'miss';
                                                    $check->leave_table_id = null;
                                                    $check->status = true;
                                                    $check->save();
                                                }
                                            } else {
                                                //off
                                                $check->date =  $dateYesterday;
                                                $check->user_id =  $User->id;
                                                $check->zkt_time_id = null;
                                                $check->type = 'off';
                                                $check->leave_table_id = null;
                                                $check->status = true;
                                                $check->save();
                                            }
                                        } else {
                                            //off
                                            $check->date =  $dateYesterday;
                                            $check->user_id =  $User->id;
                                            $check->zkt_time_id = null;
                                            $check->type = 'off';
                                            $check->leave_table_id = null;
                                            $check->status = true;
                                            $check->save();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', []);
        } catch (\Throwable $e) {
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }


    public function ZkTimePage(Request $request)
    {
        $columns = $request->columns;
        $length = $request->length ?? 10;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start ?? 0;
        $page = ($start / $length) + 1;

        $login_branch_id = $request->login_branch_id;

        $date = $request->date;
        $user_id = $request->user_id;

        // คอลัมน์ที่จะ select
        $col = [
            'zkt_time.id',
            'zkt_time.personnel_id',
            'zkt_time.time',
            'zkt_time.area_name',
            'zkt_time.device_name',
            'zkt_time.pic',
            'zkt_time.latitude',
            'zkt_time.longitude',
            'users.id as user_id',
            'users.branch_id',
        ];

        // สร้าง query ด้วย LEFT JOIN
        $d = Zk_time::select($col)
            ->with('user')
            ->leftJoin('users', function ($join) {
                $join->on(
                    DB::raw('zkt_time.personnel_id COLLATE utf8mb4_unicode_ci'),
                    '=',
                    DB::raw('users.personnel_id COLLATE utf8mb4_unicode_ci')
                )->whereNull('users.deleted_at');
            });

        // เงื่อนไข branch
        if ($login_branch_id) {
            $d->where('users.branch_id', $login_branch_id);
        }

        if ($date) {
            $d->whereDate('zkt_time.time', $date);
        }

        if ($user_id) {
            $d->whereDate('users.id', $user_id);
        }

        // ค้นหา
        if (!empty($search['value'])) {
            $keyword = $search['value'];
            $d->where(function ($query) use ($columns, $keyword) {
                foreach ($columns as $colItem) {
                    if (!empty($colItem['data'])) {
                        $query->orWhere($colItem['data'], 'like', "%{$keyword}%");
                    }
                }
            });
        }

        // จัดเรียง
        if (!empty($order)) {
            $orderColumnIndex = $order[0]['column'] ?? 0;
            $orderDir = $order[0]['dir'] ?? 'desc';
            $orderColumn = $columns[$orderColumnIndex]['data'] ?? 'id';
            $d->orderBy($orderColumn, $orderDir);
        } else {
            $d->orderBy('zkt_time.id', 'desc');
        }

        // ทำการ paginate
        $d = $d->paginate($length, ['*'], 'page', $page);

        // เพิ่มเลขลำดับ (No)
        if ($d->isNotEmpty()) {
            $No = (($page - 1) * $length);
            foreach ($d as $row) {
                $No++;
                $row->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }
}
