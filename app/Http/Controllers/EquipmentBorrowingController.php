<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use App\Models\EquipmentBorrowing;
use App\Models\EquipmentBorrowingItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class EquipmentBorrowingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = EquipmentBorrowing::with(['user', 'branch', 'borrowingItems.equipment', 'approvedBy']);

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Filter by user
            if ($request->has('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            // Filter by branch
            if ($request->has('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }

            // Filter by date range
            if ($request->has('date_from')) {
                $query->whereDate('borrow_date', '>=', $request->date_from);
            }
            if ($request->has('date_to')) {
                $query->whereDate('borrow_date', '<=', $request->date_to);
            }

            // Filter overdue items
            if ($request->has('overdue') && $request->overdue == 'true') {
                $query->overdue();
            }

            // Search by borrowing code or user name
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('borrowing_code', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('first_name', 'like', "%{$search}%")
                                   ->orWhere('last_name', 'like', "%{$search}%");
                      });
                });
            }

            $perPage = $request->get('per_page', 15);
            $borrowings = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $borrowings,
                'message' => 'Borrowing list retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve borrowing list',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'branch_id' => 'required|exists:branch,id',
                'borrow_date' => 'required|date',
                'expected_return_date' => 'required|date|after:borrow_date',
                'purpose' => 'nullable|string',
                'notes' => 'nullable|string',
                'equipment_items' => 'required|array|min:1',
                'equipment_items.*.equipment_id' => 'required|exists:equipments,id',
                'equipment_items.*.quantity' => 'required|integer|min:1',
                'equipment_items.*.condition_before' => 'nullable|string',
                'equipment_items.*.notes' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Check equipment availability
            foreach ($request->equipment_items as $item) {
                $equipment = Equipment::find($item['equipment_id']);
                if (!$equipment || $equipment->status !== 'available') {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => "Equipment {$equipment->name} is not available for borrowing"
                    ], 400);
                }
            }

            // Create borrowing record
            $borrowing = new EquipmentBorrowing();
            $borrowing->borrowing_code = $borrowing->generateBorrowingCode();
            $borrowing->user_id = $request->user_id;
            $borrowing->branch_id = $request->branch_id;
            $borrowing->borrow_date = $request->borrow_date;
            $borrowing->expected_return_date = $request->expected_return_date;
            $borrowing->purpose = $request->purpose;
            $borrowing->notes = $request->notes;
            $borrowing->status = 'pending';
            $borrowing->create_by = auth()->user()->id ?? null;
            $borrowing->save();

            // Create borrowing items
            foreach ($request->equipment_items as $item) {
                EquipmentBorrowingItem::create([
                    'borrowing_id' => $borrowing->id,
                    'equipment_id' => $item['equipment_id'],
                    'quantity' => $item['quantity'],
                    'condition_before' => $item['condition_before'] ?? null,
                    'notes' => $item['notes'] ?? null
                ]);
            }

            DB::commit();

            $borrowing->load(['user', 'branch', 'borrowingItems.equipment']);

            return response()->json([
                'success' => true,
                'data' => $borrowing,
                'message' => 'Borrowing request created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create borrowing request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): JsonResponse
    {
        try {
            $borrowing = EquipmentBorrowing::with([
                'user',
                'branch',
                'borrowingItems.equipment.category',
                'approvedBy',
                'returnedBy'
            ])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $borrowing,
                'message' => 'Borrowing details retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Borrowing not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $borrowing = EquipmentBorrowing::findOrFail($id);

            // Only allow updates for pending status
            if ($borrowing->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only update pending borrowing requests'
                ], 400);
            }

            $validator = Validator::make($request->all(), [
                'expected_return_date' => 'required|date|after:borrow_date',
                'purpose' => 'nullable|string',
                'notes' => 'nullable|string',
                'equipment_items' => 'required|array|min:1',
                'equipment_items.*.equipment_id' => 'required|exists:equipments,id',
                'equipment_items.*.quantity' => 'required|integer|min:1',
                'equipment_items.*.condition_before' => 'nullable|string',
                'equipment_items.*.notes' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Check equipment availability (excluding current borrowing items)
            foreach ($request->equipment_items as $item) {
                $equipment = Equipment::find($item['equipment_id']);
                if (!$equipment || ($equipment->status !== 'available' &&
                    !$borrowing->borrowingItems->contains('equipment_id', $item['equipment_id']))) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => "Equipment {$equipment->name} is not available for borrowing"
                    ], 400);
                }
            }

            // Update borrowing record
            $borrowing->update([
                'expected_return_date' => $request->expected_return_date,
                'purpose' => $request->purpose,
                'notes' => $request->notes,
                'update_by' => auth()->user()->id ?? null
            ]);

            // Delete existing borrowing items and create new ones
            $borrowing->borrowingItems()->delete();

            foreach ($request->equipment_items as $item) {
                EquipmentBorrowingItem::create([
                    'borrowing_id' => $borrowing->id,
                    'equipment_id' => $item['equipment_id'],
                    'quantity' => $item['quantity'],
                    'condition_before' => $item['condition_before'] ?? null,
                    'notes' => $item['notes'] ?? null
                ]);
            }

            DB::commit();

            $borrowing->load(['user', 'branch', 'borrowingItems.equipment']);

            return response()->json([
                'success' => true,
                'data' => $borrowing,
                'message' => 'Borrowing request updated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update borrowing request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
            $borrowing = EquipmentBorrowing::findOrFail($id);

            // Only allow deletion for pending or cancelled status
            if (!in_array($borrowing->status, ['pending', 'cancelled'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only delete pending or cancelled borrowing requests'
                ], 400);
            }

            $borrowing->delete();

            return response()->json([
                'success' => true,
                'message' => 'Borrowing request deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete borrowing request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve a borrowing request
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve($id): JsonResponse
    {
        try {
            $borrowing = EquipmentBorrowing::findOrFail($id);

            if ($borrowing->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only approve pending borrowing requests'
                ], 400);
            }

            $borrowing->approve(auth()->user()->id ?? null);
            $borrowing->load(['user', 'branch', 'borrowingItems.equipment', 'approvedBy']);

            return response()->json([
                'success' => true,
                'data' => $borrowing,
                'message' => 'Borrowing request approved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve borrowing request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark borrowing as borrowed (equipment handed out)
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsBorrowed($id): JsonResponse
    {
        try {
            $borrowing = EquipmentBorrowing::findOrFail($id);

            if ($borrowing->status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only mark approved borrowing requests as borrowed'
                ], 400);
            }

            $borrowing->markAsBorrowed();
            $borrowing->load(['user', 'branch', 'borrowingItems.equipment']);

            return response()->json([
                'success' => true,
                'data' => $borrowing,
                'message' => 'Equipment marked as borrowed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark equipment as borrowed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Return borrowed equipment
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function returnEquipment(Request $request, $id): JsonResponse
    {
        try {
            $borrowing = EquipmentBorrowing::findOrFail($id);

            if ($borrowing->status !== 'borrowed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only return borrowed equipment'
                ], 400);
            }

            $validator = Validator::make($request->all(), [
                'return_notes' => 'nullable|string',
                'equipment_conditions' => 'nullable|array',
                'equipment_conditions.*.equipment_id' => 'required|exists:equipments,id',
                'equipment_conditions.*.condition_after' => 'required|string',
                'equipment_conditions.*.notes' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Update equipment conditions if provided
            if ($request->has('equipment_conditions')) {
                foreach ($request->equipment_conditions as $condition) {
                    $borrowingItem = $borrowing->borrowingItems()
                        ->where('equipment_id', $condition['equipment_id'])
                        ->first();

                    if ($borrowingItem) {
                        $borrowingItem->update([
                            'condition_after' => $condition['condition_after'],
                            'notes' => $condition['notes'] ?? $borrowingItem->notes
                        ]);
                    }
                }
            }

            $borrowing->returnEquipment(auth()->user()->id ?? null, $request->return_notes);

            DB::commit();

            $borrowing->load(['user', 'branch', 'borrowingItems.equipment', 'returnedBy']);

            return response()->json([
                'success' => true,
                'data' => $borrowing,
                'message' => 'Equipment returned successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to return equipment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a borrowing request
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id): JsonResponse
    {
        try {
            $borrowing = EquipmentBorrowing::findOrFail($id);

            if (!in_array($borrowing->status, ['pending', 'approved'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only cancel pending or approved borrowing requests'
                ], 400);
            }

            $borrowing->update([
                'status' => 'cancelled',
                'notes' => $borrowing->notes . "\n\nCancelled: " . ($request->reason ?? 'No reason provided'),
                'update_by' => auth()->user()->id ?? null
            ]);

            $borrowing->load(['user', 'branch', 'borrowingItems.equipment']);

            return response()->json([
                'success' => true,
                'data' => $borrowing,
                'message' => 'Borrowing request cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel borrowing request',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
