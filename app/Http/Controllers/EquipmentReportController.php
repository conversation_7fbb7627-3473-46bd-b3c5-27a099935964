<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use App\Models\EquipmentBorrowing;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EquipmentReportController extends Controller
{
    /**
     * Equipment status summary report
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function equipmentStatusSummary(Request $request): JsonResponse
    {
        try {
            $query = Equipment::query();

            // Filter by branch if provided
            if ($request->has('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }

            // Filter by category if provided
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            $statusSummary = $query->select('status', DB::raw('count(*) as count'))
                                  ->groupBy('status')
                                  ->get();

            $totalEquipment = $query->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'status_summary' => $statusSummary,
                    'total_equipment' => $totalEquipment
                ],
                'message' => 'Equipment status summary retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve equipment status summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Borrowing statistics report
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function borrowingStatistics(Request $request): JsonResponse
    {
        try {
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->toDateString());
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->toDateString());

            $query = EquipmentBorrowing::whereBetween('borrow_date', [$dateFrom, $dateTo]);

            // Filter by branch if provided
            if ($request->has('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }

            // Status summary
            $statusSummary = $query->select('status', DB::raw('count(*) as count'))
                                  ->groupBy('status')
                                  ->get();

            // Monthly borrowing trend
            $monthlyTrend = EquipmentBorrowing::select(
                DB::raw('YEAR(borrow_date) as year'),
                DB::raw('MONTH(borrow_date) as month'),
                DB::raw('count(*) as total_borrowings')
            )
            ->whereBetween('borrow_date', [
                Carbon::parse($dateFrom)->startOfYear()->toDateString(),
                $dateTo
            ])
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

            // Top borrowers
            $topBorrowers = EquipmentBorrowing::with('user')
                ->select('user_id', DB::raw('count(*) as borrowing_count'))
                ->whereBetween('borrow_date', [$dateFrom, $dateTo])
                ->groupBy('user_id')
                ->orderBy('borrowing_count', 'desc')
                ->limit(10)
                ->get();

            // Overdue items
            $overdueCount = EquipmentBorrowing::where('status', 'borrowed')
                ->where('expected_return_date', '<', Carbon::now()->toDateString())
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ],
                    'status_summary' => $statusSummary,
                    'monthly_trend' => $monthlyTrend,
                    'top_borrowers' => $topBorrowers,
                    'overdue_count' => $overdueCount
                ],
                'message' => 'Borrowing statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve borrowing statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Overdue equipment report
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function overdueEquipment(Request $request): JsonResponse
    {
        try {
            $query = EquipmentBorrowing::with(['user', 'branch', 'borrowingItems.equipment'])
                ->where('status', 'borrowed')
                ->where('expected_return_date', '<', Carbon::now()->toDateString());

            // Filter by branch if provided
            if ($request->has('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }

            // Sort by most overdue first
            $overdueItems = $query->orderBy('expected_return_date', 'asc')->get();

            // Add days overdue to each item
            $overdueItems->each(function ($item) {
                $item->days_overdue = Carbon::parse($item->expected_return_date)->diffInDays(Carbon::now());
            });

            return response()->json([
                'success' => true,
                'data' => $overdueItems,
                'message' => 'Overdue equipment report retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve overdue equipment report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Equipment utilization report
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function equipmentUtilization(Request $request): JsonResponse
    {
        try {
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->toDateString());
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->toDateString());

            $utilizationData = Equipment::with(['category', 'borrowingItems' => function($query) use ($dateFrom, $dateTo) {
                $query->whereHas('borrowing', function($borrowingQuery) use ($dateFrom, $dateTo) {
                    $borrowingQuery->whereBetween('borrow_date', [$dateFrom, $dateTo]);
                });
            }])
            ->withCount(['borrowingItems as total_borrowings' => function($query) use ($dateFrom, $dateTo) {
                $query->whereHas('borrowing', function($borrowingQuery) use ($dateFrom, $dateTo) {
                    $borrowingQuery->whereBetween('borrow_date', [$dateFrom, $dateTo]);
                });
            }])
            ->get();

            // Calculate utilization percentage
            $utilizationData->each(function ($equipment) {
                $equipment->utilization_rate = $equipment->total_borrowings > 0 ?
                    round(($equipment->total_borrowings / 30) * 100, 2) : 0; // Assuming 30 days max utilization
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ],
                    'equipment_utilization' => $utilizationData
                ],
                'message' => 'Equipment utilization report retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve equipment utilization report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * User borrowing history report
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function userBorrowingHistory(Request $request): JsonResponse
    {
        try {
            $userId = $request->get('user_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfYear()->toDateString());
            $dateTo = $request->get('date_to', Carbon::now()->endOfYear()->toDateString());

            $query = EquipmentBorrowing::with(['user', 'branch', 'borrowingItems.equipment.category'])
                ->whereBetween('borrow_date', [$dateFrom, $dateTo]);

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $borrowingHistory = $query->orderBy('borrow_date', 'desc')->get();

            // Calculate summary statistics
            $summary = [
                'total_borrowings' => $borrowingHistory->count(),
                'completed_borrowings' => $borrowingHistory->where('status', 'returned')->count(),
                'pending_borrowings' => $borrowingHistory->where('status', 'pending')->count(),
                'overdue_borrowings' => $borrowingHistory->where('status', 'overdue')->count(),
                'total_equipment_borrowed' => $borrowingHistory->sum(function($borrowing) {
                    return $borrowing->borrowingItems->sum('quantity');
                })
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ],
                    'summary' => $summary,
                    'borrowing_history' => $borrowingHistory
                ],
                'message' => 'User borrowing history retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve user borrowing history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Equipment maintenance report
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function equipmentMaintenance(Request $request): JsonResponse
    {
        try {
            $query = Equipment::with(['category', 'branch']);

            // Filter by branch if provided
            if ($request->has('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }

            // Filter by category if provided
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Get equipment that needs maintenance or is damaged
            $maintenanceEquipment = $query->whereIn('status', ['maintenance', 'damaged'])
                ->orderBy('updated_at', 'desc')
                ->get();

            // Get equipment by condition
            $conditionSummary = Equipment::select('condition', DB::raw('count(*) as count'))
                ->groupBy('condition')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'maintenance_equipment' => $maintenanceEquipment,
                    'condition_summary' => $conditionSummary
                ],
                'message' => 'Equipment maintenance report retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve equipment maintenance report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Dashboard summary for equipment management
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function dashboardSummary(): JsonResponse
    {
        try {
            // Equipment counts by status
            $equipmentCounts = Equipment::select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status');

            // Recent borrowings (last 7 days)
            $recentBorrowings = EquipmentBorrowing::with(['user', 'borrowingItems.equipment'])
                ->where('created_at', '>=', Carbon::now()->subDays(7))
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // Overdue count
            $overdueCount = EquipmentBorrowing::where('status', 'borrowed')
                ->where('expected_return_date', '<', Carbon::now()->toDateString())
                ->count();

            // Pending approvals
            $pendingApprovals = EquipmentBorrowing::where('status', 'pending')->count();

            // Equipment needing maintenance
            $maintenanceNeeded = Equipment::whereIn('status', ['maintenance', 'damaged'])->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'equipment_counts' => $equipmentCounts,
                    'recent_borrowings' => $recentBorrowings,
                    'overdue_count' => $overdueCount,
                    'pending_approvals' => $pendingApprovals,
                    'maintenance_needed' => $maintenanceNeeded
                ],
                'message' => 'Dashboard summary retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
