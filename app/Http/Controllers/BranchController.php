<?php

namespace App\Http\Controllers;

use App\Imports\BranchImport;
use App\Models\Branch;
use App\Models\Config;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class BranchController extends Controller
{

    public function getBranch(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        $branch = Branch::where('status', 1)->with('user_create');

        if ($login_company_id) {
            $branch->where('company_id', $login_company_id);
        }

        $Branch = $branch->get()
            ->toarray();

        if (!empty($Branch)) {

            for ($i = 0; $i < count($Branch); $i++) {
                $Branch[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $Branch);
    }

    public function BranchPage(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;


        $col = array('id', 'company_id', 'name', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Branch::select($col)->with('company')
            ->with('user_create');


        if ($login_company_id) {
            $d->where('company_id', $login_company_id);
        }

        $d->orderby($col[$order[0]['column']], $order[0]['dir']);
        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($request->company_id)) {
            return $this->returnErrorData('กรุณาระบุบริษัท', 404);
        } else if (!isset($request->name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อสาขาด้วย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $company_id = $request->company_id;
        $name = $request->name;

        $checkName = Branch::where('name', $name)
            ->where('company_id', $company_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $Branch = new Branch();
                $Branch->company_id = $company_id;
                $Branch->name = $name;
                $Branch->status = 1;

                $Branch->create_by = $loginBy->user_id;

                $Branch->save();

                //new config
                $Config = new Config();
                $Config->branch_id = $Branch->id;
                $Config->save();

                //log
                $userId = $loginBy->user_id;
                $type = 'Add Branch';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Branch = Branch::find($id);
        return $this->returnSuccess('Successful', $Branch);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }
        $company_id = $request->company_id;
        $name = $request->name;

        $checkName = Branch::where('id', '!=', $id)
            ->where('name', $name)
            ->where('company_id', $company_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $Branch = Branch::find($id);

                $Branch->name = $name;
                $Branch->company_id = $company_id;
                $Branch->status = $request->status;

                $Branch->update_by = $loginBy->user_id;
                $Branch->updated_at = Carbon::now()->toDateTimeString();

                $Branch->save();
                //log
                $userId = $loginBy->user_id;
                $type = 'Edit Branch';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $Branch->name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnUpdate('Successful operation');
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Branch = Branch::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Branch';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Branch->name;
            $this->Log($userId, $description, $type);
            //

            $Branch->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function ImportBranch(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('User information not found. Please login again', 404);
        }

        $file = request()->file('file');
        $fileName = $file->getClientOriginalName();

        $Data = Excel::toArray(new BranchImport(), $file);
        $data = $Data[0];

        if (count($data) > 0) {

            $insert_data = [];

            for ($i = 0; $i < count($data); $i++) {

                $name = trim($data[$i]['name']);

                $row = $i + 2;

                if ($name == '') {
                    return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
                }

                //check row sample
                if ($name == 'SIMPLE-000') {
                    //
                } else {

                    // //check name
                    // $Branch = Branch::where('name', $name)->first();
                    // if ($Branch) {
                    //     return $this->returnErrorData('Branch ' . $name . ' was information information is already in the system', 404);
                    // }

                    //check dupicate data form file import
                    for ($j = 0; $j < count($insert_data); $j++) {

                        if ($name == $insert_data[$j]['name']) {
                            return $this->returnErrorData('Branch ' . $name . ' There is duplicate data in the import file', 404);
                        }
                    }
                    ///

                    $insert_data[] = array(
                        'name' => $name,
                        'status' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    );
                }
            }

            if (!empty($insert_data)) {

                DB::beginTransaction();

                try {

                    //updateOrInsert
                    for ($i = 0; $i < count($insert_data); $i++) {

                        DB::table('branch')
                            ->updateOrInsert(
                                [
                                    'id' => trim($data[$i]['id']), //id
                                ],
                                $insert_data[$i]
                            );
                    }
                    //

                    DB::commit();

                    //log
                    $userId = $loginBy->user_id;
                    $type = 'Import Branch';
                    $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
                    $this->Log($userId, $description, $type);
                    //

                    DB::commit();

                    return $this->returnSuccess('Successful operation', []);
                } catch (\Throwable $e) {

                    DB::rollback();

                    return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
                }
            } else {
                return $this->returnErrorData('Data Not Found', 404);
            }
        } else {
            return $this->returnErrorData('Data Not Found', 404);
        }
    }
}
