<?php

namespace App\Http\Controllers;

use App\Models\Loan;
use App\Exports\SocialExport;
use App\Imports\PayrollImport;
use App\Models\BondLog;
use App\Models\DeductPaid;
use App\Models\DeductType;
use App\Models\IncomePaid;
use App\Models\Payroll;
use App\Models\PayrollRound;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\IncomeType;
use App\Models\EmployeeDeposit;
use App\Models\Holiday;
use App\Models\LoanPayment;
use App\Models\LoanSchedule;
use App\Models\LoanScheduleStatus;
use App\Models\PayrollContribution;
use App\Models\PayrollContributionSetting;
use Carbon\Carbon;
use DateTime;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

class PayrollController extends Controller
{

    public function getPayrollRound(Request $request)
    {

        $Payroll = PayrollRound::where('status', 1)
            ->get()
            ->toarray();

        if (!empty($Payroll)) {

            for ($i = 0; $i < count($Payroll); $i++) {
                $Payroll[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $Payroll);
    }


    public function getPayroll(Request $request)
    {

        $Payroll = Payroll::with('payrollRound')
            ->with('user.department')
            ->with('user.position')
            ->with('user.branch')
            ->with('incomePaids.income_type')
            ->with('deductPaids.deduct_type')
            ->where('status', 1)
            ->get()
            ->toarray();

        if (!empty($Payroll)) {

            for ($i = 0; $i < count($Payroll); $i++) {
                $Payroll[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $Payroll);
    }

    public function PayrollPage(Request $request)
    {
        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $round = $request->round;
        $year = $request->year;
        $month = $request->month;
        $user_id = $request->user_id;

        $col = array('id', 'user_id', 'round', 'month', 'pay_status', 'salary', 'ot', 'salary_withdraw', 'sso_total', 'pvd_total', 'total_income', 'total_deduct', 'total', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Payroll::select($col)
            ->with('user.department')
            ->with('user.position')
            ->with('user.branch')
            ->with('payrollRound')
            ->with('incomePaids.income_type')
            ->with('deductPaids.deduct_type')
            ->orderby($col[$order[0]['column']], $order[0]['dir']);

        if (isset($user_id)) {
            $d->where('user_id', $user_id);
        }

        // if ($login_branch_id) {
        //     $d->whereHas('user', function ($q) use ($login_branch_id) {
        //         $q->where('branch_id', $login_branch_id);
        //     });
        // }

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        if (isset($round)) {
            $d->where('round', $round);
        }

        if (isset($year)) {
            $d->where('year', $year);
        }

        if (isset($month)) {
            $d->where('month', $month);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    public function deductHoliday($Payroll, $branch_id, $user_id, $round, $year, $month, $loginBy)
    {
        $total_deduct = 0;

        $getUser = User::where('id', $user_id)->first();
        if ($getUser->register_date && $getUser->work_type == 'month') {

            $getPayrollRound =  PayrollRound::where('round', $round)->first();

            $start_day = $getPayrollRound->start_day;
            $end_day = $getPayrollRound->end_day;

            $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
            $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

            $dateDiff = $this->dateDiff($date_end, $getUser->register_date);
            $daysInYear = Carbon::createFromDate($year)->daysInYear;

            if ($dateDiff < $daysInYear) {

                $getid_deductTypeHoliday = DeductType::firstOrCreate(
                    [
                        'name' => 'เงินหักวันลาพักร้อน',
                        'branch_id' => $branch_id
                    ], // หา
                    [                             // สร้าง ถ้าไม่เจอ
                        'description' => 'ถ้าเป็นพนักงานเงินเดือนแต่ทำงานไม่ถึง 1 ปี ต้องหักเงินวันลาพักร้อนที่บริษัทกำหนด',
                        'view_in_slip' => false,
                        'type' => 'Once'
                    ]
                );

                $branch_id_user = $branch_id;

                $holidays = Holiday::where('type', 'off')
                    ->where('status', 1)
                    ->whereBetween('date', [$date_start, $date_end])
                    ->when($branch_id_user, function ($query, $branch_id_user) {
                        return $query->where(function ($q) use ($branch_id_user) {
                            $q->whereNull('branch_id')
                                ->orWhere('branch_id', $branch_id_user);
                        });
                    })
                    ->pluck('date');

                $count_holidays = count($holidays);

                $salary_per_day = $getUser->salary / 30; // คิดจากเงินเดือนหาร 30 วัน
                $deduct_leave_holiday = $count_holidays * $salary_per_day;

                if ($deduct_leave_holiday > 0) {

                    $DeductPaid_Holiday = new DeductPaid();
                    $DeductPaid_Holiday->payroll_id = $Payroll->id;
                    $DeductPaid_Holiday->user_id = $getUser->id;
                    $DeductPaid_Holiday->deduct_type_id = $getid_deductTypeHoliday->id; // เงินหักวันลาพักร้อน
                    $DeductPaid_Holiday->year =  $year;
                    $DeductPaid_Holiday->month =  $month;
                    $DeductPaid_Holiday->price = $deduct_leave_holiday;
                    $DeductPaid_Holiday->description =   $getid_deductTypeHoliday->description;
                    $DeductPaid_Holiday->create_by = $loginBy->user_id;

                    $DeductPaid_Holiday->save();

                    $total_deduct  += $DeductPaid_Holiday->price;
                }
            }
        }

        return $total_deduct;
    }

    public function calculateSalary($User, $date_start, $date_end, $day_type)
    {
        //salary
        $salaryUser = $User->salary;
        $branch_id_user = $User->branch_id;

        //จำนวนวันทำงาน
        $calculatePerday = User_attendance::where('user_id', $User->id)
            ->whereIn('type', ['normal', 'late'])
            ->whereBetween('date', [$date_start, $date_end])
            ->count();

        //วันหยุดหักเงิน
        $holidays = Holiday::where('type', 'off')
            ->where('status', 1)
            ->whereBetween('date', [$date_start, $date_end])
            ->when($branch_id_user, function ($query, $branch_id_user) {
                return $query->where(function ($q) use ($branch_id_user) {
                    $q->whereNull('branch_id')
                        ->orWhere('branch_id', $branch_id_user);
                });
            })
            ->pluck('date');

        $count_holidays = count($holidays);

        $balance_day_work =  $calculatePerday - $count_holidays;

        if ($User->work_type == 'day') {
            //คำนวน กรณีรายวัน
            $salaryUser = $salaryUser * $balance_day_work;
        } else {
            if ($day_type == 'full') {
                $salaryUser = $salaryUser;
            } else {
                $salaryUser = $salaryUser / count(PayrollRound::get());
            }
        }

        return $salaryUser;
    }

    public  function calculateSocialSecurity($code, $wage)
    {
        // ดึง setting ล่าสุดที่ยัง active
        $setting = PayrollContributionSetting::where('status', 1)
            ->where('code', $code)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$setting) {
            throw new \Exception("ไม่พบการตั้งค่าอัตราประกันสังคม");
        }

        // กำหนดฐานค่าจ้างตามเพดานขั้นต่ำ–สูงสุด
        $wageBase = $wage;
        if ($setting->max_wage_calculate_status == true) {
            if ($wage < $setting->min_wage_base) {
                $wageBase = $setting->min_wage_base;
            } elseif ($wage > $setting->max_wage_base) {
                $wageBase = $setting->max_wage_base;
            }
        }

        // คำนวณเงินสมทบ
        $employeeContrib = min(round(($wageBase * $setting->employee_rate) / 100, 2), 750.00);
        $employerContrib = min(round(($wageBase * $setting->employer_rate) / 100, 2), 750.00);

        return [
            'wage_base'        => $wageBase,
            'employee_contrib' => $employeeContrib,
            'employer_contrib' => $employerContrib,
            'setting'          => $setting
        ];
    }

    public function addPayrollContribution($pay_status, $code, $Payroll, $User, $year, $month, $loginBy)
    {
        $data = [
            'code_contribution' => null,
            'employer_total' => 0.00,
            'employee_total' => 0.00,
        ];

        if ($pay_status == true) {

            // ประกันสังคม และเงินสมทบ
            $date_start_full = Carbon::create($year, $month, 1)->toDateString();         // วันที่ 1 ของเดือน
            $date_end_full   = Carbon::create($year, $month)->endOfMonth()->toDateString(); // วันสุดท้ายของเดือน

            $salaryUserFull = $this->calculateSalary($User, $date_start_full, $date_end_full, 'full');

            // ประกันสังคม
            $contribution       = $this->calculateSocialSecurity($code, $salaryUserFull);
            $wage_base          = $contribution['wage_base']; // ฐานค่าจ้าง
            $employee_contrib   = $contribution['employee_contrib']; // หักพนักงาน
            $employer_contrib   = $contribution['employer_contrib']; // สมทบนายจ้าง
            $setting            = $contribution['setting'];

            // ตรวจสอบว่ามี record เดิมอยู่หรือไม่
            $PayrollContribution = PayrollContribution::where('code_contribution', $code)
                ->where('payroll_id', $Payroll->id)
                ->where('user_id', $User->id)
                ->first();

            if (!$PayrollContribution) {
                $PayrollContribution = new PayrollContribution();
                $PayrollContribution->code_contribution = $code;
                $PayrollContribution->payroll_id        = $Payroll->id;
                $PayrollContribution->user_id           = $User->id;
                $PayrollContribution->create_by         = $loginBy->user_id;
            } else {
                $PayrollContribution->update_by         = $loginBy->user_id;
            }

            // อัพเดทข้อมูลหลัก
            $PayrollContribution->employer_rate   = $setting->employer_rate;
            $PayrollContribution->employee_rate   = $setting->employee_rate;
            $PayrollContribution->wage_base       = $wage_base;
            $PayrollContribution->employer_total  = $employer_contrib;
            $PayrollContribution->employee_total  = $employee_contrib;
            $PayrollContribution->status          = 1;
            $PayrollContribution->save();

            $data = [
                'code_contribution' => $PayrollContribution->code_contribution,
                'employer_total' => $PayrollContribution->employer_total,
                'employee_total' => $PayrollContribution->employee_total,
            ];
        }

        return $data;
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($request->round)) {
            return $this->returnErrorData('กรุณาใส่รอบเงินเดือนด้วย', 404);
        } else if (!isset($request->year)) {
            return $this->returnErrorData('กรุณาใส่ปีด้วย', 404);
        } else if (!isset($request->month)) {
            return $this->returnErrorData('กรุณาใส่เดือนด้วย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $round = $request->round;
        $year = $request->year;
        $month = $request->month;

        $checkName = Payroll::where('round', $round)
            ->where('year', $year)
            ->where('month', $month)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('มีการสร้างรอบเงินเดือนนี้ในระบบแล้ว', 404);
        } else {

            DB::beginTransaction();

            try {

                $User = User::with('userIncomes.income_type')
                    ->with('userDeducts.deduct_type')
                    ->where('payroll_status', 1)
                    ->where('status', 1)
                    ->where('branch_id', $login_branch_id)
                    ->get();

                for ($i = 0; $i < count($User); $i++) {
                    $this->createPayrollByUser($User[$i], $round, $year, $month, $login_branch_id, $loginBy);
                }

                //log
                $userId = $loginBy->user_id;
                $type = 'สร้างรอบเงินเดือน';
                $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $year . '-' . $month . ' ' . $round;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Payroll = Payroll::with('payrollRound')
            ->with('user.department')
            ->with('user.position')
            ->with('user.branch')
            ->with('incomePaids.income_type')
            ->with('deductPaids.deduct_type')
            ->find($id);
        return $this->returnSuccess('Successful', $Payroll);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        $income_paids = $request->income_paids;
        $deduct_paids = $request->deduct_paids;

        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('กรุณาใส่ id', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }


        DB::beginTransaction();

        try {

            $Payroll = Payroll::with('user')
                ->with('incomePaids.income_type')
                ->with('deductPaids.deduct_type')
                ->find($id);

            if (!$Payroll) {
                return $this->returnErrorData('ไม่พบข้อมูล', 404);
            }


            //income
            $total_income = 0;

            //del
            for ($i = 0; $i < count($Payroll->incomePaids); $i++) {
                $Payroll->incomePaids[$i]->delete();
            }

            //add
            for ($i = 0; $i < count($income_paids); $i++) {

                $IncomePaid = new IncomePaid();
                $IncomePaid->payroll_id = $Payroll->id;
                $IncomePaid->user_id = $Payroll->user_id;
                $IncomePaid->income_type_id = $income_paids[$i]['income_type_id'];
                $IncomePaid->year =  $Payroll->year;
                $IncomePaid->month =   $Payroll->month;
                $IncomePaid->price =  $income_paids[$i]['price'];
                $IncomePaid->description =   $income_paids[$i]['description'];

                $IncomePaid->create_by = $loginBy->user_id;

                $IncomePaid->save();

                $total_income  += $IncomePaid->price;
            }


            //deduct
            $total_deduct = 0;

            //del
            for ($i = 0; $i < count($Payroll->deductPaids); $i++) {
                $Payroll->deductPaids[$i]->delete();
            }

            //add
            for ($i = 0; $i < count($deduct_paids); $i++) {

                $DeductPaid = new DeductPaid();
                $DeductPaid->payroll_id = $Payroll->id;
                $DeductPaid->user_id =  $Payroll->user_id;
                $DeductPaid->deduct_type_id = $deduct_paids[$i]['deduct_type_id'];
                $DeductPaid->year =  $Payroll->year;
                $DeductPaid->month =  $Payroll->month;
                $DeductPaid->price = $deduct_paids[$i]['price'];
                $DeductPaid->description =   $deduct_paids[$i]['description'];
                $DeductPaid->create_by = $loginBy->user_id;

                $DeductPaid->save();

                $total_deduct  += $DeductPaid->price;
            }

            //

            $salary = $request->salary;
            $total_ot = $request->total_ot;
            $total_withdraw_salary = $request->total_withdraw_salary;

            $Payroll->salary = $salary;
            $Payroll->ot = $total_ot;
            $Payroll->salary_withdraw = $total_withdraw_salary;
            $Payroll->total_income = $total_income;
            $Payroll->total_deduct = $total_deduct;
            $Payroll->total =  ($Payroll->salary +  $Payroll->ot + $Payroll->total_income) - ($Payroll->total_deduct + $Payroll->salary_withdraw);
            $Payroll->save();


            //log
            $userId = $loginBy->user_id;
            $type = 'Update Payroll';
            $description = 'User ' . $userId . ' has ' . $type . ' ' .  $Payroll->year . '-' .  $Payroll->month . ' ' .  $Payroll->round;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    public function process(Request $request, $id)
    {
        // $income_paids = $request->income_paids;
        // $deduct_paids = $request->deduct_paids;

        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('กรุณาใส่ id', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }


        DB::beginTransaction();

        try {

            $Payroll = Payroll::with('user.userIncomes.income_type')
                ->with('user.userDeducts.deduct_type')
                ->with('incomePaids.income_type')
                ->with('deductPaids.deduct_type')
                ->find($id);

            if (!$Payroll) {
                return $this->returnErrorData('ไม่พบข้อมูล', 404);
            }



            //เงินได้และเงินหัก
            if ($Payroll->round == 2) {
                //////////////////////////////////// income  /////////////////////////////////
                $total_income = 0;
                //del
                for ($i = 0; $i < count($Payroll->incomePaids); $i++) {
                    $Payroll->incomePaids[$i]->delete();
                }

                //add
                $income_paids = $Payroll->user->userIncomes;

                for ($i = 0; $i < count($income_paids); $i++) {

                    $IncomePaid = new IncomePaid();
                    $IncomePaid->payroll_id = $Payroll->id;
                    $IncomePaid->user_id = $Payroll->user_id;
                    $IncomePaid->income_type_id = $income_paids[$i]['income_types_id'];
                    $IncomePaid->year =  $Payroll->year;
                    $IncomePaid->month =   $Payroll->month;
                    $IncomePaid->price =  $income_paids[$i]['price'];
                    $IncomePaid->description = $income_paids[$i]['income_type']['name'];

                    $IncomePaid->create_by = $loginBy->user_id;
                    $IncomePaid->save();

                    $total_income  += $IncomePaid->price;
                }


                // ----- เช็คว่า user มียอดถอนเงิน ใน เดือนนี้ ปีนี้หรือป่าว  ----
                $withdraw = EmployeeDeposit::where('user_id', $Payroll->user_id)
                    ->whereYear('date', $Payroll->year)
                    ->whereMonth('date', $Payroll->month)
                    ->where('type', 'withdraw')
                    ->whereIN('payment_status', ['pending', 'paid']) // จ่ายไปแล้วรอบนึง
                    ->orderBy('id', 'desc') // ดึงตัวล่าสุด
                    ->first();


                if ($withdraw) {

                    $getid_incomeType = IncomeType::firstOrCreate(
                        [
                            'name' => 'ถอนงินฝากพนักงาน',
                            'branch_id' => $Payroll->user->branch_id
                        ], // หา
                        [                             // สร้าง ถ้าไม่เจอ
                            'description' => 'ถอนงินฝากสะสมของพนักงงาน',
                            'view_in_slip' => false,
                            'type' => 'Once'
                        ]
                    );

                    $IncomePaid = new IncomePaid();
                    $IncomePaid->payroll_id = $Payroll->id;
                    $IncomePaid->user_id = $Payroll->user_id;
                    $IncomePaid->income_type_id = $getid_incomeType->id;
                    $IncomePaid->year =  $Payroll->year;
                    $IncomePaid->month =  $Payroll->month;
                    $IncomePaid->price = $withdraw->amount;
                    $IncomePaid->description =  $getid_incomeType->name;
                    $IncomePaid->create_by = $loginBy->user_id;
                    $IncomePaid->save();

                    $total_income  += $IncomePaid->price;

                    $withdraw->payment_status = 'paid';
                    $withdraw->save();
                }



                /////////////////////////////////////////////////////////////////////////////



                //////////////////////////////////// deduct  /////////////////////////////////
                $total_deduct = 0;


                // // del log
                // BondLog::where('user_id', $Payroll->user_id)
                //     ->where('year', $Payroll->year)
                //     ->where('month', $Payroll->month)
                //     ->where('type', 'deduct')
                //     ->delete();

                //del
                for ($i = 0; $i < count($Payroll->deductPaids); $i++) {
                    $Payroll->deductPaids[$i]->delete();
                }


                //add
                $deduct_paids = $Payroll->user->userDeducts;

                for ($i = 0; $i < count($deduct_paids); $i++) {

                    $DeductPaid = new DeductPaid();
                    $DeductPaid->payroll_id = $Payroll->id;
                    $DeductPaid->user_id =  $Payroll->user_id;
                    $DeductPaid->deduct_type_id = $deduct_paids[$i]['deduct_type_id'];
                    $DeductPaid->year =  $Payroll->year;
                    $DeductPaid->month =  $Payroll->month;
                    $DeductPaid->price = $deduct_paids[$i]['price'];
                    $DeductPaid->description = $deduct_paids[$i]['deduct_type']['name'];
                    $DeductPaid->create_by = $loginBy->user_id;

                    $DeductPaid->save();


                    // เงินฝากพนักงานประจำเดือน
                    $getid_deductType = DeductType::firstOrCreate(
                        [
                            'name' => 'เงินฝากพนักงาน',
                            'branch_id' => $Payroll->user->branch_id
                        ], // หา
                        [                             // สร้าง ถ้าไม่เจอ
                            'description' => 'เงินสะสมรายเดือนที่หักจากเงินเดือนเพื่อออม',
                            'view_in_slip' => false,
                            'type' => 'Once'
                        ]
                    );

                    $getDeduct = $Payroll->user->userDeducts
                        ->firstWhere('deduct_type_id', $getid_deductType->id);

                    if ($getDeduct) {

                        // เช็คว่า มีฝากในเดือน-ปี นี้ยัง
                        $deposit = EmployeeDeposit::where('user_id', $Payroll->user_id)
                            ->whereYear('date', $Payroll->year)
                            ->whereMonth('date', $Payroll->month)
                            ->where('type', 'deposit')
                            ->orderBy('id', 'desc') // ดึงตัวล่าสุด
                            ->first();

                        if ($deposit) {
                            // อัปเดตข้อมูล
                            $newAmount = $getDeduct->price;
                            $diff = $newAmount - $deposit->amount;  // คำนวณค่าต่าง

                            $deposit->amount = $newAmount;
                            $deposit->total_amount += $diff;        // ปรับยอดรวมตามส่วนต่าง
                            $deposit->description = "ระบบฝากเงินสะสมรายเดือน88";
                            $deposit->payment_status = 'paid';
                            $deposit->save();
                        }

                        $total_deduct  += $DeductPaid->price;
                    }
                }

                //////////////////////////////////// เงินหักลาพักร้อน //////////////////////////////////////

                $round = $Payroll->round;
                $year = $Payroll->year;
                $month = $Payroll->month;
                $user_id = $Payroll->user_id;
                $branch_id = $Payroll->user->branch_id;

                $total_deduct_holiday =  $this->deductHoliday($Payroll, $branch_id, $user_id, $round, $year, $month, $loginBy);
                $total_deduct  += $total_deduct_holiday;
                ///////////////////////////////////////////////////////////////////////////////////////


                // รอบ 2 จ่ายเบี้ยขยัน เงินประกัน


                // เช้คเบี้ยขยัน
                $bonusAmount = $this->checkBonusStep($Payroll->id, $Payroll->user_id, $Payroll->user->branch_id, $Payroll->year, $Payroll->month, $loginBy->user_id);

                // จ่ายเงินประกันการทำงาน
                $refunded = BondLog::where('user_id', $Payroll->user_id)
                    ->where('year', $Payroll->year)
                    ->where('month', $Payroll->month)
                    ->where('type', 'refund')
                    ->first();

                if ($refunded) {
                    // ถ้าเคยคืนเงินแล้ว ไม่ต้องจ่ายเงินประกัน
                    $getid_type = IncomeType::where('name', 'เงินประกัน')->first();

                    $IncomePaid = new IncomePaid();
                    $IncomePaid->payroll_id = $Payroll->id;
                    $IncomePaid->user_id = $Payroll->user_id;
                    $IncomePaid->income_type_id = $getid_type->id;
                    $IncomePaid->year =  $Payroll->year;
                    $IncomePaid->month =  $Payroll->month;
                    $IncomePaid->price = $refunded->total_amount;
                    $IncomePaid->description =  $getid_type->name;

                    $IncomePaid->create_by = $loginBy->user_id;
                    $IncomePaid->save();

                    $total_income += $IncomePaid->price;
                } else {
                    // ถ้ายังไม่เคยคืนเงิน ให้จ่ายเงินประกัน
                    $bondAmount = $this->checkdeductBond($Payroll->id, $Payroll->user_id, $Payroll->user->branch_id, $Payroll->year, $Payroll->month, $loginBy->user_id);
                }


                // บวกเพิ่มเข้า total
                if ($bonusAmount > 0) {
                    $total_income += $bonusAmount;
                }

                $type = $bondAmount['type'] ?? null;

                if ($type === 'income') {
                    $total_income += $bondAmount['amount'];
                } elseif ($type === 'deduct') {
                    $total_deduct += $bondAmount['amount'];
                }
            }

            //

            $round =  $Payroll->round;
            $year =  $Payroll->year;
            $month =  $Payroll->month;

            $date_start = date('Y-m-01', strtotime("$year-$month-01"));
            $date_end = date('Y-m-t', strtotime("$year-$month-01"));

            if ($round) {
                $PayrollRound =  PayrollRound::where('round', $round)->first();

                $start_day = $PayrollRound->start_day;
                $end_day = $PayrollRound->end_day;

                $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
                $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

            }

            //salary
            $salaryUser = $this->calculateSalary($Payroll->user, $date_start, $date_end, 'haft');

            //ot
            $total_ot = $this->getOtUser($Payroll->user_id,  $date_start, $date_end);

            //withdraw salary
            $total_withdraw_salary  = 0.00;
            if ($Payroll->round == 2) {
                $total_withdraw_salary = $this->getWithdrawSalaryUser($Payroll->user_id,  $date_start, $date_end);
            }

            // คำนวนหักเงินกู้
            $total_loan = $this->calculateLoan($Payroll->user_id, $year, $month, $round);
            $total_deduct += $total_loan;

            //sso and pvd
            $sso_total = 0.00;
            $sso_employer_total = 0.00;

            $pvd_total = 0.00;
            $pvd_employer_total = 0.00;

            if ($round == 2) {
                $sso_status =  $Payroll->user->sso_status;
                $pvd_status =  $Payroll->user->pvd_status;

                //sso
                $total_contribution_sso = $this->addPayrollContribution($sso_status, 'sso', $Payroll, $Payroll->user, $year, $month, $loginBy);
                $sso_code_contribution = $total_contribution_sso['code_contribution'];

                $sso_total = $total_contribution_sso['employee_total'];
                $sso_employer_total = $total_contribution_sso['employer_total'];


                //pvd
                $total_contribution_sso = $this->addPayrollContribution($pvd_status, 'pvd', $Payroll, $Payroll->user, $year, $month, $loginBy);
                $pvd_code_contribution = $total_contribution_sso['code_contribution'];

                $pvd_total = $total_contribution_sso['employee_total'];
                $pvd_employer_total = $total_contribution_sso['employer_total'];
            }

            $Payroll->sso_total =  $sso_total;
            $Payroll->sso_employer_total = $sso_employer_total;
            $Payroll->pvd_total =  $pvd_total;
            $Payroll->pvd_employer_total = $pvd_employer_total;
            //


            $Payroll->salary = $salaryUser;
            $Payroll->ot = $total_ot;
            $Payroll->salary_withdraw = $total_withdraw_salary;
            $Payroll->total_income = $total_income;
            $Payroll->total_deduct = $total_deduct;
            $Payroll->total =  ($Payroll->salary +  $Payroll->ot + $Payroll->total_income) - ($Payroll->total_deduct + $Payroll->salary_withdraw + $Payroll->sso_total + $Payroll->pvd_total);
            $Payroll->save();



            //log
            $userId = $loginBy->user_id;
            $type = 'ดึงข้อมูลเงินเดือนใหม่';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ของพนักงาน ' . $Payroll->user->user_id .  $Payroll->year . '-' .  $Payroll->month . ' ' .  $Payroll->round;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    public function processAll(Request $request)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($request->round)) {
            return $this->returnErrorData('กรุณาใส่รอบเงินเดือนด้วย', 404);
        } else if (!isset($request->year)) {
            return $this->returnErrorData('กรุณาใส่ปีด้วย', 404);
        } else if (!isset($request->month)) {
            return $this->returnErrorData('กรุณาใส่เดือนด้วย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $round = $request->round;
        $year = $request->year;
        $month = $request->month;

        $checkName = Payroll::with('user.userIncomes.income_type')
            ->with('user.userDeducts.deduct_type')
            ->with('incomePaids')
            ->with('deductPaids')
            ->where('round', $round)
            ->where('year', $year)
            ->where('month', $month)
            ->where('branch_id', $login_branch_id)
            ->first();



        if ($checkName) {

            DB::beginTransaction();

            try {

                $delPayroll = Payroll::with('incomePaids')
                    ->with('deductPaids')
                    ->where('round', $round)
                    ->where('year', $year)
                    ->where('month', $month)
                    ->where('branch_id', $login_branch_id)
                    ->get();


                //del all
                for ($i = 0; $i < count($delPayroll); $i++) {

                    // del log
                    BondLog::where('user_id', $delPayroll[$i]->user_id)
                        ->where('year', $year)
                        ->where('month', $month)
                        ->where('type', 'deduct')
                        ->delete();


                    for ($j = 0; $j < count($delPayroll[$i]->incomePaids); $j++) {
                        $delPayroll[$i]->incomePaids[$j]->delete();
                    }

                    for ($j = 0; $j < count($delPayroll[$i]->deductPaids); $j++) {
                        $delPayroll[$i]->deductPaids[$j]->delete();
                    }
                    $delPayroll[$i]->delete();
                }
                //


                //add
                $User = User::with('userIncomes.income_type')
                    ->with('userDeducts.deduct_type')
                    ->where('payroll_status', 1)
                    ->where('status', 1)
                    ->where('branch_id', $login_branch_id)
                    ->get();

                for ($i = 0; $i < count($User); $i++) {

                    $Payroll = new Payroll();
                    $Payroll->branch_id = $login_branch_id;
                    $Payroll->user_id = $User[$i]->id;
                    $Payroll->round = $round;
                    $Payroll->year = $year;
                    $Payroll->month = $month;
                    $Payroll->pay_status = false;

                    $Payroll->status = 1;

                    $Payroll->create_by = $loginBy->user_id;

                    $Payroll->save();



                    $total_income = 0;
                    $total_deduct = 0;


                    //เงินได้และเงินหัก
                    if ($round == 2) {

                        //////////////////////////////// income ////////////////////////////////
                        for ($j = 0; $j < count($User[$i]->userIncomes); $j++) {
                            $IncomePaid = new IncomePaid();
                            $IncomePaid->payroll_id = $Payroll->id;
                            $IncomePaid->user_id = $User[$i]->id;
                            $IncomePaid->income_type_id = $User[$i]->userIncomes[$j]->income_types_id;
                            $IncomePaid->year =  $year;
                            $IncomePaid->month =  $month;
                            $IncomePaid->price = $User[$i]->userIncomes[$j]->price;
                            $IncomePaid->description =  $User[$i]->userIncomes[$j]->income_type->name;

                            $IncomePaid->create_by = $loginBy->user_id;

                            $IncomePaid->save();

                            $total_income  += $IncomePaid->price;
                        }

                        // ----- เช็คว่า user มียอดถอนเงิน ใน เดือนนี้ ปีนี้หรือป่าว ----
                        $withdraw = EmployeeDeposit::where('user_id', $User[$i]->id)
                            ->whereYear('date', $Payroll->year)
                            ->whereMonth('date', $Payroll->month)
                            ->where('type', 'withdraw')
                            ->whereIN('payment_status', ['pending', 'paid']) // จ่ายไปแล้วรอบนึง
                            ->orderBy('id', 'desc') // ดึงตัวล่าสุด
                            ->first();

                        // dd($withdraw);
                        if ($withdraw) {
                            $getid_incomeType = IncomeType::firstOrCreate(
                                [
                                    'name' => 'ถอนงินฝากพนักงาน',
                                    'branch_id' => $Payroll->user->branch_id
                                ], // หา
                                [                             // สร้าง ถ้าไม่เจอ
                                    'description' => 'ถอนงินฝากสะสมของพนักงงาน',
                                    'view_in_slip' => false,
                                    'type' => 'Once'
                                ]
                            );


                            $IncomePaid = new IncomePaid();
                            $IncomePaid->payroll_id = $Payroll->id;
                            $IncomePaid->user_id = $Payroll->user_id;
                            $IncomePaid->income_type_id = $getid_incomeType->id;
                            $IncomePaid->year =  $Payroll->year;
                            $IncomePaid->month =  $Payroll->month;
                            $IncomePaid->price = $withdraw->amount;
                            $IncomePaid->description =  $getid_incomeType->name;
                            $IncomePaid->create_by = $loginBy->user_id;
                            $IncomePaid->save();

                            $total_income  += $IncomePaid->price;

                            $withdraw->payment_status = 'paid';
                            $withdraw->save();
                        }


                        ///////////////////////////////////////////////////////////////////////////


                        //////////////////////////////////// deduct //////////////////////////////////////

                        for ($j = 0; $j < count($User[$i]->userDeducts); $j++) {

                            $DeductPaid = new DeductPaid();
                            $DeductPaid->payroll_id = $Payroll->id;
                            $DeductPaid->user_id = $User[$i]->id;
                            $DeductPaid->deduct_type_id = $User[$i]->userDeducts[$j]->deduct_type_id;
                            $DeductPaid->year =  $year;
                            $DeductPaid->month =  $month;
                            $DeductPaid->price = $User[$i]->userDeducts[$j]->price;
                            $DeductPaid->description =  $User[$i]->userDeducts[$j]->deduct_type->name;
                            $DeductPaid->create_by = $loginBy->user_id;

                            $DeductPaid->save();

                            $total_deduct  += $DeductPaid->price;


                            // เงินฝากพนักงานประจำเดือน
                            $getid_deductType = DeductType::firstOrCreate(
                                [
                                    'name' => 'เงินฝากพนักงาน',
                                    'branch_id' => $User[$i]->branch_id
                                ], // หา
                                [                             // สร้าง ถ้าไม่เจอ
                                    'description' => 'เงินสะสมรายเดือนที่หักจากเงินเดือนเพื่อออม',
                                    'view_in_slip' => false,
                                    'type' => 'Once'
                                ]
                            );

                            if ($User[$i]->userDeducts[$j]->deduct_type_id == $getid_deductType->id) {

                                // เช็คว่า มีฝากในเดือน-ปี นี้ยัง
                                $deposit = EmployeeDeposit::where('user_id', $User[$i]->id)
                                    ->whereYear('date', $year)
                                    ->whereMonth('date', $month)
                                    ->where('type', '!=', 'withdraw')
                                    ->first();

                                if ($deposit) {
                                    // อัปเดตข้อมูล
                                    $newAmount = $User[$i]->userDeducts[$j]->price;
                                    $diff = $newAmount - $deposit->amount;  // คำนวณค่าต่าง

                                    $deposit->amount = $newAmount;
                                    $deposit->total_amount += $diff;        // ปรับยอดรวมตามส่วนต่าง
                                    $deposit->description = "ระบบฝากเงินสะสมรายเดือน";
                                    $deposit->payment_status = 'paid';
                                    $deposit->save();
                                }
                            }
                        }

                        //////////////////////////////////// เงินหักลาพักร้อน //////////////////////////////////////

                        $round = $Payroll->round;
                        $year = $Payroll->year;
                        $month = $Payroll->month;
                        $user_id = $Payroll->user_id;
                        $branch_id = $Payroll->user->branch_id;

                        $total_deduct_holiday =  $this->deductHoliday($Payroll, $branch_id, $user_id, $round, $year, $month, $loginBy);
                        $total_deduct  += $total_deduct_holiday;
                        ///////////////////////////////////////////////////////////////////////////////////////


                        // ---------- รอบ 2 จ่ายเบี้ยขยัน เงินประกัน ถอนเงินฝาก --------------

                        // เช้คเบี้ยขยัน
                        $bonusAmount = $this->checkBonusStep($Payroll->id, $User[$i]->id, $User[$i]->branch_id, $year, $month, $loginBy->user_id);

                        // จ่ายเงินประกันการทำงาน

                        $refunded = BondLog::where('user_id', $User[$i]->id)
                            ->where('year', $year)
                            ->where('month', $month)
                            ->where('type', 'refund')
                            ->first();

                        if ($refunded) {
                            // ถ้าเคยคืนเงินแล้ว ไม่ต้องจ่ายเงินประกัน
                            $getid_type = IncomeType::where('name', 'เงินประกัน')->first();

                            $IncomePaid = new IncomePaid();
                            $IncomePaid->payroll_id = $Payroll->id;
                            $IncomePaid->user_id = $User[$i]->id;
                            $IncomePaid->income_type_id = $getid_type->id;
                            $IncomePaid->year =  $year;
                            $IncomePaid->month =  $month;
                            $IncomePaid->price = $refunded->total_amount;
                            $IncomePaid->description =  $getid_type->name;

                            $IncomePaid->create_by = $loginBy->user_id;

                            $IncomePaid->save();


                            $total_income += $IncomePaid->price;
                        } else {
                            // ถ้ายังไม่เคยคืนเงิน ให้จ่ายเงินประกัน
                            $bondAmount = $this->checkdeductBond($Payroll->id, $User[$i]->id, $User[$i]->branch_id, $year, $month, $loginBy->user_id);
                        }


                        // บวกเพิ่มเข้า total
                        if ($bonusAmount > 0) {
                            $total_income += $bonusAmount;
                        }

                        $type = $bondAmount['type'] ?? null;

                        if ($type === 'income') {
                            $total_income += $bondAmount['amount'];
                        } elseif ($type === 'deduct') {
                            $total_deduct += $bondAmount['amount'];
                        }
                    }


                    //
                    $date_start = date('Y-m-01', strtotime("$year-$month-01"));
                    $date_end = date('Y-m-t', strtotime("$year-$month-01"));

                    if ($round) {
                        $PayrollRound =  PayrollRound::where('round', $round)->first();

                        $start_day = $PayrollRound->start_day;
                        $end_day = $PayrollRound->end_day;

                        $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
                        $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

                    }

                    //salary
                    $salaryUser = $this->calculateSalary($User[$i], $date_start, $date_end, 'haft');

                    //ot
                    $total_ot = $this->getOtUser($User[$i]->id,  $date_start, $date_end);

                    //withdraw salary
                    $total_withdraw_salary  = 0.00;
                    if ($round == 2) {
                        $total_withdraw_salary = $this->getWithdrawSalaryUser($User[$i]->id,  $date_start, $date_end);
                    }

                    // คำนวนหักเงินกู้
                    $total_loan = $this->calculateLoan($User[$i]->id, $year, $month, $round);
                    $total_deduct += $total_loan;

                    //sso and pvd
                    $sso_total = 0.00;
                    $sso_employer_total = 0.00;

                    $pvd_total = 0.00;
                    $pvd_employer_total = 0.00;

                    if ($round == 2) {
                        $sso_status =  $User[$i]->sso_status;
                        $pvd_status =  $User[$i]->pvd_status;

                        //sso
                        $total_contribution_sso = $this->addPayrollContribution($sso_status, 'sso', $Payroll, $User[$i], $year, $month, $loginBy);
                        $sso_code_contribution = $total_contribution_sso['code_contribution'];

                        $sso_total = $total_contribution_sso['employee_total'];
                        $sso_employer_total = $total_contribution_sso['employer_total'];


                        //pvd
                        $total_contribution_sso = $this->addPayrollContribution($pvd_status, 'pvd', $Payroll, $User[$i], $year, $month, $loginBy);
                        $pvd_code_contribution = $total_contribution_sso['code_contribution'];

                        $pvd_total = $total_contribution_sso['employee_total'];
                        $pvd_employer_total = $total_contribution_sso['employer_total'];
                    }

                    $Payroll->sso_total =  $sso_total;
                    $Payroll->sso_employer_total = $sso_employer_total;
                    $Payroll->pvd_total =  $pvd_total;
                    $Payroll->pvd_employer_total = $pvd_employer_total;
                    //

                    $Payroll->salary = $salaryUser;
                    $Payroll->ot = $total_ot;
                    $Payroll->salary_withdraw = $total_withdraw_salary;
                    $Payroll->total_income = $total_income;
                    $Payroll->total_deduct = $total_deduct;
                    $Payroll->total =  ($Payroll->salary +  $Payroll->ot + $Payroll->total_income) - ($Payroll->total_deduct + $Payroll->salary_withdraw + $Payroll->sso_total + $Payroll->pvd_total);
                    $Payroll->save();
                }

                //log
                $userId = $loginBy->user_id;
                $type = 'ดึงข้อมูลเงินเงินเดือนใหม่';
                $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $year . '-' . $month . ' ' . $round;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        } else {
            return $this->returnErrorData('ไม่พบข้อมูลรอบเงินเดือนนี้', 404);
        }
    }

    public function calculateProcess(Request $request)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($request->round)) {
            return $this->returnErrorData('กรุณาใส่รอบเงินเดือนด้วย', 404);
        } else if (!isset($request->year)) {
            return $this->returnErrorData('กรุณาใส่ปีด้วย', 404);
        } else if (!isset($request->month)) {
            return $this->returnErrorData('กรุณาใส่เดือนด้วย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $round = $request->round;
        $year = $request->year;
        $month = $request->month;

        DB::beginTransaction();

        try {

            //
            $date_start = date('Y-m-01', strtotime("$year-$month-01"));
            $date_end = date('Y-m-t', strtotime("$year-$month-01"));

            if ($round) {
                $PayrollRound =  PayrollRound::where('round', $round)->first();

                $start_day = $PayrollRound->start_day;
                $end_day = $PayrollRound->end_day;

                $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
                $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

            }

            $Payroll = Payroll::with('user.userIncomes.income_type')
                ->with('user.userDeducts.deduct_type')
                ->with('incomePaids')
                ->with('deductPaids')
                ->where('round', $round)
                ->where('year', $year)
                ->where('month', $month)
                ->where('branch_id', $login_branch_id)
                ->get();

            if ($Payroll) {

                for ($i = 0; $i < count($Payroll); $i++) {

                    //salary
                    $salaryUser = $this->calculateSalary($Payroll[$i]->user, $date_start, $date_end, 'haft');

                    //ot
                    $total_ot = $this->getOtUser($Payroll[$i]->user,  $date_start, $date_end);


                    //withdraw salary
                    $total_withdraw_salary  = 0.00;
                    if ($round == 2) {
                        $total_withdraw_salary = $this->getWithdrawSalaryUser($Payroll[$i]->user->id,  $date_start, $date_end);
                    }

                    //เงินได้และเงินหัก
                    $total_income = $Payroll[$i]->incomePaids->sum('price');
                    $total_deduct = $Payroll[$i]->deductPaids->sum('price');

                    //เงินประกันสังคม และกองทุน
                    // $contribution_total = $Payroll[$i]->payrollContributions->sum('employee_total');

                    // คำนวนหักเงินกู้
                    $total_loan = $this->calculateLoan($Payroll[$i]->user->id, $year, $month, $round);
                    $total_deduct += $total_loan;

                    //sso and pvd
                    $sso_total = 0.00;
                    $sso_employer_total = 0.00;

                    $pvd_total = 0.00;
                    $pvd_employer_total = 0.00;

                    if ($round == 2) {
                        $sso_status =  $Payroll[$i]->user->sso_status;
                        $pvd_status = $Payroll[$i]->user->pvd_status;

                        //sso
                        $total_contribution_sso = $this->addPayrollContribution($sso_status, 'sso',  $Payroll[$i], $Payroll[$i]->user, $year, $month, $loginBy);
                        $sso_code_contribution = $total_contribution_sso['code_contribution'];

                        $sso_total = $total_contribution_sso['employee_total'];
                        $sso_employer_total = $total_contribution_sso['employer_total'];


                        //pvd
                        $total_contribution_sso = $this->addPayrollContribution($pvd_status, 'pvd', $Payroll[$i], $Payroll[$i]->user, $year, $month, $loginBy);
                        $pvd_code_contribution = $total_contribution_sso['code_contribution'];

                        $pvd_total = $total_contribution_sso['employee_total'];
                        $pvd_employer_total = $total_contribution_sso['employer_total'];
                    }
                    //


                    $up = Payroll::find($Payroll[$i]->id);
                    $up->salary = $salaryUser;
                    $up->ot = $total_ot;
                    $up->salary_withdraw = $total_withdraw_salary;
                    $up->total_income = $total_income;
                    $up->total_deduct = $total_deduct;

                    $up->sso_total =  $sso_total;
                    $up->sso_employer_total = $sso_employer_total;
                    $up->pvd_total =  $pvd_total;
                    $up->pvd_employer_total = $pvd_employer_total;

                    $up->total =  ($up->salary +  $up->ot + $up->total_income) - ($up->total_deduct + $up->salary_withdraw + $sso_total + $pvd_total);
                    $up->save();
                }
            }


            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Payroll = Payroll::with('incomePaids')
                ->with('deductPaids')
                ->find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Payroll';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Payroll->name;
            $this->Log($userId, $description, $type);
            //

            for ($j = 0; $j < count($Payroll->incomePaids); $j++) {
                $Payroll->incomePaids[$j]->delete();
            }

            for ($j = 0; $j < count($Payroll->deductPaids); $j++) {
                $Payroll->deductPaids[$j]->delete();
            }

            $Payroll->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function exportBank(Request $request)
    {
        $token = $request->token;
        $verifyPreviewToken =  $this->verifyPreviewToken($token);

        if (!$verifyPreviewToken) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_branch_id =  $verifyPreviewToken['login_branch_id'];
        $login_user_id =  $verifyPreviewToken['login_user_id'];


        $round =  $verifyPreviewToken['round'];
        $year =  $verifyPreviewToken['year'];
        $month =  $verifyPreviewToken['month'];

        if (!isset($round)) {
            return $this->returnErrorData('กรุณาใส่รอบเงินเดือนด้วย', 404);
        } else if (!isset($year)) {
            return $this->returnErrorData('กรุณาใส่ปีด้วย', 404);
        } else if (!isset($month)) {
            return $this->returnErrorData('กรุณาใส่เดือนด้วย', 404);
        }

        $spreadsheet = new Spreadsheet();
        $reader = IOFactory::createReader('Xls');
        $spreadsheet = $reader->load(public_path() . '/templates/BBL_Payroll_Worksheet.xls');
        $sheet = $spreadsheet->getActiveSheet();

        //
        $Payroll = Payroll::with('user')
            ->with('incomePaids')
            ->with('deductPaids')
            ->where('round', $round)
            ->where('year', $year)
            ->where('month', $month)
            ->where('branch_id', $login_branch_id)
            ->get();

        for ($i = 0; $i < count($Payroll); $i++) {

            $n = $i + 7; //row 7 start

            $sheet->setCellValue('C' . $n, $Payroll[$i]->user->prefix . $Payroll[$i]->user->first_name . ' ' . $Payroll[$i]->user->last_name);
            $sheet->setCellValue('D' . $n, $Payroll[$i]->user->account_no);
            $sheet->setCellValue('E' . $n,  round($Payroll[$i]->total, 2));
        }

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="BBL_Payroll_Worksheet ' . date('Y-m-d') . '.xls"');
        header('Cache-Control: max-age=0');

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xls');
        $writer->save('php://output');
        // return $this->returnSuccess('Successful', []);
    }


    // ปรับแก้ สถานะเงินประกัน และยอดคงเหลือ
    public function updateBondStatus(Request $request, $userId)
    {

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $loginBy = $request->login_by;

        $status = $request->status;
        $balance = $request->balance;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $checkId = User::find($userId);

        DB::beginTransaction();

        try {

            if (!$checkId) {
                return $this->returnErrorData('ไม่พบข้อมูล [user_id]', 404);
            }


            $checkId->is_bond_complete = $status ?? $checkId->is_bond_complete;
            $checkId->bond_balance = $balance ?? $checkId->bond_balance;

            $checkId->save();


            //log
            $userId = $loginBy->user_id;
            $type = 'Edit Bond Status/Balance';
            $description = 'User ' . $userId . ' has ' . $type . 'for user' . $checkId->user_id;
            $this->Log($userId, $description, $type);


            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    private function createPayrollByUser($user, $round, $year, $month, $login_branch_id, $loginBy)
    {
        $Payroll = new Payroll();
        $Payroll->branch_id = $login_branch_id;
        $Payroll->user_id = $user->id;
        $Payroll->round = $round;
        $Payroll->year = $year;
        $Payroll->month = $month;
        $Payroll->pay_status = false;
        $Payroll->status = 1;
        $Payroll->create_by = $loginBy->user_id;

        $Payroll->save();

        $total_income = 0;
        $total_deduct = 0; //จำนวนเงินที่หักทั้งหมด

        //เงินได้และเงินหัก
        if ($round == 2) {

            ////////////////////////////////////// income ///////////////////////////////////
            for ($j = 0; $j < count($user->userIncomes); $j++) {

                $IncomePaid = new IncomePaid();
                $IncomePaid->payroll_id = $Payroll->id;
                $IncomePaid->user_id = $user->id;
                $IncomePaid->income_type_id = $user->userIncomes[$j]->income_types_id;
                $IncomePaid->year =  $year;
                $IncomePaid->month =  $month;
                $IncomePaid->price = $user->userIncomes[$j]->price;
                $IncomePaid->description =  $user->userIncomes[$j]->income_type->name;

                $IncomePaid->create_by = $loginBy->user_id;

                $IncomePaid->save();

                $total_income  += $IncomePaid->price;
            }


            //เช็คว่า user มียอดถอนเงิน ใน เดือนนี้ ปีนี้หรือป่าว
            $withdraw = EmployeeDeposit::where('user_id', $user->id)
                ->whereYear('date', $Payroll->year)
                ->whereMonth('date', $Payroll->month)
                ->where('type', 'withdraw')
                ->where('payment_status', 'pending')
                ->first();

            // dd($withdraw);
            if ($withdraw) {

                $getid_incomeType = IncomeType::firstOrCreate(
                    [
                        'name' => 'ถอนงินฝากพนักงาน',
                        'branch_id' => $user->branch_id
                    ], // หา
                    [                             // สร้าง ถ้าไม่เจอ
                        'description' => 'ถอนงินฝากสะสมของพนักงงาน',
                        'view_in_slip' => false,
                        'type' => 'Once'
                    ]
                );

                $IncomePaid = new IncomePaid();
                $IncomePaid->payroll_id = $Payroll->id;
                $IncomePaid->user_id = $user->id;
                $IncomePaid->income_type_id = $getid_incomeType->id;
                $IncomePaid->year =  $year;
                $IncomePaid->month =  $month;
                $IncomePaid->price = $withdraw->amount;
                $IncomePaid->description =  $getid_incomeType->name;
                $IncomePaid->create_by = $loginBy->user_id;
                $IncomePaid->save();

                $total_income  += $IncomePaid->price;

                $withdraw->payment_status = 'paid';
                $withdraw->save();
            }

            ////////////////////////////////////////////////////////////////////////////////

            //////////////////////////////////// deduct ////////////////////////////////////
            for ($j = 0; $j < count($user->userDeducts); $j++) {

                $DeductPaid = new DeductPaid();
                $DeductPaid->payroll_id = $Payroll->id;
                $DeductPaid->user_id = $user->id;
                $DeductPaid->deduct_type_id = $user->userDeducts[$j]->deduct_type_id;
                $DeductPaid->year =  $year;
                $DeductPaid->month =  $month;
                $DeductPaid->price = $user->userDeducts[$j]->price;
                $DeductPaid->description =  $user->userDeducts[$j]->deduct_type->name;
                $DeductPaid->create_by = $loginBy->user_id;

                $DeductPaid->save();

                $total_deduct  += $DeductPaid->price;


                //////////////////////////////////// เงินฝากพนักงาน //////////////////////////////////////
                $getid_deductType = DeductType::firstOrCreate(
                    [
                        'name' => 'เงินฝากพนักงาน',
                        'branch_id' => $user->branch_id
                    ], // หา
                    [                             // สร้าง ถ้าไม่เจอ
                        'description' => 'เงินสะสมรายเดือนที่หักจากเงินเดือนเพื่อออม',
                        'view_in_slip' => false,
                        'type' => 'Once'
                    ]
                );

                // เงินฝากพนักงานประจำเดือน
                if ($user->userDeducts[$j]->deduct_type_id == $getid_deductType->id) {

                    //ยอดล่าสุด
                    $lastDeposit = EmployeeDeposit::where('user_id', $user->id)
                        ->orderByDesc('date')
                        ->orderByDesc('id')
                        ->first();

                    $totalAmount = $lastDeposit ? $lastDeposit->total_amount + $user->userDeducts[$j]->price : $user->userDeducts[$j]->price;

                    // สร้างใหม่
                    $Item = new EmployeeDeposit();
                    $Item->branch_id = $user->branch_id;
                    $Item->user_id = $user->id;
                    $Item->date = Carbon::createFromDate($year, $month, 1)->endOfMonth();
                    $Item->amount = $user->userDeducts[$j]->price;
                    $Item->total_amount = $totalAmount;
                    $Item->description = "ระบบฝากเงินสะสมรายเดือน";
                    $Item->payment_status = 'paid';

                    $Item->save();
                }
                ///////////////////////////////////////////////////////////////////////////////////////
            }


            //////////////////////////////////// เงินหักลาพักร้อน //////////////////////////////////////

            $total_deduct_holiday =  $this->deductHoliday($Payroll, $user->branch_id, $user->id, $round, $year, $month, $loginBy);
            $total_deduct  += $total_deduct_holiday;

            ///////////////////////////////////////////////////////////////////////////////////////


            // รอบ 2 จ่ายเบี้ยขยัน เงินประกัน

            // เช้คเบี้ยขยัน
            $bonusAmount = $this->checkBonusStep($Payroll->id, $user->id, $user->branch_id, $year, $month, $loginBy->user_id);

            // จ่ายเงินประกันการทำงาน
            $bondAmount = $this->checkdeductBond($Payroll->id, $user->id, $user->branch_id, $year, $month, $loginBy->user_id);


            // บวกเพิ่มเข้า total
            if ($bonusAmount > 0) {
                $total_income += $bonusAmount;
            }

            $type = $bondAmount['type'] ?? null;

            if ($type === 'income') {
                $total_income += $bondAmount['amount'];
            } elseif ($type === 'deduct') {
                $total_deduct += $bondAmount['amount'];
            }
        }


        //

        $date_start = date('Y-m-01', strtotime("$year-$month-01"));
        $date_end = date('Y-m-t', strtotime("$year-$month-01"));

        if ($round) {
            $PayrollRound =  PayrollRound::where('round', $round)->first();

            $start_day = $PayrollRound->start_day;
            $end_day = $PayrollRound->end_day;

            $date_start = Carbon::create($year, $month)->subMonth()->day($start_day)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่  ของเดือนก่อนหน้า
            $date_end = Carbon::create($year, $month)->day($end_day)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่  ของเดือนใน $round

        }

        //salary
        $salaryUser = $this->calculateSalary($user, $date_start, $date_end, 'haft');

        //ot
        $total_ot = $this->getOtUser($user->id, $date_start, $date_end);

        //withdraw salary
        $total_withdraw_salary  = 0.00;
        if ($round == 2) {
            $total_withdraw_salary = $this->getWithdrawSalaryUser($user->id, $date_start, $date_end);
        }

        //sso and pvd
        $sso_total = 0.00;
        $sso_employer_total = 0.00;

        $pvd_total = 0.00;
        $pvd_employer_total = 0.00;

        if ($round == 2) {
            $sso_status =  $user->sso_status;
            $pvd_status =  $user->pvd_status;

            //sso
            $total_contribution_sso = $this->addPayrollContribution($sso_status, 'sso', $Payroll, $user, $year, $month, $loginBy);
            $sso_code_contribution = $total_contribution_sso['code_contribution'];

            $sso_total = $total_contribution_sso['employee_total'];
            $sso_employer_total = $total_contribution_sso['employer_total'];

            //pvd
            $total_contribution_sso = $this->addPayrollContribution($pvd_status, 'pvd', $Payroll, $user, $year, $month, $loginBy);
            $pvd_code_contribution = $total_contribution_sso['code_contribution'];

            $pvd_total = $total_contribution_sso['employee_total'];
            $pvd_employer_total = $total_contribution_sso['employer_total'];
        }

        // คำนวนหักเงินกู้
        $total_loan = $this->calculateLoan($user->id, $year, $month, $round);
        $total_deduct += $total_loan;

        $Payroll->sso_total =  $sso_total;
        $Payroll->sso_employer_total = $sso_employer_total;
        $Payroll->pvd_total =  $pvd_total;
        $Payroll->pvd_employer_total = $pvd_employer_total;
        //

        $Payroll->salary = $salaryUser; //เงินเดือน
        $Payroll->ot = $total_ot;
        $Payroll->salary_withdraw = $total_withdraw_salary;
        $Payroll->total_income = $total_income;
        $Payroll->total_deduct = $total_deduct;
        $Payroll->total =  ($Payroll->salary +  $Payroll->ot + $Payroll->total_income) - ($Payroll->total_deduct + $Payroll->salary_withdraw + $Payroll->sso_total + $Payroll->pvd_total);
        $Payroll->save();

        return $Payroll;
    }

    // private function calculateLoan($user_id, $year, $month, $round)
    // {
    //     // กำหนดช่วงวันที่ของรอบจ่าย (Period Window) ตามตาราง PayrollRound
    //     $periodStart = Carbon::create($year, $month, 1);
    //     $periodEnd   = Carbon::create($year, $month, 1)->endOfMonth();

    //     $payrollRound = PayrollRound::where('round', $round)->first();
    //     if ($payrollRound) {
    //         // เช่น รอบ 1: start_day ถึง end_day (โดย start_day อยู่เดือนก่อนหน้า)
    //         $startDay = (int) $payrollRound->start_day; // เช่น 16
    //         $endDay   = (int) $payrollRound->end_day;   // เช่น 15

    //         // ตามรูปแบบที่ใช้ในไฟล์นี้: ช่วงคำนวณ = [ปี/เดือน-1 ที่ start_day, ปี/เดือน ที่ end_day]
    //         $periodStart = Carbon::create($year, $month)->subMonth()->day($startDay)->startOfDay();
    //         $periodEnd   = Carbon::create($year, $month)->day($endDay)->endOfDay();
    //     }

    //     // วันที่จ่ายจริงตามรอบ
    //     $paidDate = ($round == 1)
    //         ? Carbon::create($year, $month, 15)
    //         : Carbon::create($year, $month, 1)->endOfMonth();

    //     // ค้นหางวดที่ต้องจ่ายในช่วงรอบนี้ (ข้ามงวดที่พักชำระแบบชัดเจน)
    //     $schedules = LoanSchedule::whereHas('loan', function ($q) use ($user_id) {
    //         $q->where('user_id', $user_id);
    //     })
    //         ->whereBetween('due_date', [$periodStart, $periodEnd])
    //         ->whereIn('status', ['due', 'overdue'])
    //         ->orderBy('installment_no')
    //         ->get();

    //     $totalDeduct = 0.00;

    //     foreach ($schedules as $loan) {
    //         // กันความปลอดภัย: ข้ามงวดที่ถูกตั้งสถานะพักไว้
    //         if ($loan->status === 'moratorium') {
    //             continue;
    //         }

    //         // มาร์กจ่ายและสร้างรายการชำระ
    //         $loan->status = 'paid';
    //         $loan->save();

    //         LoanPayment::create([
    //             'loan_id'        => $loan->loan_id,
    //             'installment_no' => $loan->installment_no,
    //             'paid_date'      => $paidDate,
    //             'principal_paid' => $loan->principal_due,
    //             'interest_paid'  => $loan->interest_due,
    //             'total_paid'     => $loan->total_due,
    //             'payroll_ref'    => 'Payroll',
    //             'method'         => 'payroll',
    //         ]);

    //         $totalDeduct += (float) ($loan->total_due ?? 0);
    //     }

    //     return round($totalDeduct, 2);
    // }

    /**
     * ฟังก์ชันคำนวณยอดเงินกู้ที่จะถูกหักออกในรอบเงินเดือน
     * โดยจะไปดึงงวดที่ต้องชำระ (LoanSchedule) มาร์กสถานะเป็น "paid"
     * และสร้างรายการการชำระเงิน (LoanPayment) ให้เรียบร้อย
     *
     * @param int $userId  รหัสผู้ใช้งาน
     * @param int $year    ปีที่ต้องการคำนวณ
     * @param int $month   เดือนที่ต้องการคำนวณ
     * @param int $round   รอบเงินเดือน (1 หรือ 2)
     * @return string      ยอดรวมที่ต้องหัก (ทศนิยม 2 ตำแหน่ง)
     */
    public function calculateLoan(int $userId, int $year, int $month, int $round): string
    {
        // คำนวณช่วงวันที่ของรอบจ่าย (period window)
        [$periodStart, $periodEnd] = $this->computePeriodWindow($year, $month, $round);

        // คำนวณวันที่จ่ายจริงตามรอบ
        $paidDate = $this->computePaidDate($year, $month, $round);

        // ใช้ transaction เพื่อความปลอดภัย และป้องกันการจ่ายซ้ำ
        return DB::transaction(function () use ($userId, $periodStart, $periodEnd, $paidDate) {
            // ดึงรายการงวดที่ถึงกำหนด และ lock แถวกัน concurrent process
            $schedules = $this->fetchPayableSchedules($userId, $periodStart, $periodEnd, lock: true);

            $total = '0.00';
            foreach ($schedules as $sch) {
                // ข้ามงวดที่มีสถานะพักชำระ (moratorium)
                if ($sch->status === LoanScheduleStatus::MORATORIUM->value) {
                    continue;
                }

                // จ่ายงวดนี้ และได้ยอดที่จ่ายกลับมา
                $paid = $this->payOneSchedule($sch, $paidDate);

                // สะสมยอดรวมโดยใช้ bcadd เพื่อความแม่นยำ
                $total = bcadd($total, $paid, 2);
            }
            return $total;
        });
    }

    /**
     * คำนวณช่วงวันที่ของรอบจ่าย (Period Window)
     * เช่น รอบ 1 อาจเริ่มวันที่ 16 ของเดือนก่อน ถึง 15 ของเดือนปัจจุบัน
     */
    private function computePeriodWindow(int $year, int $month, int $round): array
    {
        // ค่าเริ่มต้นคือทั้งเดือน
        $periodStart = Carbon::create($year, $month, 1)->startOfMonth();
        $periodEnd   = Carbon::create($year, $month, 1)->endOfMonth();

        // ถ้ามีการตั้งค่าใน PayrollRound จะ override ช่วงวัน
        $payrollRound = PayrollRound::where('round', $round)->first();
        if ($payrollRound) {
            $startDay = (int) $payrollRound->start_day; // เช่น 16
            $endDay   = (int) $payrollRound->end_day;   // เช่น 15

            $periodStart = Carbon::create($year, $month)->subMonth()->day($startDay)->startOfDay();
            $periodEnd   = Carbon::create($year, $month)->day($endDay)->endOfDay();
        }

        return [$periodStart, $periodEnd];
    }

    /**
     * คำนวณวันที่จ่ายจริงของรอบเงินเดือน
     * รอบ 1 = วันที่ 15, รอบ 2 = วันสิ้นเดือน
     */
    private function computePaidDate(int $year, int $month, int $round): Carbon
    {
        return $round === 1
            ? Carbon::create($year, $month, 15)->startOfDay()
            : Carbon::create($year, $month, 1)->endOfMonth()->startOfDay();
    }

    /**
     * ดึงรายการงวดกู้ที่ถึงกำหนดในช่วงรอบนั้น ๆ
     * @return \Illuminate\Support\Collection<LoanSchedule>
     */
    private function fetchPayableSchedules(int $userId, Carbon $start, Carbon $end, bool $lock = false)
    {
        $q = LoanSchedule::query()
            ->whereHas('loan', fn($q) => $q->where('user_id', $userId))
            ->whereBetween('due_date', [$start, $end])
            ->whereIn('status', [
                LoanScheduleStatus::DUE->value,
                LoanScheduleStatus::OVERDUE->value,
            ])
            ->orderBy('installment_no');

        // lock แถวเพื่อป้องกันการประมวลผลซ้ำซ้อน
        if ($lock) {
            $q->lockForUpdate();
        }

        return $q->get();
    }

    /**
     * ทำการจ่ายงวดกู้ 1 งวด
     * - เปลี่ยนสถานะ schedule เป็น paid
     * - สร้างรายการ LoanPayment
     * - คืนยอดที่จ่าย (string ทศนิยม 2 ตำแหน่ง)
     */
    private function payOneSchedule(LoanSchedule $sch, Carbon $paidDate): string
    {
        // ตรวจสอบว่ามีการสร้าง LoanPayment แล้วหรือยัง (idempotent)
        $exists = LoanPayment::where('loan_id', $sch->loan_id)
            ->where('installment_no', $sch->installment_no)
            ->where('method', 'payroll')
            ->exists();

        if ($exists) {
            return '0.00';
        }

        // อัปเดตสถานะเป็นจ่ายแล้ว
        $sch->status = LoanScheduleStatus::PAID->value;
        $sch->save();

        // สร้าง record ของการจ่ายจริง
        LoanPayment::create([
            'loan_id'        => $sch->loan_id,
            'installment_no' => $sch->installment_no,
            'paid_date'      => $paidDate,
            'principal_paid' => $sch->principal_due,
            'interest_paid'  => $sch->interest_due,
            'total_paid'     => $sch->total_due,
            'payroll_ref'    => 'Payroll',
            'method'         => 'payroll',
        ]);

        // คืนค่าจำนวนเงินที่จ่าย
        $amount = $sch->total_due ?? '0.00';
        return number_format((float)$amount, 2, '.', '');
    }

    public function exportSocial(Request $request)
    {
        $token = $request->token;
        $verifyPreviewToken =  $this->verifyPreviewToken($token);

        if (!$verifyPreviewToken) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_branch_id =  $verifyPreviewToken['login_branch_id'];
        $login_user_id =  $verifyPreviewToken['login_user_id'];


        $round =  $verifyPreviewToken['round'];
        $year =  $verifyPreviewToken['year'];
        $month =  $verifyPreviewToken['month'];

        if (!isset($round)) {
            return $this->returnErrorData('กรุณาใส่รอบเงินเดือนด้วย', 404);
        } else if (!isset($year)) {
            return $this->returnErrorData('กรุณาใส่ปีด้วย', 404);
        } else if (!isset($month)) {
            return $this->returnErrorData('กรุณาใส่เดือนด้วย', 404);
        }

        //
        $data = Payroll::select('user_id', DB::raw('sum(total) as total_summary'), DB::raw('sum(sso_total) as total_sso'))
            ->with('user')
            // ->where('round', $round)
            ->where('year', $year)
            ->where('month', $month)
            ->where('branch_id', $login_branch_id)
            ->groupby('user_id')
            ->get()
            ->toArray();

        if (!empty($data)) {

            for ($i = 0; $i < count($data); $i++) {

                $export_data[] = array(
                    'citizen_no' => trim($data[$i]['user'] ? $data[$i]['user']['citizen_no'] : null),
                    'prefix' => trim($data[$i]['user'] ? $data[$i]['user']['prefix'] : null),
                    'first_name' => trim($data[$i]['user'] ? $data[$i]['user']['first_name'] : null),
                    'last_name' => trim($data[$i]['user'] ? $data[$i]['user']['last_name'] : null),
                    'total_summary' => trim($data[$i]['total_summary']),
                    'total_sso' => trim($data[$i]['total_sso']),
                );
            }

            $result = new SocialExport($export_data);
            return Excel::download($result, 'รายงานเงินสบทบประกันสังคม' . ' ' .  $year . '-' . $month . '(' . $round . ')' . '.xlsx');
        } else {

            $export_data[] = array(
                'citizen_no' => null,
                'prefix' => null,
                'first_name' => null,
                'last_name' => null,
                'total_summary' => null,
                'total_sso' => null,
            );

            $result = new SocialExport($export_data);
            return Excel::download($result, 'รายงานเงินสบทบประกันสังคม' . ' ' .  $year . '-' . $month . '(' . $round . ')' . '.xlsx');
        }
    }
}
