<?php

namespace App\Http\Controllers;

use App\Models\Holiday;
use App\Models\Leave_table_date;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\Work_shift_time;
use App\Models\Zk_time;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OlafController extends Controller
{

    protected $base_url = 'https://klock-grich.dev-asha.com:2053'; //prod
    // protected $base_url = 'https://klock-grich-1.dev-asha.com:2053'; //demo

    public function login()
    {

        try {

            $token = null;

            //api login zk
            $responseLogin = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
                ->post($this->base_url . '/login', [
                    "username" => "admin",
                    "password" => "myrich",
                ]);

            if ($responseLogin->successful()) {

                $resLogin = $responseLogin->body();
                $dataLogin = json_decode($resLogin); //

                $token =  $dataLogin->access;
            }

            return $token;
        } catch (\Throwable $e) {

            return null;
        }
    }

    public function createEmployee($data)
    {

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }


            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->post($this->base_url . '/employee/create', [
                    "name" => $data['name'],
                    "surname" => $data['surname'],
                    "gender" => $data['gender'],
                    "date_of_birth" => $data['date_of_birth'],
                    "employee_no" => $data['employee_no'],
                    "citizen_id" => $data['citizen_id'],
                    "email" => $data['email'],
                    "phone_no" => $data['phone_no'],
                    "role" => $data['role'],
                    "department_id" => $data['department_id'],
                    "group_id" => $data['group_id'],
                    "employment" => $data['employment'],
                    "status_start" => $data['status_start'],
                    "status_end" => $data['status_end'],
                    "image" => $data['image'],
                ]);

            $res = null;
            $olaf_id = null;
            $result = null;

            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //

                if ($res->status == 'ok') {
                    //update olaf id
                    $olaf_id = $res->data->employee->id;
                    if ($olaf_id) {
                        $User =  User::find($data['id']);
                        $User->olaf_id = $olaf_id;
                        $User->save();
                    }

                    $result = [
                        'res' => $res,
                        'olaf_id' => $olaf_id,
                    ];
                } else {
                    $result = [
                        'res' => $res,
                        'olaf_id' => null,
                    ];
                }
            }
            return $result;
        } catch (\Throwable $e) {
            return null;
        }
    }

    public function updateEmployee($code, $data)
    {

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }

            $EmployeeOlaf = $this->getEmployeeByCode($code);

            $emId = $EmployeeOlaf['em_code'];
            $role = $EmployeeOlaf['role'];
            $department = $EmployeeOlaf['department'];
            $group = $EmployeeOlaf['group'];
            $employment = $EmployeeOlaf['employment'];

            $body = null;
            if ($data['image']) {
                $body = [
                    "name" => $data['name'],
                    "surname" => $data['surname'],
                    "gender" => $data['gender'],
                    "date_of_birth" => $data['date_of_birth'],
                    "employee_no" => $data['employee_no'],
                    "citizen_id" => $data['citizen_id'],
                    "email" => $data['email'],
                    "phone_no" => $data['phone_no'],
                    "role" => $role,
                    "department_id" => $department,
                    "group_id" => $group,
                    "employment" => $employment,
                    "status_start" => $data['status_start'],
                    "status_end" => $data['status_end'],
                    "image" => $data['image'],
                    "same_image" => true
                ];
            } else {
                $body = [
                    "name" => $data['name'],
                    "surname" => $data['surname'],
                    "gender" => $data['gender'],
                    "date_of_birth" => $data['date_of_birth'],
                    "employee_no" => $data['employee_no'],
                    "citizen_id" => $data['citizen_id'],
                    "email" => $data['email'],
                    "phone_no" => $data['phone_no'],
                    "role" => $role,
                    "department_id" => $department,
                    "group_id" => $group,
                    "employment" => $employment,
                    "status_start" => $data['status_start'],
                    "status_end" => $data['status_end'],
                    "same_image" => false
                ];
            }


            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->put($this->base_url . '/employee/edit/' . $emId, $body);
            $res = null;
            $result = null;

            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //

                if ($res->status == 'ok') {
                    $result = [
                        'res' => $res,
                        'olaf_id' => $emId,
                    ];
                } else {
                    $result = [
                        'res' => $res,
                        'olaf_id' => null,
                    ];
                }
            }

            return $result;
        } catch (\Throwable $e) {

            return null;
        }
    }

    public function deleteEmployee($code)
    {

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }

            $EmployeeOlaf = $this->getEmployeeByCode($code);
            $emId = $EmployeeOlaf['em_code'];

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->delete($this->base_url . '/employee/delete/' . $emId, []);

            $res = null;
            $result = null;

            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //

                if ($res->status == 'ok') {
                    $result = [
                        'res' => $res,
                        'olaf_id' => $emId,
                    ];
                } else {
                    $result = [
                        'res' => $res,
                        'olaf_id' => null,
                    ];
                }
            }

            return $result;
        } catch (\Throwable $e) {

            return null;
        }
    }

    public function getEmployeeByCode($code)
    {

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->get($this->base_url . '/employee/code?employee_no=' . $code);
            $res = null;
            $emCode = null;

            $role = 'STF';
            $department = 1;
            $group = 2;
            $employment = 'FTM';

            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //

                if ($res->status == 'ok') {
                    $emCode =  $res->data->id;

                    $role =  $res->data->role;
                    $department =  $res->data->department;
                    $group =  $res->data->group;
                    $employment =  $res->data->employment;
                }
            }

            $data = [
                'em_code' => $emCode,
                'role' => $role,
                'department' => $department,
                'group' => $group,
                'employment' => $employment,
            ];

            return $data;
        } catch (\Throwable $e) {

            return null;
        }
    }


    public function getGroup()
    {

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->get($this->base_url . '/group/getAll?branch=1');
            $res = null;
            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //
            }

            return $res;
        } catch (\Throwable $e) {

            return null;
        }
    }

    public function getRole()
    {

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->get($this->base_url . '/employee/roles');
            $res = null;
            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //
            }

            return $res;
        } catch (\Throwable $e) {

            return null;
        }
    }

    public function getDepartment()
    {

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->get($this->base_url . '/department/getAll');
            $res = null;
            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //
            }

            return $res;
        } catch (\Throwable $e) {

            return null;
        }
    }

    public function punchlog(Request $request)
    {

        $start = $request->start;
        $end = $request->end;

        try {

            $token = $this->login();
            if (!$token) {
                return null;
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'authorization' => 'Bearer ' . $token
            ])
                ->get('https://klock-grich.dev-asha.com:2053/attendances/punchlog?start=' . $start . '&end=' . $end);
            $res = null;
            if ($response->successful()) {

                $Res = $response->body();
                $res = json_decode($Res); //

                if ($res->status == 'ok') {
                    //
                    $Data = $res->data;
                    for ($i = 0; $i < count($Data); $i++) {

                        $check = Zk_time::where('time', $Data[$i]->date . ' ' . $Data[$i]->time)->first();

                        if (!$check) {

                            $zkTime = new Zk_time();
                            $zkTime->event_id =  $Data[$i]->employee_id;
                            $zkTime->time = $Data[$i]->date . ' ' . $Data[$i]->time;
                            $zkTime->area_name = $Data[$i]->camera_name;
                            $zkTime->device_name = $Data[$i]->camera_name;
                            $zkTime->event_point = $Data[$i]->camera_name;
                            $zkTime->event_description = $Data[$i]->camera_name;
                            $zkTime->personnel_id = $Data[$i]->employee__employee_no;
                            $zkTime->first_name = '';
                            $zkTime->last_name = '';
                            $zkTime->card_number = $Data[$i]->employee__employee_no;
                            $zkTime->department_number = '';
                            $zkTime->department_name = '';
                            $zkTime->reader_name = '';
                            $zkTime->verification_mode = '';

                            // Save the model instance to database
                            $zkTime->save();


                            ///////////////////////////////// add Attendance /////////////////////////////////
                            $dateYesterday = $Data[$i]->date;

                            $User = User::where('personnel_id', $Data[$i]->employee__employee_no)->first();

                            //check duplicate Attendance
                            $check = User_attendance::where('user_id', $User->id)
                                ->where('date', $dateYesterday)
                                ->first();

                            if (!$check) {

                                //add

                                $strDate = date('D', strtotime($dateYesterday));

                                //check working day
                                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                                if ($User->work_shift_id) {
                                    $workTime->where('work_shift_id', $User->work_shift_id);
                                }
                                $WorkTime =  $workTime->first();

                                if ($WorkTime) {

                                    //check holiday
                                    $Holiday =  Holiday::where('date', $dateYesterday)->first();
                                    if (!$Holiday) {

                                        if ($WorkTime->status == true) {
                                            //
                                            $Zk_time =  Zk_time::where('personnel_id', $User->personnel_id)
                                                ->where('time', 'like', '%' . $dateYesterday . '%')
                                                ->orderby('time')
                                                ->first();

                                            //check leave
                                            $userID =  $User->id;
                                            $Leave_table_date =  Leave_table_date::with('leave_table')
                                                ->where('date', $dateYesterday)
                                                ->WhereHas('leave_table', function ($query) use ($userID) {
                                                    $query->where('user_id', $userID);
                                                    $query->where('status', 'approved');
                                                })
                                                ->first();

                                            if ($Leave_table_date) {
                                                //leave
                                                $User_attendance =  new User_attendance();
                                                $User_attendance->date =  $dateYesterday;
                                                $User_attendance->user_id =  $User->id;
                                                $User_attendance->zkt_time_id = null;
                                                $User_attendance->type = 'leave';
                                                $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                                $User_attendance->status = true;
                                                $User_attendance->save();
                                            } else if ($Zk_time) {

                                                if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                                    //late
                                                    $User_attendance =  new User_attendance();
                                                    $User_attendance->date =  $dateYesterday;
                                                    $User_attendance->user_id =  $User->id;
                                                    $User_attendance->zkt_time_id = $Zk_time->id;
                                                    $User_attendance->type = 'late';
                                                    $User_attendance->leave_table_id = null;
                                                    $User_attendance->status = true;
                                                    $User_attendance->save();
                                                } else {
                                                    //normal
                                                    $User_attendance =  new User_attendance();
                                                    $User_attendance->date =  $dateYesterday;
                                                    $User_attendance->user_id =  $User->id;
                                                    $User_attendance->zkt_time_id = $Zk_time->id;
                                                    $User_attendance->type = 'normal';
                                                    $User_attendance->leave_table_id = null;
                                                    $User_attendance->status = true;
                                                    $User_attendance->save();
                                                }
                                            } else {

                                                //miss
                                                $User_attendance =  new User_attendance();
                                                $User_attendance->date =  $dateYesterday;
                                                $User_attendance->user_id =  $User->id;
                                                $User_attendance->zkt_time_id = null;
                                                $User_attendance->type = 'miss';
                                                $User_attendance->leave_table_id = null;
                                                $User_attendance->status = true;
                                                $User_attendance->save();
                                            }
                                        } else {
                                            //off
                                            $User_attendance =  new User_attendance();
                                            $User_attendance->date =  $dateYesterday;
                                            $User_attendance->user_id =  $User->id;
                                            $User_attendance->zkt_time_id = null;
                                            $User_attendance->type = 'off';
                                            $User_attendance->leave_table_id = null;
                                            $User_attendance->status = true;
                                            $User_attendance->save();
                                        }
                                    } else {
                                        //off
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User->id;
                                        $User_attendance->zkt_time_id = null;
                                        $User_attendance->type = 'off';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    }
                                }
                            } else {

                                //update

                                $strDate = date('D', strtotime($dateYesterday));

                                //check working day
                                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                                if ($User->work_shift_id) {
                                    $workTime->where('work_shift_id', $User->work_shift_id);
                                }
                                $WorkTime =  $workTime->first();

                                if ($WorkTime) {

                                    //check holiday
                                    $Holiday =  Holiday::where('date', $dateYesterday)->first();
                                    if (!$Holiday) {

                                        if ($WorkTime->status == true) {
                                            //
                                            $Zk_time =  Zk_time::where('personnel_id', $User->personnel_id)
                                                ->where('time', 'like', '%' . $dateYesterday . '%')
                                                ->orderby('time')
                                                ->first();

                                            //check leave
                                            $userID =  $User->id;
                                            $Leave_table_date =  Leave_table_date::with('leave_table')
                                                ->where('date', $dateYesterday)
                                                ->WhereHas('leave_table', function ($query) use ($userID) {
                                                    $query->where('user_id', $userID);
                                                    $query->where('status', 'approved');
                                                })
                                                ->first();

                                            if ($Leave_table_date) {
                                                //leave
                                                $User_attendance =  new User_attendance();
                                                $User_attendance->date =  $dateYesterday;
                                                $User_attendance->user_id =  $User->id;
                                                $User_attendance->zkt_time_id = null;
                                                $User_attendance->type = 'leave';
                                                $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                                $User_attendance->status = true;
                                                $User_attendance->save();
                                            } else if ($Zk_time) {

                                                if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                                    //late
                                                    $check->date =  $dateYesterday;
                                                    $check->user_id =  $User->id;
                                                    $check->zkt_time_id = $Zk_time->id;
                                                    $check->type = 'late';
                                                    $check->leave_table_id = null;
                                                    $check->status = true;
                                                    $check->save();
                                                } else {
                                                    //normal
                                                    $check->date =  $dateYesterday;
                                                    $check->user_id =  $User->id;
                                                    $check->zkt_time_id = $Zk_time->id;
                                                    $check->type = 'normal';
                                                    $check->leave_table_id = null;
                                                    $check->status = true;
                                                    $check->save();
                                                }
                                            } else {

                                                //miss
                                                $check->date =  $dateYesterday;
                                                $check->user_id =  $User->id;
                                                $check->zkt_time_id = null;
                                                $check->type = 'miss';
                                                $check->leave_table_id = null;
                                                $check->status = true;
                                                $check->save();
                                            }
                                        } else {
                                            //off
                                            $check->date =  $dateYesterday;
                                            $check->user_id =  $User->id;
                                            $check->zkt_time_id = null;
                                            $check->type = 'off';
                                            $check->leave_table_id = null;
                                            $check->status = true;
                                            $check->save();
                                        }
                                    } else {
                                        //off
                                        $check->date =  $dateYesterday;
                                        $check->user_id =  $User->id;
                                        $check->zkt_time_id = null;
                                        $check->type = 'off';
                                        $check->leave_table_id = null;
                                        $check->status = true;
                                        $check->save();
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', []);
        } catch (\Throwable $e) {

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }
}
