<?php

namespace App\Http\Controllers;

use App\Models\EmployeeDeposit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmployeeDepositController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getPage(Request $request)
    {
        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $month = $request->month ?? now()->month;
        $year = $request->year ?? now()->year;
        $user_id = $request->user_id;
        $status = $request->status;

        $col = array('id', 'branch_id', 'user_id', 'date', 'amount', 'total_amount', 'description', 'type', 'payment_status' , 'created_at', 'updated_at');

        $orderby = array('id', 'branch_id', 'user_id', 'date', 'amount', 'total_amount', 'description', 'type' , 'payment_status' , 'created_at', 'updated_at');

        $d = EmployeeDeposit::select($col)
            ->with('user')
            ->whereYear('date', $year)
            ->whereMonth('date', $month);

        if($user_id){
            $d->where('user_id', $user_id);
        }

        if($login_branch_id){
            $d->where('branch_id', $login_branch_id);
        }

        if($status){
            $d->where('type', $status);
        }

        // dd($d->toSql(), $d->getBindings());

        if ($orderby[$order[0]['column']]) {
            $d->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            $d->Where(function ($query) use ($search, $col) {

                //search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$c) {
                        $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                    }
                });

                //search with
                // //$query = $this->withPermission($query, $search);
            });
        }

        $d = $d->orderby('id','desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }


        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    

    }

   
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาระบุพนักงงาน', 404);
        }if (!isset($request->amount)) {
            return $this->returnErrorData('กรุณาระบุจำนวนเงินฝาก', 404);
        }
        if ($request->amount <= 0) {
            return $this->returnErrorData('เกิดข้อผิดพลาด! ต้องระบุจำนวนมากกว่า 0', 404);
        }

        DB::beginTransaction();

        try {

            //ยอดล่าสุด
            $lastDeposit = EmployeeDeposit::where('user_id', $request->user_id)
                ->orderBy('date', 'desc')
                ->orderBy('id', 'desc')
                ->first();

            $totalAmount = $lastDeposit ? $lastDeposit->total_amount + $request->amount : $request->amount;

            // dd($lastDeposit->total_amount, $request->amount);

            // สร้างใหม่
            $Item = new EmployeeDeposit();
            $Item->branch_id = $login_branch_id;
            $Item->user_id = $request->user_id;
            // $Item->date = ($request->year && $request->month)
            //             ? Carbon::createFromDate($request->year, $request->month, 1)->endOfMonth()
            //             : now();
            $Item->date =now();
            $Item->amount = $request->amount;
            $Item->total_amount = $totalAmount;
            $Item->description = $request->description;
            $Item->payment_status = 'paid';
            $Item->save();
            
            //

            //log
            $userId = "admin";
            $type = 'เพิ่มจำนวนเงินฝากพนักงาน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', $Item);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //

        $Item = EmployeeDeposit::find($id);
        
        if($Item){
            return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
        }else{
            return $this->returnErrorData('[id] not found', 404);
        }


       
    }

   

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if ($request->amount <= 0) {
            return $this->returnErrorData('เกิดข้อผิดพลาด! ต้องระบุจำนวนมากกว่า 0', 404);
        }

        DB::beginTransaction();

        try {

            $employeeDeposit = EmployeeDeposit::find($id);
            if (!$employeeDeposit) {
                return $this->returnErrorData('[id] not found', 404);
            }

            // เช้ค ว่าเป็นเดือนล่าสุดป่าว
            $latestDeposit = EmployeeDeposit::where('user_id', $employeeDeposit->user_id)
                ->orderByDesc('date')
                ->first();

            if (!$latestDeposit || $employeeDeposit->id !== $latestDeposit->id) {
                return $this->returnErrorData('อนุญาตให้แก้ไขเฉพาะเดือนล่าสุดเท่านั้น', 403);
            }

            // คำนวณค่าต่างของ amount ใหม่กับเก่า
            $diff = $request->amount - $employeeDeposit->amount;


            $employeeDeposit->amount = $request->amount;
            $employeeDeposit->total_amount = ($employeeDeposit->total_amount ?? 0) + $diff;
            $employeeDeposit->description = $request->description;

            $employeeDeposit->save();
            
            //

            //log
            $userId = "admin";
            $type = 'แก้ไขจำนวนเงินฝากพนักงาน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', $employeeDeposit);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }


    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request , $id)
    {
        //

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }
        

        DB::beginTransaction();
        try {

            $item = EmployeeDeposit::find($id);
            if (!$item) {
                return $this->returnErrorData('[id] not found', 404);
            }
            
            //delete
            $item->delete();

            //log
            $userId = "admin";
            $type = 'ลบรายการเงินฝากพนักงาน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }


    // ถอนเงิน
    public function withdrawEmployee(Request $request){
        
        DB::beginTransaction();

        try {
            $userId = $request->user_id;
            $amount = $request->amount;
            $date = now();
            $payment_type = $request->payment_type;
            
            // $date = "2025-07-12";

            // เช็กว่า user นี้ถอนเดือนนี้ มีคำสั่งถอนค้างอยู่มั้ย
            $hasWithdraw = EmployeeDeposit::where('user_id', $userId)
                ->where('type', 'withdraw')
                ->where('payment_status' , 'pending')
                ->whereYear('date', now()->year)
                ->whereMonth('date', now()->month)
                ->exists();

            if ($hasWithdraw) {
                return $this->returnErrorData('คุณมีคำขอถอนเงินที่รอดำเนินการในเดือนนี้แล้ว', 400);
            }


            // ดึงยอดล่าสุดของพนักงาน
            $latest = EmployeeDeposit::where('user_id', $userId)
                ->orderByDesc('date')
                ->orderByDesc('id')
                ->first();
            // dd($latest);

            if (!$latest || $latest->total_amount < $amount) {
                return $this->returnErrorData('ยอดเงินไม่เพียงพอสำหรับการถอน', 400);
            }

            

             // สร้างรายการถอนใหม่ 
            $newWithdraw = new EmployeeDeposit();
            $newWithdraw->user_id = $userId;
            $newWithdraw->branch_id = $latest->branch_id;
            $newWithdraw->date = $date;
            $newWithdraw->amount = $amount;
            $newWithdraw->total_amount = $latest->total_amount - $amount;
           
            $newWithdraw->type = 'withdraw';

            if($payment_type == "immediate"){ 
                $newWithdraw->description = 'ถอนเงินฝาก';
                $newWithdraw->payment_status = 'paid';
               
            }else if ($payment_type == "payroll"){
                $newWithdraw->description = 'ถอนเงินฝาก (รอจ่ายพร้อมเงินเดือน)';
                $newWithdraw->payment_status = 'pending';
    
            }

            $newWithdraw->save();

            

        

            DB::commit();
            return $this->returnSuccess('ถอนเงินสำเร็จ', []);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnErrorData('Something went wrong Please try again: ' . $e->getMessage(), 500);
        }
    }

}
