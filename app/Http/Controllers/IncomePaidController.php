<?php

namespace App\Http\Controllers;

use App\Models\IncomePaid;
use App\Models\IncomeType;
use App\Models\User;
use App\Models\UserIncome;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class IncomePaidController extends Controller
{

    public function getListAll()
    {
        $Item = IncomePaid::all();

        if (!empty($Item)) {

            for ($i = 0; $i < count($Item); $i++) {
                $Item[$i]['No'] = $i + 1;
                $Item[$i]['income_type'] = IncomeType::find($Item[$i]['income_type_id']);
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    public function getList($id)
    {
        $Item = IncomePaid::where('user_id', $id)->get();

        if (!empty($Item)) {

            for ($i = 0; $i < count($Item); $i++) {
                $Item[$i]['No'] = $i + 1;
                $Item[$i]['income_type'] = IncomeType::find($Item[$i]['income_type_id']);
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    public function getPage(Request $request)
    {
        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $user_id = $request->user_id;
        $month = $request->month;
        $year = $request->year;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $round = $year . "-" . $month;
        $roundDate = $year . "-" . $month . "-01";

        $lastMonth26 = date('Y-m-26', strtotime('-1 month', strtotime($roundDate)));
        $nowMonth25 = date('Y-m-25', strtotime($roundDate));

        $Status = $request->status;

        $col = array('id', 'user_id', 'income_type_id', 'date', 'price', 'type', 'description', 'create_by', 'update_by', 'created_at', 'updated_at');

        $orderby = array('', 'user_id', 'income_type_id', 'date', 'price', 'type', 'description', 'create_by');

        $d = IncomePaid::select($col)
            ->with('user');

        if ($user_id) {
            $d->where('user_id',  $user_id);
        }


        if ($login_branch_id) {
            $d->whereHas('user', function ($query) use ($login_branch_id) {
                $query->where('branch_id', $login_branch_id);
            });
        }

        if ($year && $month) {
            $d->whereBetween('date', [$lastMonth26, $nowMonth25]);
            // $d->where('date', 'like', '%' . $round . '-%');
        }

        if (isset($Status)) {
            $d->where('status', $Status);
        }

        if ($orderby[$order[0]['column']]) {
            $d->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            $d->Where(function ($query) use ($search, $col) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });

                // search with
                $query = $this->withUser($query, $search);
            });
        }


        $d = $d->orderby('id','desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
                $d[$i]->income_type = IncomeType::where('id', $d[$i]->income_type_id)->get();
                $d[$i]->user = User::where('id', $d[$i]->user_id)->get();
                $d[$i]->create = User::where('user_id', $d[$i]->create_by)->first();
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาระบุ user_id ให้เรียบร้อย', 404);
        } else if (!isset($request->income_type_id)) {
            return $this->returnErrorData('กรุณาระบุ income_type_id ให้เรียบร้อย', 404);
        } else if (!isset($request->date)) {
            return $this->returnErrorData('กรุณาระบุ date ให้เรียบร้อย', 404);
        } else if (!isset($request->price)) {
            return $this->returnErrorData('กรุณาระบุ price ให้เรียบร้อย', 404);
        } else if (!isset($request->description)) {
            return $this->returnErrorData('กรุณาระบุ description ให้เรียบร้อย', 404);
        }
        // else if (!isset($request->type)) {
        //     return $this->returnErrorData('กรุณาระบุ type ให้เรียบร้อย', 404);
        // }

        DB::beginTransaction();

        try {
            $Item = new IncomePaid();
            $Item->user_id = $request->user_id;
            $Item->income_type_id = $request->income_type_id;
            $Item->date = $request->date;
            $Item->price = $request->price;
            $Item->description = $request->description;
            // $Item->type = $request->type;

            $Item->create_by = $loginBy->user_id;

            $Item->save();

            // เพิ่มข้อมูลลงในตาราง UserIncome หาก type เป็น "All Month" และไม่มีข้อมูลซ้ำในตาราง
            if ($request->type === 'All Month') {
                $existingRecord = UserIncome::where('user_id', $request->user_id)
                    ->where('income_types_id', $request->income_type_id)
                    ->where('date', $request->date)
                    ->exists();

                if (!$existingRecord) {
                    $userIncome = new UserIncome();
                    $userIncome->user_id = $request->user_id;
                    $userIncome->income_paids_id = $Item->id;
                    $userIncome->income_types_id = $request->income_type_id;
                    $userIncome->date = $request->date;
                    $userIncome->price = $request->price;

                    $userIncome->save();
                } else {
                    return $this->returnErrorData('มีข้อมูลชุดนี้ในระบบแล้ว', 404);
                }
            }

            //log
            $userId = "admin";
            $type = 'เพิ่มรายการ';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $request->name;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', $Item);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param  \App\Models\IncomePaid  $IncomePaid
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Item = IncomePaid::where('id', $id)
            ->first();


        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\IncomePaid  $IncomePaid
     * @return \Illuminate\Http\Response
     */
    public function edit(IncomePaid $IncomePaid)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\IncomePaid  $IncomePaid
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('ไม่พบข้อมูล id', 404);
        }
        
        DB::beginTransaction();
        try {

            $Item = IncomePaid::find($id);
            if (!$Item) {
                return $this->returnErrorData('ไม่พบข้อมูลในระบบ', 404);
            }

            $Item->user_id = $request->user_id;
            $Item->income_type_id = $request->income_type_id;
            $Item->date = $request->date;
            $Item->price = $request->price;
            $Item->description = $request->description;
            // $Item->type = $request->type;

            $Item->create_by = $loginBy->user_id;

            $Item->save();

            //log
            $userId = "admin";
            $type = 'แก้ไขผู้ใช้งาน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $Item->username;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('ดำเนินการสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\IncomePaid  $IncomePaid
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Item = IncomePaid::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Item';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Item->name;
            $this->Log($userId, $description, $type);
            //

            $Item->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
    public function getIncomewithUser(Request $request, $id)
    {

        DB::beginTransaction();
        try {

            $Item = UserIncome::where('user_id', $id)
                ->with('income_paids')
                ->get();


            DB::commit();
            return $this->returnSuccess('Successful operation', $Item);
        } catch (\Throwable $e) {
            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
}
