<?php

namespace App\Http\Controllers;

use App\Imports\HolidayImport;
use App\Models\Holiday;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class HolidayController extends Controller
{

    public function getHoliday()
    {

        $Holiday = Holiday::where('status', 1)->with('user_create')->get()->toarray();

        if (!empty($Holiday)) {

            for ($i = 0; $i < count($Holiday); $i++) {
                $Holiday[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $Holiday);
    }

    public function HolidayPage(Request $request)
    {

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $year = $request->year;
        $type = $request->type;

        $col = array('id', 'date', 'name', 'type', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Holiday::select($col)->with('user_create')
            ->orderby($col[$order[0]['column']], $order[0]['dir']);

        if (isset($year)) {
            $d->where('date', 'like', '%' . $year . '-%');
        }

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        if ($type) {
            $d->where('type', $type);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;


        if (!isset($request->date)) {
            return $this->returnErrorData('กรุณาใส่วันที่ด้วย', 404);
        } else if (!isset($request->name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อวันหยุดด้วย', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $date = $request->date;
        $name = $request->name;

        $checkName = Holiday::where('date', $date)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this date in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $Holiday = new Holiday();
                $Holiday->branch_id = $login_branch_id;
                $Holiday->date = $date;
                $Holiday->name = $name;
                $Holiday->type  = $request->type ?? 'holiday'; // holiday ,off
                $Holiday->status = 1;

                $Holiday->create_by = $loginBy->user_id;

                $Holiday->save();

                //log
                $userId = $loginBy->user_id;
                $type = 'Add Holiday';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Holiday = Holiday::find($id);
        return $this->returnSuccess('Successful', $Holiday);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        DB::beginTransaction();

        try {

            $Holiday = Holiday::find($id);

            $Holiday->name = $name;
            $Holiday->date = $request->date;
            $Holiday->status = $request->status;

            $Holiday->update_by = $loginBy->user_id;
            $Holiday->updated_at = Carbon::now()->toDateTimeString();

            $Holiday->save();
            //log
            $userId = $loginBy->user_id;
            $type = 'Edit Holiday';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Holiday->name;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Holiday = Holiday::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Holiday';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Holiday->name;
            $this->Log($userId, $description, $type);
            //

            $Holiday->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function ImportHoliday(Request $request)
    {

        $loginBy = $request->login_by;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($loginBy)) {
            return $this->returnErrorData('User information not found. Please login again', 404);
        }

        $file = request()->file('file');
        $fileName = $file->getClientOriginalName();

        $Data = Excel::toArray(new HolidayImport(), $file);
        $data = $Data[0];

        if (count($data) > 0) {

            DB::beginTransaction();

            try {


                for ($i = 0; $i < count($data); $i++) {

                    $date = trim($data[$i][0]);
                    $name = trim($data[$i][1]);


                    $row = $i + 2;

                    if ($date == '') {
                        return $this->returnErrorData('Row excel data ' . $row . 'please enter date', 404);
                    } else if ($name == '') {
                        return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
                    }

                    $strDate =  $date;
                    $_date = Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($strDate));
                    $Date = date("Y-m-d", strtotime($_date));


                    //check name
                    $check = Holiday::where('date', $date)
                        ->where('branch_id', $login_branch_id)
                        ->first();

                    if ($check) {
                        //update
                        $check->date = $date;
                        $check->name = $name;
                        $check->save();
                    } else {
                        //add
                        $Holiday = new Holiday();
                        $Holiday->branch_id = $login_branch_id;
                        $Holiday->date = $Date;
                        $Holiday->name = $name;
                        $Holiday->type  = $request->type;
                        $Holiday->status = 1;
                        $Holiday->create_by = $loginBy->user_id;

                        $Holiday->save();
                    }
                }

                DB::commit();

                //log
                $userId = $loginBy->user_id;
                $type = 'Import Holiday';
                $description = 'User ' . $userId . ' has ' . $type;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        } else {
            return $this->returnErrorData('Data Not Found', 404);
        }
    }
}
