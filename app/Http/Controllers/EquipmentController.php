<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class EquipmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Equipment::with(['category', 'branch']);

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Filter by category
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Filter by branch
            if ($request->has('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }

            // Filter by availability
            if ($request->has('available') && $request->available == 'true') {
                $query->available();
            }

            // Search by name, code, or brand
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('brand', 'like', "%{$search}%");
                });
            }

            $perPage = $request->get('per_page', 15);
            $equipments = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $equipments,
                'message' => 'Equipment list retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve equipment list',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required|string|unique:equipments,code',
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'brand' => 'nullable|string|max:255',
                'model' => 'nullable|string|max:255',
                'serial_number' => 'nullable|string|max:255',
                'purchase_price' => 'nullable|numeric|min:0',
                'purchase_date' => 'nullable|date',
                'condition' => 'nullable|string|in:good,fair,poor',
                'status' => 'nullable|string|in:available,borrowed,maintenance,damaged,retired',
                'location' => 'nullable|string|max:255',
                'category_id' => 'nullable|exists:equipment_categories,id',
                'branch_id' => 'required|exists:branch,id',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['create_by'] = auth()->user()->id ?? null;

            // Handle image upload
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('equipment-images', 'public');
                $data['image'] = $imagePath;
            }

            $equipment = Equipment::create($data);
            $equipment->load(['category', 'branch']);

            return response()->json([
                'success' => true,
                'data' => $equipment,
                'message' => 'Equipment created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create equipment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): JsonResponse
    {
        try {
            $equipment = Equipment::with(['category', 'branch', 'borrowingItems.borrowing.user'])
                                 ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $equipment,
                'message' => 'Equipment retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Equipment not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $equipment = Equipment::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'code' => 'required|string|unique:equipments,code,' . $id,
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'brand' => 'nullable|string|max:255',
                'model' => 'nullable|string|max:255',
                'serial_number' => 'nullable|string|max:255',
                'purchase_price' => 'nullable|numeric|min:0',
                'purchase_date' => 'nullable|date',
                'condition' => 'nullable|string|in:good,fair,poor',
                'status' => 'nullable|string|in:available,borrowed,maintenance,damaged,retired',
                'location' => 'nullable|string|max:255',
                'category_id' => 'nullable|exists:equipment_categories,id',
                'branch_id' => 'required|exists:branch,id',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['update_by'] = auth()->user()->id ?? null;

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($equipment->image) {
                    Storage::disk('public')->delete($equipment->image);
                }
                $imagePath = $request->file('image')->store('equipment-images', 'public');
                $data['image'] = $imagePath;
            }

            $equipment->update($data);
            $equipment->load(['category', 'branch']);

            return response()->json([
                'success' => true,
                'data' => $equipment,
                'message' => 'Equipment updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update equipment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
            $equipment = Equipment::findOrFail($id);

            // Check if equipment is currently borrowed
            if ($equipment->status === 'borrowed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete equipment that is currently borrowed'
                ], 400);
            }

            // Delete image if exists
            if ($equipment->image) {
                Storage::disk('public')->delete($equipment->image);
            }

            $equipment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Equipment deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete equipment',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
