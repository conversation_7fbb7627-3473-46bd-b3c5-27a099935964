<?php

namespace App\Http\Controllers;

use App\Mail\SendMail;
use App\Models\Amphure;
use App\Models\BondLog;
use App\Models\Commission;
use App\Models\Config;
use App\Models\Config_stock;
use App\Models\Doc;
use App\Models\Holiday;
use App\Models\Item_line;
use App\Models\Item_trans;
use App\Models\Leave_permission;
use App\Models\Leave_table;
use App\Models\LeaveType;
use App\Models\Location;
use App\Models\Log;
use App\Models\Log_saleOrder;
use App\Models\Ot;
use App\Models\Ot_type;
use App\Models\Payroll;
use App\Models\PayrollRound;
use App\Models\Position;
use App\Models\Province;
use App\Models\Sale_order;
use App\Models\Sale_order_line;
use App\Models\TeleSalesTopUpLog;
use App\Models\User;
use App\Models\User_leave_permission;
use App\Models\User_attendance;
use App\Models\Vendor;
use App\Models\Withdraw_salary;
use App\Models\IncomePaid;
use App\Models\UserIncome;
use App\Models\BonusStep;
use App\Models\DeductType;
use App\Models\IncomeType;
use App\Models\UserDeduct;
use App\Models\DeductPaid;
use Carbon\Carbon;
use DateTime;
use finfo;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;
use Intervention\Image\Facades\Image;
use PhpOffice\PhpSpreadsheet\Calculation\MathTrig\Round;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;


    public function getPermissionViewData($user)
    {

        $loginBy = User::with('permission')
            ->with('branch.company')
            ->where('id', $user->id)
            ->first();

        $company_id = null;
        $branch_id = null;
        $user_id = null;

        if ($loginBy->permission->permission_view == 'all') {
            $company_id = null;
            $branch_id = null;
            $user_id = null;
        }
        if ($loginBy->permission->permission_view == 'company') {
            $company_id = $loginBy->branch->company_id;
            $branch_id = null;
            $user_id = null;
        }
        if ($loginBy->permission->permission_view == 'branch') {
            $company_id = $loginBy->branch->company_id;
            $branch_id = $loginBy->branch_id;
            $user_id = null;
        }
        if ($loginBy->permission->permission_view == 'user') {
            $company_id = $loginBy->branch->company_id;
            $branch_id = $loginBy->branch_id;
            $user_id = $loginBy->user_id;
        }
        return [
            'login_id' => $loginBy->id,
            'login_by' => $loginBy,
            'login_permission_view' => $loginBy->permission->permission_view,
            'login_user_id' => $user_id,
            'login_branch_id' => $branch_id,
            'login_company_id' => $company_id,
        ];
    }


    public function getStockCountqty($item_id, $location_id)
    {
        // $QtyItem = Item_trans::where('item_id', $item_id);

        // if (!empty($location_id)) {
        //     $QtyItem->whereIn('location_1_id', $location_id);
        // }
        // $qtyItem = $QtyItem->where('status', 1)
        //     ->where('operation', 'finish')
        //     ->sum('qty');

        // return intval($qtyItem);
    }

    public function getStockBookingCount($item_id, $location_id)
    {
        // $QtyItem = Item_trans::where('item_id', $item_id);

        // if (!empty($location_id)) {
        //     $QtyItem->whereIn('location_1_id', $location_id);
        // }
        // $qtyItem = $QtyItem->where('status', 1)
        //     ->where('operation', 'booking')
        //     ->sum('qty');

        // return intval($qtyItem);
    }


    public function returnSuccess($massage, $data)
    {

        return response()->json([
            'code' => strval(200),
            'status' => true,
            'message' => $massage,
            'data' => $data,
        ], 200);
    }

    public function returnUpdate($massage)
    {
        return response()->json([
            'code' => strval(201),
            'status' => true,
            'message' => $massage,
            'data' => [],
        ], 201);
    }




    public function returnUpdateReturnData($massage, $data)
    {
        return response()->json([
            'code' => strval(201),
            'status' => true,
            'message' => $massage,
            'data' => $data,
        ], 201);
    }

    public function returnErrorData($massage, $code)
    {
        return response()->json([
            'code' => strval($code),
            'status' => false,
            'message' => $massage,
            'data' => [],
        ], $code);
    }

    public function errorData($massage, $error, $code)
    {
        return response()->json([
            'code'      => strval($code),
            'status'    => false,
            'message'   => $massage,
            'error'     => $error,
        ], $code);
    }

    public function returnError($massage)
    {
        return response()->json([
            'code' => strval(401),
            'status' => false,
            'message' => $massage,
            'data' => [],
        ], 401);
    }

    public function Log($userId, $description, $type)
    {
        $Log = new Log();
        $Log->user_id = $userId;
        $Log->description = $description;
        $Log->type = $type;
        $Log->save();
    }



    public function LogSaleOrder($userId, $description, $type)
    {
        // $Log = new Log_saleOrder();
        // $Log->user_id = $userId;
        // $Log->description = $description;
        // $Log->type = $type;
        // $Log->save();
    }
    public function sendMail($email, $data, $title, $type)
    {

        $mail = new SendMail($email, $data, $title, $type);
        Mail::to($email)->send($mail);
    }

    public function sendLine($line_token, $text)
    {

        $sToken = $line_token;
        $sMessage = $text;

        $chOne = curl_init();
        curl_setopt($chOne, CURLOPT_URL, "https://notify-api.line.me/api/notify");
        curl_setopt($chOne, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($chOne, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($chOne, CURLOPT_POST, 1);
        curl_setopt($chOne, CURLOPT_POSTFIELDS, "message=" . $sMessage);
        $headers = array('Content-type: application/x-www-form-urlencoded', 'Authorization: Bearer ' . $sToken . '');
        curl_setopt($chOne, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($chOne, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($chOne);

        curl_close($chOne);
    }


    public function uploadImage1(Request $request)
    {
        $image = $request->image;
        $path = $request->path;

        $input['imagename'] = md5(rand(0, 999999) . $image->getClientOriginalName()) . '.' . $image->extension();
        $destinationPath = public_path('/thumbnail');
        if (!File::exists($destinationPath)) {
            File::makeDirectory($destinationPath, 0777, true);
        }

        $img = Image::make($image->path());
        $img->save($destinationPath . '/' . $input['imagename']);
        $destinationPath = public_path($path);
        $image->move($destinationPath, $input['imagename']);


        return $this->returnSuccess('Successful operation', $path . $input['imagename']);
    }



    public function uploadImage($image, $path)
    {

        $input['imagename'] = md5(rand(0, 999999) . $image->getClientOriginalName()) . '.' . $image->extension();
        $destinationPath = public_path('/thumbnail');
        if (!File::exists($destinationPath)) {
            File::makeDirectory($destinationPath, 0777, true);
        }

        $img = Image::make($image->path());
        $img->save($destinationPath . '/' . $input['imagename']);
        $destinationPath = public_path($path);
        $image->move($destinationPath, $input['imagename']);

        return $path . $input['imagename'];
    }

    public function uploadFile($file, $path)
    {
        $input['filename'] = random_int(10, 1000000) . '.' . $file->extension();
        //dd($input['filename']);
        $destinationPath = public_path('/file_thumbnail');
        if (!File::exists($destinationPath)) {
            File::makeDirectory($destinationPath, 0777, true);
        }

        $destinationPath = public_path($path);
        $file->move($destinationPath, $input['filename']);

        return $path . $input['filename'];
    }

    public function uploadFile2(Request $request)
    {

        $file = $request->file;
        $path = $request->path;

        $input['filename'] = time() . '.' . $file->extension();

        $destinationPath = public_path('/file_thumbnail');
        if (!File::exists($destinationPath)) {
            File::makeDirectory($destinationPath, 0777, true);
        }

        $destinationPath = public_path($path);
        $file->move($destinationPath, $input['filename']);

        return $this->returnSuccess('Successful operation', $path . $input['filename']);
    }

    public function ImageUrlToImageBase64($url_image)
    {
        // // Get the image content from the URL
        // $imageContent = file_get_contents($url_image);

        // // Check if the image content was successfully fetched
        // if ($imageContent === FALSE) {
        //     return "Failed to fetch image content.";
        // }

        // // Get the mime type of the image
        // $finfo = new finfo(FILEINFO_MIME_TYPE);
        // $mimeType = $finfo->buffer($imageContent);

        // // Encode the image content to Base64
        // $base64Encoded = base64_encode($imageContent);

        // // Create the data URL for the image
        // $dataUrl = 'data:' . $mimeType . ';base64,' . $base64Encoded;

        // return $dataUrl;

        // Initialize cURL session
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url_image); // URL ของภาพ
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // คืนค่าเป็น String
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // รองรับการ Redirect
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // ตั้งเวลา Timeout

        // Execute the cURL request
        $imageContent = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return "Failed to fetch image content: $error";
        }

        // Close the cURL session
        curl_close($ch);

        // Check if the image content was successfully fetched
        if ($imageContent === false) {
            return "Failed to fetch image content.";
        }

        // Get the mime type of the image
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($imageContent);

        // Encode the image content to Base64
        $base64Encoded = base64_encode($imageContent);

        // Create the data URL for the image
        $dataUrl = 'data:' . $mimeType . ';base64,' . $base64Encoded;

        return $dataUrl;
    }

    public function generatePreviewToken($login_branch_id, $login_user_id, $round, $year, $month = null)
    {
        $payload = [
            'login_branch_id' => $login_branch_id,
            'login_user_id' => $login_user_id,
            'round' => $round,
            'year' => $year,
            'month' => $month,
            'exp' => Carbon::now()->addMinutes(10)->timestamp, // หมดอายุใน 10 นาที
            'nonce' => Str::random(10), // ป้องกัน token ซ้ำ
        ];

        return Crypt::encryptString(json_encode($payload));
    }

    public function verifyPreviewToken($token)
    {
        try {
            $decrypted = json_decode(Crypt::decryptString($token), true);

            if (!isset($decrypted['exp']) || Carbon::now()->timestamp > $decrypted['exp']) {
                return response()->json(['error' => 'Token expired'], 401);
            }

            $login_branch_id = $decrypted['login_branch_id'];
            $login_user_id = $decrypted['login_user_id'];
            $round = $decrypted['round'];
            $year = $decrypted['year'];
            $month = $decrypted['month'];

            // ใช้ $hr_employee_id และ $year ต่อได้เลย

            $data = [
                'login_branch_id' => $login_branch_id,
                'login_user_id' => $login_user_id,
                'round' => $round,
                'year' => $year,
                'month' => $month,
            ];

            return  $data;
        } catch (\Exception $e) {
            Log::error('Invalid token: ' . $e->getMessage());
            return  null;
            // return response()->json(['error' => 'Invalid token'], 401);
        }
    }

    public function getDropDownYear()
    {
        $Year = intval(((date('Y')) + 1) + 543);

        $data = [];

        for ($i = 0; $i < 10; $i++) {

            $Year = $Year - 1;
            $data[$i]['year'] = $Year;
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $data);
    }

    public function getDropDownProvince()
    {

        $province = array("กระบี่", "กรุงเทพมหานคร", "กาญจนบุรี", "กาฬสินธุ์", "กำแพงเพชร", "ขอนแก่น", "จันทบุรี", "ฉะเชิงเทรา", "ชลบุรี", "ชัยนาท", "ชัยภูมิ", "ชุมพร", "เชียงราย", "เชียงใหม่", "ตรัง", "ตราด", "ตาก", "นครนายก", "นครปฐม", "นครพนม", "นครราชสีมา", "นครศรีธรรมราช", "นครสวรรค์", "นนทบุรี", "นราธิวาส", "น่าน", "บุรีรัมย์", "บึงกาฬ", "ปทุมธานี", "ประจวบคีรีขันธ์", "ปราจีนบุรี", "ปัตตานี", "พะเยา", "พังงา", "พัทลุง", "พิจิตร", "พิษณุโลก", "เพชรบุรี", "เพชรบูรณ์", "แพร่", "ภูเก็ต", "มหาสารคาม", "มุกดาหาร", "แม่ฮ่องสอน", "ยโสธร", "ยะลา", "ร้อยเอ็ด", "ระนอง", "ระยอง", "ราชบุรี", "ลพบุรี", "ลำปาง", "ลำพูน", "เลย", "ศรีสะเกษ", "สกลนคร", "สงขลา", "สตูล", "สมุทรปราการ", "สมุทรสงคราม", "สมุทรสาคร", "สระแก้ว", "สระบุรี", "สิงห์บุรี", "สุโขทัย", "สุพรรณบุรี", "สุราษฎร์ธานี", "สุรินทร์", "หนองคาย", "หนองบัวลำภู", "อยุธยา", "อ่างทอง", "อำนาจเจริญ", "อุดรธานี", "อุตรดิตถ์", "อุทัยธานี", "อุบลราชธานี");

        $data = [];

        for ($i = 0; $i < count($province); $i++) {

            $data[$i]['province'] = $province[$i];
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $data);
    }

    public function getDownloadFomatImport($params)
    {

        $file = $params;
        $destinationPath = public_path() . "/fomat_import/";

        return response()->download($destinationPath . $file);
    }

    public function checkDigitMemberId($memberId)
    {

        $sum = 0;
        for ($i = 0; $i < 12; $i++) {

            $sum += (int) ($memberId[$i]) * (13 - $i);
        }

        if ((11 - ($sum % 11)) % 10 == (int) ($memberId[12])) {
            return 'true';
        } else {
            return 'false';
        }
    }

    public function setRunDoc($docId, $lastId)
    {

        $doc = Doc::find($docId);
        $doc->gen = $lastId;

        $doc->save();
    }


    public function getStockCount($item_id, $location_id)
    {

        // $QtyItem = Item_trans::where('item_id', $item_id);


        // if (!empty($location_id)) {
        //     $QtyItem->whereIn('location_1_id', $location_id);
        // }
        // $qtyItem = $QtyItem->where('status', 1)
        //     ->sum('qty');

        // // dd($qtyItem);
        // return intval($qtyItem);
    }

    public function getItemCount($item_id, $main_item_id)
    {

        // $QtyItem = Item_line::where('item_id', $item_id);


        // if (!empty($main_item_id)) {
        //     $QtyItem->whereIn('main_item_id', $main_item_id);
        // }
        // $qtyItem = $QtyItem
        //     ->sum('qty');

        // // dd($qtyItem);
        // return intval($qtyItem);
    }


    public function getLastNumber($docId)
    {
        $doc = Doc::find($docId);
        //dd($doc);
        if ($doc->gen) {

            //prefix
            if ($doc->prefix) {
                $Prefix = $doc->prefix;
            } else {
                $Prefix = '';
            }

            //date
            if ($doc->date) {

                if ($doc->date == 'YY') {
                    $Date = date('y');
                } else if ($doc->date == 'YYMM') {
                    $Date = date('ym');
                } else if ($doc->date == 'YYMMDD') {
                    $Date = date('ymd');
                }
            } else {
                $Date = '';
            }

            //run number
            if ($doc->run_number) {

                $countPrefix = strlen($doc->prefix);
                $countRunNumber = strlen($doc->run_number);

                $lastDate = substr($doc->gen, $countPrefix, -$countRunNumber);

                //check date
                if ($Date > $lastDate) {

                    if ($doc->prefix == 'SO') {
                        $lastNumber = 000;
                    } else {
                        $lastNumber = 00;
                    }

                    $newNumber = intval($lastNumber) + 1;
                    $Number = sprintf('%0' . strval($countRunNumber) . 'd', $newNumber);
                } else {

                    $lastNumber = substr($doc->gen, -$countRunNumber);
                    $newNumber = intval($lastNumber) + 1;
                    $Number = sprintf('%0' . strval($countRunNumber) . 'd', $newNumber);
                }
            } else {
                $Number = null;
            }
        } else {

            //case new gen

            //prefix
            if ($doc->prefix) {
                $Prefix = $doc->prefix;
            } else {
                $Prefix = '';
            }

            //date
            if ($doc->date) {

                if ($doc->date == 'YY') {
                    $Date = date('Y');
                } else if ($doc->date == 'YYMM') {
                    $Date = date('Ym');
                } else if ($doc->date == 'YYMMDD') {
                    $Date = date('Ymd');
                }
            } else {
                $Date = '';
            }

            // dd($date);

            if ($doc->run_number) {
                $runNumber = intval($doc->run_number) + 1;
                $countZero = Strlen($doc->run_number);
                $Number = sprintf('%0' . strval($countZero) . 'd', $runNumber);
            } else {
                $Number = null;
            }
        }

        //format
        $prefix = $Prefix;
        $date = $Date;
        $run_number = $Number;

        //gen
        $gen = $prefix . $date . '-' . $run_number;

        return $gen;
    }

    public function dateBetween($dateStart, $dateStop)
    {
        $datediff = strtotime($dateStop) - strtotime($dateStart);
        return abs($datediff / (60 * 60 * 24));
    }

    public function dateform($date)
    {
        $d = explode('/', $date);
        return $d[2] . '-' . $d[1] . '-' . $d[0];
    }


    public function dateThaiAbb($strDate)
    {
        list($year, $month) = $strDate ? explode('-', substr($strDate, 0, 7)) : [null, null];

        $months_thai = [
            '01' => "ม.ค.",
            '1' => "ม.ค.",
            '02' => "ก.พ.",
            '2' => "ก.พ.",
            '03' => "มี.ค.",
            '3' => "มี.ค.",
            '04' => "เม.ย.",
            '4' => "เม.ย.",
            '05' => "พ.ค.",
            '5' => "พ.ค.",
            '06' => "มิ.ย.",
            '6' => "มิ.ย.",
            '07' => "ก.ค.",
            '7' => "ก.ค.",
            '08' => "ส.ค.",
            '8' => "ส.ค.",
            '09' => "ก.ย.",
            '9' => "ก.ย.",
            '10' => "ต.ค.",
            '11' => "พ.ย.",
            '12' => "ธ.ค.",
        ];

        $month_name_thai = $months_thai[$month] ?? null;
        $year = $year ? ($year + 543) : null;

        return $month_name_thai && $year ? "{$month_name_thai} {$year}" : null;
    }




    // public function getCommisionUser($user_id, $round)
    // {
    //     [$year, $month] = explode('-', $round);

    //     $date_start = Carbon::create($year, $month)->subMonth()->day(26)->toDateString();    // กำหนดวันที่เริ่มต้น: วันที่ 26 ของเดือนก่อนหน้า
    //     $date_end = Carbon::create($year, $month)->day(25)->toDateString();    // กำหนดวันที่สิ้นสุด: วันที่ 25 ของเดือนใน $round

    //     $config = $this->getConfig();

    //     $total = 0;

    //     if ($config->commission_calculate == 'sales') {
    //         //จากยอดขาย

    //         //ยอดขาย
    //         $Sale_order =  Sale_order::where('sale_id', $user_id)
    //             // ->where('date_time', 'like', '%' . $round . '-%')
    //             ->where('date_time', '>=', $date_start)
    //             ->where('date_time', '<=', $date_end)
    //             ->where('status', 'finish')
    //             ->sum('total');

    //         $total = $Sale_order;
    //     } else {
    //         //จากกำไร

    //         //ยอดขาย
    //         $Sale_order =  Sale_order::where('sale_id', $user_id)
    //             // ->where('date_time', 'like', '%' . $round . '-%')
    //             ->where('date_time', '>=', $date_start)
    //             ->where('date_time', '<=', $date_end)
    //             ->where('status', 'finish')
    //             ->sum('total');

    //         //ต้นทุน
    //         $Sale_order_line =  Sale_order_line::with('item')
    //             ->with('sale_order')
    //             ->WhereHas('sale_order', function ($query) use ($user_id, $round, $date_start, $date_end) {
    //                 $query->where('sale_id', $user_id);
    //                 // $query->where('date_time', 'like', '%' . $round . '-%');
    //                 $query->where('date_time', '>=', $date_start);
    //                 $query->where('date_time', '<=', $date_end);
    //                 $query->where('status', 'finish');
    //             })
    //             ->get();


    //         $totalCost = 0;
    //         for ($i = 0; $i < count($Sale_order_line); $i++) {

    //             if ($Sale_order_line[$i]->item) {
    //                 $totalCost += $Sale_order_line[$i]->item->unit_cost;
    //             }
    //         }
    //         $total = $Sale_order - $totalCost;
    //     }

    //     //check commision
    //     $Commission =  Commission::where('start', '<=', $total)
    //         ->where('position_id', $user_id)
    //         ->orderby('start', 'desc')
    //         ->first();

    //     if ($Commission) {
    //         $percentCom = $Commission->percent;
    //     } else {
    //         $percentCom = 0;
    //     }

    //     $com = $total * $percentCom;
    //     return round($com, 2);
    // }

    public function calculateOtUser($user, $ot_type, $qty_hour, $qty_type)
    {
        if ($qty_type == "amount") {
            if ($ot_type->amount == 0) {
                $ot = 0;
            } else {
                $ot = ($user->salary / 30 / 8) + $ot_type->amount * $qty_hour;
            }
            return $ot;
        } else if ($qty_type == "rate") {
            if ($ot_type->rate == 0) {
                $ot = 0;
            } else {
                $ot = ($user->salary / 30 / 8) * $ot_type->rate * $qty_hour;
            }
            return $ot;
        } else {
            return null;
        }
    }

    public function getOtUser($user_id, $date_start = null, $date_end = null)
    {

        $Ot =  Ot::where('user_id', $user_id)
            // ->where('date', 'like', '%' . $round . '-%')
            ->where('date', '>=', $date_start)
            ->where('date', '<=', $date_end)
            ->where('status', 'approved')
            ->sum('qty');

        return round($Ot, 2);
    }

    public function calculateYellowCard($type, $qty_hour, $leave_type_id)
    {
        if ($type == 'leave') {
            $LeaveType = LeaveType::find($leave_type_id);
            $YellowCard = ($qty_hour * $LeaveType->yellow_card) / 8;
        }
        if ($type == 'miss') {
            $YellowCard = ($qty_hour * 16) / 8;
        }

        if ($type == 'late') {
            $YellowCard = ($qty_hour * 1);
        }


        return round($YellowCard, 2);
    }

    public function dateInPeriod($start, $end)
    {

        $output = array();

        $days = floor((strtotime($end) - strtotime($start)) / 86400);

        for ($i = 0; $i <= $days; $i++) {

            array_push($output, date("Y-m-d", strtotime("+" . $i . " day", strtotime($start))));
        }

        return $output;
    }

    public function getWithdrawSalaryUser($user_id, $date_start, $date_end)
    {
        $Withdraw_salary =  Withdraw_salary::where('user_id', $user_id)
            // ->where('month_withdraw', 'like', '%' . $ym . '%')
            ->where('date', '>=', $date_start)
            ->where('date', '<=', $date_end)
            ->where('status', 'approved')
            ->sum('qty');

        return round($Withdraw_salary, 2);
    }


    public function TimeDiff($time1, $time2)
    {
        //Convert date to formate Timpstamp
        $time1 = strtotime($time1);
        $time2 = strtotime($time2);

        //$diffdate=$time1-$time2
        $distanceInSeconds = round($time2 - $time1); //จะได้เป็นวินาที

        $distanceInMinutes = round($distanceInSeconds / 60); //แปลงจากวินาทีเป็นนาที

        $days = floor(abs($distanceInMinutes / 1440));
        $hours = floor(fmod($distanceInMinutes, 1440) / 60);
        $minutes = floor(fmod($distanceInMinutes, 60));

        return $hours;
    }

    public function dateDiff($time1, $time2)
    {
        //Convert date to formate Timpstamp
        $time1 = strtotime($time1);
        $time2 = strtotime($time2);

        //$diffdate=$time1-$time2
        $distanceInSeconds = round(abs($time2 - $time1)); //จะได้เป็นวินาที

        $distanceInMinutes = round($distanceInSeconds / 60); //แปลงจากวินาทีเป็นนาที

        $days = floor(abs($distanceInMinutes / 1440));
        $hours = floor(fmod($distanceInMinutes, 1440) / 60);
        $minutes = floor(fmod($distanceInMinutes, 60));

        return $days;
    }

    public function yearDiff($time1, $time2)
    {

        $startDate = new DateTime($time1);
        $endDate = new DateTime($time2);

        $interval = $startDate->diff($endDate);
        $diffInYears = $interval->y;

        return $diffInYears;
    }


    public function checkLeavePermissionUser($user_id, $qty_hour, $leave_type_id, $qty_hour_work_shift, $year)
    {
        $qty_leave_day = $qty_hour;


        // $User = User::find($user_id);
        // $User->work_day = $this->dateDiff(date('Y-m-d'), $User->register_date);
        // $work_year = intval($User->work_day / 365);

        // //permission
        // $Leave_permission = Leave_permission::where('year', '<=',  $work_year)
        //     ->where('leave_type_id', $leave_type_id)
        //     ->orderby('year', 'desc')
        //     ->first();

        $Leave_permission = User_leave_permission::with('leave_type')
            ->where('user_id', $user_id)
            ->where('year', $year)
            ->where('leave_type_id', $leave_type_id)
            ->first();

        if ($Leave_permission) {
            $qtyHourPermission = $Leave_permission->qty * $qty_hour_work_shift;
        } else {
            $qtyHourPermission = 0;
        }

        //use
        $qty_hour_use = Leave_table::where('user_id', $user_id)
            ->where('created_at', '>=', date('Y') . '-01-01' . ' 00:00:00')
            ->where('created_at', '<=', date('Y') . '-12-25' . ' 23:23:59')
            ->where('leave_type_id', $leave_type_id)
            ->where('status', 'approved')
            ->sum('qty_hour');

        $check = ($qtyHourPermission - $qty_hour_use) - $qty_leave_day;
        $qty_day = intval($check / $qty_hour_work_shift);

        return $qty_day;
    }

    // public function qtyLeavePermissionUser($user_id, $leave_type_id)
    // {

    //     $User = User::find($user_id);
    //     $User->work_day = $this->dateDiff(date('Y-m-d'), $User->register_date);
    //     $work_year = intval($User->work_day / 365);

    //     //permission
    //     $Leave_permission = Leave_permission::where('year', '<=',  $work_year)
    //         ->where('leave_type_id', $leave_type_id)
    //         ->orderby('year', 'desc')
    //         ->first();

    //     if ($Leave_permission) {
    //         $qtyHourPermission = $Leave_permission->qty * 24;
    //     } else {
    //         $qtyHourPermission = 0;
    //     }
    //     // dd($qtyHourPermission);

    //     //use
    //     $qty_hour_use = Leave_table::where('user_id', $user_id)
    //         ->where('created_at', '>=', date('Y') . '-01-01' . ' 00:00:00')
    //         ->where('created_at', '<=', date('Y') . '-12-25' . ' 23:23:59')
    //         ->where('leave_type_id', $leave_type_id)
    //         ->where('status', 'approved')
    //         ->sum('qty_hour');

    //     $check = ($qtyHourPermission - $qty_hour_use);

    //     $data = [
    //         'day_all' => $this->convertHoursToDaysAndHours($qtyHourPermission),
    //         'day_use' => $this->convertHoursToDaysAndHours($qty_hour_use),
    //         'day_diff' => $this->convertHoursToDaysAndHours($check),
    //     ];
    //     return $data;
    // }

    public function qtyLeavePermissionUser($user_id, $leave_type_id, $qty, $qty_hour_work_shift = null)
    {

        $qtyHourPermission = $qty * $qty_hour_work_shift;

        //use
        $qty_hour_use = Leave_table::where('user_id', $user_id)
            ->where('created_at', '>=', date('Y') . '-01-01' . ' 00:00:00')
            ->where('created_at', '<=', date('Y') . '-12-25' . ' 23:23:59')
            ->where('leave_type_id', $leave_type_id)
            ->where('status', 'approved')
            ->sum('qty_hour');

        $check = ($qtyHourPermission - $qty_hour_use);

        $data = [
            'day_all' => $this->convertHoursToDaysAndHours($qtyHourPermission, $qty_hour_work_shift),
            'day_use' => $this->convertHoursToDaysAndHours($qty_hour_use, $qty_hour_work_shift),
            'day_diff' => $this->convertHoursToDaysAndHours($check, $qty_hour_work_shift),
        ];
        return $data;
    }

    public function convertHoursToDaysAndHours($hours, $qty_hour_work_shift)
    {
        $qty_hour_work_shift = ($qty_hour_work_shift == 0 ? 1 : $qty_hour_work_shift);
        $days = floor($hours / $qty_hour_work_shift); // หาจำนวนวัน
        $remainingHours = $hours % $qty_hour_work_shift; // หาจำนวนชั่วโมงที่เหลือหลังจากหารด้วย 8

        // สร้างข้อความผลลัพธ์
        $result = "";
        if ($days > 0) {
            $result .= $days . " วัน ";
        }
        if ($remainingHours > 0) {
            $result .= $remainingHours . " ชั่วโมง";
        }

        if (!$result) {
            $result .=  "0 วัน ";
        }

        return $result;
    }

    public function genBarcodeNumber()
    {

        // Specify the desired barcode length
        $barcodeLength = 12;

        // Generate a random barcode number
        $barcodeNumber = '';
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

        for ($i = 0; $i < $barcodeLength; $i++) {
            $barcodeNumber .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $barcodeNumber;
    }

    public function genCodeOrder(Model $model, $prefix, $number)
    {

        $countPrefix = strlen($prefix);
        $countRunNumber = strlen($number);

        //get last code
        $m = $model::orderby('order_id', 'desc')
            ->first();
        if ($m) {
            $lastCode = $m->order_id;
        } else {
            $lastCode = $prefix . $number;
        }


        $codelast = substr($lastCode, -$countRunNumber);

        $newNumber = intval($codelast) + 1;
        $Number = sprintf('%0' . strval($countRunNumber) . 'd', $newNumber);

        $runNumber = $prefix . date('y') . date('m') . '-' . $Number;

        return $runNumber;
    }

    public function getConfig($branch_id)
    {
        $Config = Config::where('branch_id', $branch_id)->first();

        return $Config;
    }

    public function parseAddress($address)
    {
        $name = '';
        $phone = '';
        $subDistrict = '';
        $district = '';
        $province = '';
        $zipcode = '';
        $rawAddress = $address;

        // จับคู่ชื่อ
        if (preg_match('/^\d+\s+(.+?)\s*\(/', $address, $nameMatch) || preg_match('/^(.*?)\s*\(/', $address, $nameMatch)) {
            $name = trim($nameMatch[1]);
            $rawAddress = str_replace($nameMatch[0], '', $rawAddress);
        }

        // จับคู่เบอร์โทรศัพท์
        if (preg_match('/(?:โทร|เบอร์โทร|โทร.)?\s*(\d{3}[-]?\d{3}[-]?\d{4})/', $address, $phoneMatch)) {
            $phone = str_replace('-', '', trim($phoneMatch[1]));
            $rawAddress = str_replace($phoneMatch[0], '', $rawAddress);
        } elseif (preg_match('/\b\d{10}\b/', $address, $phoneDirectMatch)) {
            $phone = trim($phoneDirectMatch[0]);
            $rawAddress = str_replace($phoneDirectMatch[0], '', $rawAddress);
        }

        // จับคู่รหัสไปรษณีย์
        if (preg_match('/\b\d{5}\b/', $address, $postalCodeMatch)) {
            $zipcode = trim($postalCodeMatch[0]);
            $rawAddress = str_replace($postalCodeMatch[0], '', $rawAddress);
        }

        // จับคู่จังหวัด
        if (preg_match('/(?:จ\.|จังหวัด)\s*(\S+)/', $address, $provinceMatch)) {
            $province = trim($provinceMatch[1]);
            $rawAddress = str_replace($provinceMatch[0], '', $rawAddress);
        }

        // จับคู่อำเภอ
        if (preg_match('/(?:อ\.|อำเภอ)\s*(\S+)/', $address, $districtMatch)) {
            $district = trim($districtMatch[1]);
            $rawAddress = str_replace($districtMatch[0], '', $rawAddress);
        } elseif (preg_match('/(?:เขต\.|เขต)\s*(\S+)/', $address, $districtsMatch)) {
            $district = trim($districtsMatch[1]);
            $rawAddress = str_replace($districtsMatch[0], '', $rawAddress);
        }

        // จับคู่ตำบล
        if (preg_match('/(?:ต\.|ตำบล)\s*(\S+)/', $address, $subDistrictMatch)) {
            $subDistrict = trim($subDistrictMatch[1]);
            $rawAddress = str_replace($subDistrictMatch[0], '', $rawAddress);
        } elseif (preg_match('/(?:แขวง\.|แขวง)\s*(\S+)/', $address, $subDistrictsMatch)) {
            $subDistrict = trim($subDistrictsMatch[1]);
            $rawAddress = str_replace($subDistrictsMatch[0], '', $rawAddress);
        }

        // ทำความสะอาดที่อยู่
        $cleanedAddress = trim(preg_replace([
            '/^\d+\s*/',
            '/ระหัส\s?ไปรษณี/',
            '/เลขไปรษณีย์\s?/',
            '/\([^)]*\)/',
            '/\s*\([^)]*\)/',
            '/[^\s]+\)\s*/'
        ], '', $rawAddress));

        return [
            'name' => $name,
            'address' => $cleanedAddress,
            'phone' => $phone,
            'subDistrict' => $subDistrict,
            'district' => $district,
            'province' => $province,
            'zipcode' => $zipcode,
        ];
    }

    public function cutTextOrder(Request $request)
    {

        $chat_message = $request->chat_message;

        // ข้อความที่ได้รับจากลูกค้าผ่านแชท
        // $chat_message = "ที่อยู่ 123 ถนนสุขุมวิท ชื่อ John Doe โทร 0812345678";

        // ใช้ regular expression เพื่อแยกข้อมูล
        $pattern = '/(?:ชื่อ\s*(.*?)\s*โทร\s*(\d+)\s*ที่อยู่\s*(.*?)$)|(?:ชื่อ\s*(.*?)\s*ที่อยู่\s*(.*?)\s*โทร\s*(\d+)$)|(?:ที่อยู่\s*(.*?)\s*ชื่อ\s*(.*?)\s*โทร\s*(\d+)$)|(?:ที่อยู่\s*(.*?)\s*โทร\s*(\d+)\s*ชื่อ\s*(.*?)$)|(?:โทร\s*(\d+)\s*ชื่อ\s*(.*?)\s*ที่อยู่\s*(.*?)$)|(?:โทร\s*(\d+)\s*ที่อยู่\s*(.*?)\s*ชื่อ\s*(.*?)$)/';
        preg_match($pattern, $chat_message, $matches);

        // สร้างตัวแปรเพื่อเก็บข้อมูล
        $name = "";
        $address = "";
        $phone_number = "";

        // ตรวจสอบและกำหนดค่าข้อมูลตามลำดับ
        if (!empty($matches[1])) {
            $name = $matches[1];
            $phone_number = $matches[2];
            $address = $matches[3];
        } elseif (!empty($matches[4])) {
            $name = $matches[4];
            $address = $matches[5];
            $phone_number = $matches[6];
        } elseif (!empty($matches[7])) {
            $address = $matches[7];
            $name = $matches[8];
            $phone_number = $matches[9];
        } elseif (!empty($matches[10])) {
            $address = $matches[10];
            $phone_number = $matches[11];
            $name = $matches[12];
        } elseif (!empty($matches[13])) {
            $phone_number = $matches[13];
            $name = $matches[14];
            $address = $matches[15];
        } elseif (!empty($matches[16])) {
            $phone_number = $matches[16];
            $address = $matches[17];
            $name = $matches[18];
        }


        $data = [
            'name' => $name,
            'address' => $address,
            'phone' => $phone_number
        ];

        return $this->returnSuccess('Successful', $data);
    }

    public function convertToThaiText($number)
    {
        $txtnum1 = ['ศูนย์', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า', 'สิบ'];
        $txtnum2 = ['', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน'];
        $number = str_replace([",", " ", "บาท"], "", $number);
        $number = explode(".", $number);
        if (sizeof($number) > 2) {
            return 'ข้อมูลไม่ถูกต้อง';
        }
        $strlen = strlen($number[0]);
        $convert = '';
        for ($i = 0; $i < $strlen; $i++) {
            $n = substr($number[0], $i, 1);
            if ($n != 0) {
                if ($i == ($strlen - 1) && $n == 1) {
                    $convert .= 'เอ็ด';
                } elseif ($i == ($strlen - 2) && $n == 2) {
                    $convert .= 'ยี่';
                } elseif ($i == ($strlen - 2) && $n == 1) {
                    $convert .= '';
                } else {
                    $convert .= $txtnum1[$n];
                }
                $convert .= $txtnum2[$strlen - $i - 1];
            }
        }

        $convert .= 'บาท';
        if (empty($number[1]) || $number[1] == '00') {
            $convert .= 'ถ้วน';
        } else {
            $strlen = strlen($number[1]);
            for ($i = 0; $i < $strlen; $i++) {
                $n = substr($number[1], $i, 1);
                if ($n != 0) {
                    if ($i == ($strlen - 1) && $n == 1) {
                        $convert .= 'เอ็ด';
                    } elseif ($i == ($strlen - 2) && $n == 2) {
                        $convert .= 'ยี่';
                    } elseif ($i == ($strlen - 2) && $n == 1) {
                        $convert .= '';
                    } else {
                        $convert .= $txtnum1[$n];
                    }
                    $convert .= $txtnum2[$strlen - $i - 1];
                }
            }
            $convert .= 'สตางค์';
        }
        return $convert;
    }

    // public function sale_order_log($loginBy, $type, $remark, $sale_order_id, $work_telesales_id, $sale_order_line_id)
    // {
    //     $TeleSalesTopUpLog = new TeleSalesTopUpLog();
    //     $TeleSalesTopUpLog->sale_order_id = $sale_order_id;
    //     $TeleSalesTopUpLog->work_telesales_id = $work_telesales_id;
    //     $TeleSalesTopUpLog->sale_order_line_id = $sale_order_line_id;
    //     $TeleSalesTopUpLog->user_id = $loginBy->id;
    //     $TeleSalesTopUpLog->date = date("Y-m-d");
    //     $TeleSalesTopUpLog->topup_status = $type;
    //     $TeleSalesTopUpLog->remark = $remark;
    //     $TeleSalesTopUpLog->save();
    // }


    // public function getLocation01()
    // {
    //     $location = Location::where('code', '01')->first();
    //     return $location->id;
    // }

    public function getLocationSCRAP()
    {
        // $location = Location::where('code', 'SCRAP')->first();
        // return $location->id;
    }


    public function isHoliday($date)
    {
        $isHoliday = false;
        $Holiday = Holiday::where('date', $date)->first();
        if ($Holiday) {
            $isHoliday = true;
        }
        return $isHoliday;
    }


    public function withUser($query, $search)
    {

        $searchable = (new User())->getTableColumns();

        $query->orWhereHas('user', function ($query) use ($search, $searchable) {

            $query->Where(function ($query) use ($search, $searchable) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $searchable) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });
            });
        });

        return $query;
    }

    public function withPosition($query, $search)
    {

        $searchable = (new Position())->getTableColumns();

        $query->orWhereHas('position', function ($query) use ($search, $searchable) {

            $query->Where(function ($query) use ($search, $searchable) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $searchable) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });

                // // search with
                // $query = $this->withProvince($query, $search);
            });
        });

        return $query;
    }


    public function withAmphure($query, $search)
    {

        $searchable = (new Amphure())->getTableColumns();

        $query->orWhereHas('amphure', function ($query) use ($search, $searchable) {

            $query->Where(function ($query) use ($search, $searchable) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $searchable) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });

                // search with
                $query = $this->withProvince($query, $search);
            });
        });

        return $query;
    }

    public function withProvince($query, $search)
    {

        $searchable = (new Province())->getTableColumns();

        $query->orWhereHas('province', function ($query) use ($search, $searchable) {

            $query->Where(function ($query) use ($search, $searchable) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $searchable) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });
            });
        });

        return $query;
    }

    // public function withSaleOrder($query, $search)
    // {

    //     $searchable = (new Sale_order())->getTableColumns();

    //     $query->orWhereHas('sale_order', function ($query) use ($search, $searchable) {

    //         $query->Where(function ($query) use ($search, $searchable) {

    //             //search datatable
    //             $query->orWhere(function ($query) use ($search, $searchable) {
    //                 foreach ($searchable as &$c) {
    //                     $query->orWhere($c, 'like', '%' . $search['value'] . '%');
    //                 }
    //             });

    //             // // search with
    //             // $query = $this->withSaleOrder($query, $search);
    //         });
    //     });

    //     return $query;
    // }

    // public function withVendor($query, $search)
    // {

    //     $searchable = (new Vendor())->getTableColumns();

    //     $query->orWhereHas('vendor', function ($query) use ($search, $searchable) {

    //         $query->Where(function ($query) use ($search, $searchable) {

    //             // search datatable
    //             $query->orWhere(function ($query) use ($search, $searchable) {
    //                 foreach ($searchable as &$s) {
    //                     $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
    //                 }
    //             });
    //         });
    //     });

    //     return $query;
    // }


    // public function ConfigStock()
    // {
    //     $Config_stock = Config_stock::first();
    //     return $Config_stock;
    // }


    // public function getStockControlByItemType($itemTypeId)
    // {
    //     $Stock_control = Stock_control::select('ua.user_id as approver_id', 'ua.name as approver_name', 'ua.signature as approver_signature'
    //         , 'um.user_id as manager_id', 'um.name as manager_name', 'um.signature as manager_signature')
    //         ->leftjoin('item_type as it', 'it.id', 'stock_control.item_type_id')
    //         ->leftjoin('users as ua', 'ua.id', 'stock_control.approver_id')
    //         ->leftjoin('users as um', 'um.id', 'stock_control.manager_id')
    //         ->where('it.id', $itemTypeId)
    //         ->first();

    //     if ($Stock_control) {

    //         $Stock_control->approver_signature = url($Stock_control->approver_signature);
    //         $Stock_control->manager_signature = url($Stock_control->manager_signature);
    //     }

    //     return $Stock_control;
    // }
    // public function dateBetween($dateStart, $dateStop)
    // {
    //     $datediff = strtotime($dateStop) - strtotime($this->dateform($dateStart));
    //     return abs($datediff / (60 * 60 * 24));
    // }

    // public function log_noti($Title, $Description, $Url, $Pic, $Type)
    // {
    //     $log_noti = new Log_noti();
    //     $log_noti->title = $Title;
    //     $log_noti->description = $Description;
    //     $log_noti->url = $Url;
    //     $log_noti->pic = $Pic;
    //     $log_noti->log_noti_type = $Type;

    //     $log_noti->save();
    // }


    // เบี้ยขยัน
    public function checkBonusStep($payroll_id, $user_id, $branch_id, $year, $month, $loginBy)
    {

        $User = User::where('work_shift_group_id', '!=', 2)
            ->find($user_id);
        if (!$User) {
            return 0;
        }

        // ถ้าเป็นปีใหม่ รีเซ็ตเบี้ยขยัน = 0 ถ้าปีเปลี่ยน
        $currentYear = now()->year;
        //  $currentYear = 2026;

        if ($User->bonus_year != $currentYear) {
            $User->bonus_steps = 0;
            $User->last_bonus_month = null;
            $User->bonus_year = $currentYear;
            $User->save();
        }


        // ดึงเดือน ปี
        $month = $month ?? now()->subMonth()->month; // เดือนที่แล้ว
        $year = $year ?? now()->subMonth()->year;   // get ปี เดือนที่แล้ว

        $start = Carbon::createFromDate($year, $month, 1)->startOfMonth(); // เริ่มวันที่ 1 ของเดือน
        $end = Carbon::createFromDate($year, $month, 1)->endOfMonth();     // วันสุดท้ายของเดือน


        // นับจำนวนวันเข้างานของ user
        $attendanceCount = User_attendance::where('user_id', $user_id)
            ->whereBetween('date', [$start, $end])
            ->count();

        // ถ้า มีวันเดียวที่ไม่ใช่ normal = ไม่ได้เบี้ยขยัน
        $hasOnlyNormal = !User_attendance::where('user_id', $user_id)
            ->whereBetween('date', [$start, $end])
            ->where('type', '!=', 'normal')
            ->exists();


        $getBonus = $attendanceCount > 0 && $hasOnlyNormal;

        // dd($getBonus, $user_id, $attendanceCount, $hasOnlyNormal);

        $bonusAmount = 0;
        DB::beginTransaction();
        try {

            if ($getBonus && $User) {
                // ได้เบี้ยขยัน


                // เช้คว่าเดือนนี้ได้เบี้ยขยันไปยัง
                if ($User->last_bonus_month != $month || $User->bonus_year != $currentYear) {

                    // ถ้าเดือนนี้ยังไม่ได้รับเบี้ยขยัน
                    $maxStep = BonusStep::where('branch_id', $branch_id)->max('step'); // หา step สูงสุดจาก BonusStep

                    if ($User->bonus_steps < $maxStep) {
                        $User->bonus_steps += 1; // บวกเพิ่ม ถ้ายังไม่ถึงขั้นสุด
                    }

                    $User->last_bonus_month = $month;
                    $User->bonus_year = $currentYear;
                    $User->save();
                }



                // คำนวณเบี้ยขยัน ตามขั้น
                $BonusStep = BonusStep::where('step', $User->bonus_steps)
                    ->where('branch_id', $branch_id)
                    ->first();

                // เพิ่มเข้าไปใน IncomePaid เงินได้ที่ต้องจ่าย
                $getid_type = IncomeType::firstOrCreate(
                    [
                        'name' => 'เบี้ยขยัน',
                        'branch_id' => $User->branch_id

                    ], // หา
                    [                             // สร้าง ถ้าไม่เจอ
                        'description' => 'ไม่ขาด ลา มาสาย',
                        'view_in_slip' => true,
                        'type' => 'Once'
                    ]
                );

                $IncomePaid = new IncomePaid();
                $IncomePaid->payroll_id = $payroll_id;
                $IncomePaid->user_id = $user_id;
                $IncomePaid->income_type_id = $getid_type->id;
                $IncomePaid->year =  $year;
                $IncomePaid->month =  $month;
                $IncomePaid->price = $BonusStep->amount;
                $IncomePaid->description =  $getid_type->name;

                $IncomePaid->create_by = $loginBy;
                $IncomePaid->save();

                $bonusAmount = $BonusStep->amount;
            } else {

                // ไม่ได้เบี้ยขยัน รีเซ็ต
                $User->bonus_steps = 0;
                $User->last_bonus_month = null;
                $User->save();

                $bonusAmount = 0;
            }


            DB::commit();

            return $bonusAmount;  // ส่งจำนวนเงินเบี้ยขยัน

        } catch (\Exception $e) {
            DB::rollBack();
            // dd($e->getMessage());
            return null;
        }
    }


    // เงินประกันการทำงาน
    public function checkdeductBond($payroll_id, $user_id, $branch_id, $year, $month, $loginBy)
    {


        //  ดึง user  ที่ต้องจ่ายเงินประกันการทำงาน
        $User = User::where('id', $user_id)
            ->where('is_bond_complete', 0)      // สถานะ ยังไม่จ่ายเงินประกัน ไม่ครบ
            ->where(function ($q) {
                $q->whereNull('bond_balance')
                    ->orWhere('bond_balance', '>=', 0);  // ต้องมีเงินประกันคงเหลือ
            })
            ->where('work_shift_group_id', '!=', 2)  // ไม่ใช่ผู้บริหาร
            ->first();


        $bondAmount = ['type' => null, 'amount' => 0];
        DB::beginTransaction();
        try {

            if ($User) {

                // get config
                $config = Config::where('branch_id', $branch_id)->first();

                // เช้คว่า  user มีจ่ายเงินประกันการทำงานมั้ย จ่ายครบยัง
                // $isBondComplete = BondLog::where('user_id', $user_id)
                //     ->sum('deduct_amount') >= $config->bond_required;

                $latest = BondLog::where('user_id', $user_id)
                    ->orderByDesc('id')
                    ->first();

                // มี log และยอด balance = 0 แปลว่าจ่ายครบ
                $isBondComplete = $latest && $latest->balance == 0;



                // -------------- จ่ายครบแล้ว  -------------------
                if ($isBondComplete) {

                    // บันทึกการคืนเงินประกัน
                    $BondLog = new BondLog();
                    $BondLog->user_id = $user_id;
                    $BondLog->year = $year;
                    $BondLog->month = $month;
                    $BondLog->total_amount = $config->bond_required;
                    $BondLog->deduct_amount = 0; // แสดงว่าไม่หัก แต่ log ไว้
                    $BondLog->balance = 0;
                    $BondLog->type = 'refund';
                    $BondLog->save();


                    // เปลี่ยนสถานะ เป็นจ่ายครบ
                    $User->is_bond_complete = 1;
                    $User->bond_balance = 0;
                    $User->save();


                    // คืนเงินประกันการทำงาน
                    $getid_type = IncomeType::firstOrCreate(
                        [
                            'name' => 'คืนเงินประกันการทำงาน',
                            'branch_id' => $User->branch_id
                        ], // หา

                        [                             // สร้าง ถ้าไม่เจอ
                            'description' => 'คืนเงินประกันการทำงาน',
                            'view_in_slip' => false,
                            'type' => 'Once'
                        ]
                    );


                    // เพิ่มกลับไป เงินได้
                    $IncomePaid = new IncomePaid();
                    $IncomePaid->payroll_id = $payroll_id;
                    $IncomePaid->user_id = $user_id;
                    $IncomePaid->income_type_id = $getid_type->id;
                    $IncomePaid->year = $year;
                    $IncomePaid->month = $month;
                    $IncomePaid->price = $config->bond_required;
                    $IncomePaid->description =  $getid_type->name;

                    $IncomePaid->create_by = $loginBy;
                    $IncomePaid->save();


                    $bondAmount = ['type' => 'income', 'amount' => $config->bond_required];
                }


                // ------------ ยังไม่ครบ ----------------
                else {

                    $deductAmount = $config->bond_deduct_per_month; // จำนวนหักจ่าย


                    // เช้คว่า เดือนนี้ ปีนี้ user นี้ มีจ่ายไปยัง
                    $bondLog = BondLog::where('user_id', $user_id)
                        ->where('year', $year)
                        ->where('month', $month)
                        ->orderBy('id', 'desc') // ตัวล่าสุด
                        ->first();



                    // ยังไม่เคยจ่ายในเดือนนี้ ปีนี้
                    if (!$bondLog) {

                        // ดึงยอดคงเหลือล่าสุด
                        $last_log = BondLog::where('user_id', $user_id)
                            ->orderByDesc('id')
                            ->first();

                        if ($last_log) {
                            $newBalance = $last_log->balance - $deductAmount;
                        } else if ($User->bond_balance != null && $last_log == null) {
                            // ไม่มี log แต่มีเงินคงเหลือยู่
                            $newBalance = $User->bond_balance - $deductAmount;
                        } else {
                            // จ่ายครั้งแรก
                            $newBalance = $config->bond_required - $deductAmount;
                        }


                        // บันทึกการหักเงินประกัน
                        $BondLog = new BondLog();
                        $BondLog->user_id = $user_id;
                        $BondLog->year = $year;
                        $BondLog->month = $month;
                        $BondLog->total_amount = $config->bond_required;
                        $BondLog->deduct_amount = $deductAmount;
                        $BondLog->balance = $newBalance;
                        $BondLog->type = 'deduct';
                        $BondLog->save();


                        $User->bond_balance = $newBalance;
                        $User->save();
                    }


                    // บันทึกลง UserDeduct

                    // เช่คว่า id นี้ มียัง ใน UserDeduct
                    $getid_type = DeductType::firstOrCreate(
                        [
                            'name' => 'เงินประกันการทำงาน',
                            'branch_id' => $User->branch_id
                        ],
                        [
                            'description' => 'หักเงินประกันการทำงาน',
                            'view_in_slip' => false,
                            'type' => 'Once'
                        ]
                    );

                    $DeductPaid = new DeductPaid();
                    $DeductPaid->payroll_id = $payroll_id;
                    $DeductPaid->user_id = $user_id;
                    $DeductPaid->deduct_type_id = $getid_type->id;
                    $DeductPaid->year =  $year;
                    $DeductPaid->month =  $month;
                    $DeductPaid->price = $deductAmount;
                    $DeductPaid->description =  $getid_type->name;
                    $DeductPaid->create_by = $loginBy;

                    $DeductPaid->save();

                    $bondAmount = ['type' => 'deduct', 'amount' => $deductAmount];
                }
            }

            DB::commit();

            return $bondAmount;
        } catch (\Exception $e) {
            DB::rollBack();
            return null;
        }
    }
}
