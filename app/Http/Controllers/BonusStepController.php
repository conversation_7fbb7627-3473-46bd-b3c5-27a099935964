<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BonusStep;
use App\Models\User;

use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx\Rels;

class BonusStepController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getPageBonusStep(Request $request)
    {
        //
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_branch_id = $request->login_branch_id;


        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $user_id = $request->user_id;
        $branch_id = $login_branch_id;


        $col = array('id', 'step', 'branch_id', 'amount', 'created_at', 'updated_at');

        $orderby = array('id', 'step', 'branch_id', 'amount', 'created_at', 'updated_at');


        $d = BonusStep::select($col);

        if ($branch_id) {
            $d->where('branch_id', $branch_id);
        }


        if ($orderby[$order[0]['column']]) {
            $d->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            $d->Where(function ($query) use ($search, $col) {

                // search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                });

                // search with
                $query = $this->withUser($query, $search);
            });
        }


        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $loginBy = $request->login_by;
        $loginId = $request->login_id;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }
        if ($request->step > 12) {
            return $this->returnErrorData('เกิดข้อผิดพลาด! จำนวนเดือนเกิน 12 แล้ว', 404);
        }

        DB::beginTransaction();

        try {

            // check step
            $existing = BonusStep::where('step', $request->step)
                ->where('branch_id', $login_branch_id)
                ->first();

            if ($existing) {
                return response()->json([
                    'status' => false,
                    'message' => 'ขั้นเบี้ยขยันนี้มีอยู่แล้วในระบบ'
                ], 404);
            }


            //add BonusStep
            $bonusSteps = new BonusStep();

            $bonusSteps->branch_id = $login_branch_id;
            $bonusSteps->step = $request->step;
            $bonusSteps->amount = $request->amount;

            $bonusSteps->save();

            //log
            // $userId = $loginBy->user_id;
            $user = User::find($loginId);
            $userId =  $user->user_id;
            $type = 'Setting Bonus Step';
            $description = 'User ' . $userId . ' has ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
        $bonusSteps = BonusStep::find($id);
        if (!$bonusSteps) {
            return $this->returnErrorData('[id] not found', 404);
        }

        return $this->returnSuccess('Successful operation', $bonusSteps);
    }



    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //

        $loginBy = $request->login_by;
        $loginId = $request->login_id;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }
        if ($request->step > 12) {
            return $this->returnErrorData('เกิดข้อผิดพลาด! จำนวนเดือนเกิน 12 แล้ว', 404);
        }

        DB::beginTransaction();
        try {
            //update BonusStep
            $bonusSteps = BonusStep::find($id);
            if (!$bonusSteps) {
                return $this->returnErrorData('[id] not found', 404);
            }

            // check step
            $existing = BonusStep::where('step', $request->step)
                ->where('branch_id', $login_branch_id)
                ->first();
            if ($existing) {
                return response()->json([
                    'status' => false,
                    'message' => 'ขั้นเบี้ยขยันนี้มีอยู่แล้วในระบบ'
                ], 404);
            }

            $bonusSteps->step = $request->step;
            $bonusSteps->amount = $request->amount;
            $bonusSteps->save();

            //log
            $user = User::find($loginId);
            $userId =  $user->user_id;
            $type = 'Update Bonus Step';
            $description = 'User ' . $userId . ' has ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        //


        $loginBy = $request->login_by;
        $loginId = $request->login_id;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }


        DB::beginTransaction();
        try {

            $bonusSteps = BonusStep::find($id);
            if (!$bonusSteps) {
                return $this->returnErrorData('[id] not found', 404);
            }

            //delete BonusStep
            $bonusSteps->delete();

            //log
            $user = User::find($request->login_id);
            $userId =  $user->user_id;
            $type = 'Delete Bonus Step';
            $description = 'User ' . $userId . ' has ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }
}
