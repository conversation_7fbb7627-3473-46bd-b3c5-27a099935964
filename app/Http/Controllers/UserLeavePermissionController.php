<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Leave_permission;
use App\Models\LeaveType;
use App\Models\User;
use App\Models\User_leave_permission;
use App\Models\Work_shift_time;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UserLeavePermissionController extends Controller
{

    public function getUserLeavePermission(Request $request)
    {
        $user_id = $request->user_id;

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }
        // $getUserWorkShift =  $this->getUserWorkShift($loginBy->id);
        // return $loginBy;

        $USERID = null;
        if ($user_id) {
            $USERID = $user_id;
        } else {
            $USERID = $loginBy->id; //login by
        }

        $Year = date('Y');
        //
        $User_leave_permission = User_leave_permission::with('leave_type')
            ->with('user')
            ->where('user_id', $USERID)
            ->where('year', $Year)
            ->join('leave_types', 'user_leave_permissions.leave_type_id', '=', 'leave_types.id')
            ->orderBy('leave_types.id')
            ->select('user_leave_permissions.*')
            ->get()
            ->toArray();

        if (!empty($User_leave_permission)) {

            for ($i = 0; $i < count($User_leave_permission); $i++) {
                $User_leave_permission[$i]['No'] = $i + 1;


                $qty_hour_work_shift = 0;

                if ($User_leave_permission[$i]['user']) {
                    if ($User_leave_permission[$i]['user']['work_shift_id']) {
                        $Work_shift_time =  Work_shift_time::where('work_shift_id', $User_leave_permission[$i]['user']['work_shift_id'])
                            ->first();
                        if ($Work_shift_time) {
                            $time_1 = $Work_shift_time->time_in;
                            $time_2 = $Work_shift_time->time_out;

                            $qty_hour_work_shift += ($this->TimeDiff($time_1, $time_2) - 1);
                        }
                    }
                }

                // get Leave Permission
                //$User_leave_permission[$i]['leave_permission_balance'] = $this->checkLeavePermissionUser($loginBy->id, $User_leave_permission[$i]['qty'], $User_leave_permission[$i]['leave_type']['id']);
                // get Leave Permission
                $User_leave_permission[$i]['leave_permission_balance'] = $this->qtyLeavePermissionUser($USERID, $User_leave_permission[$i]['leave_type']['id'], $User_leave_permission[$i]['qty'], $qty_hour_work_shift);
            }
        }

        return $this->returnSuccess('Successful', $User_leave_permission);
    }

    public function getUserLeavePermissionForLine(Request $request)
    {
        if (!isset($request->line_id)) {
            return $this->returnErrorData('กรุณาส่ง line_id', 404);
        }

        $loginBy = User::where('line_id', $request->line_id)->first();
        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $leavePermissions = $this->getUserLeavePermissions($request->line_id);

        return $this->returnSuccess('Successful', $leavePermissions);
    }

    public function getUserLeavePermissions($line_id)
    {
        if (!isset($line_id)) {
            return $this->returnErrorData('กรุณาส่ง line_id', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();
        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }
        // $getUserWorkShift =  $this->getUserWorkShift($loginBy->id);
        // return $loginBy;

        $Year = date('Y');
        //
        $User_leave_permission = User_leave_permission::with('leave_type')
            ->with('user')
            ->where('user_id', $loginBy->id)
            ->where('year', $Year)
            ->join('leave_types', 'user_leave_permissions.leave_type_id', '=', 'leave_types.id')
            ->orderBy('leave_types.id')
            ->select('user_leave_permissions.*')
            ->get()
            ->toArray();

        if (!empty($User_leave_permission)) {

            for ($i = 0; $i < count($User_leave_permission); $i++) {
                $User_leave_permission[$i]['No'] = $i + 1;


                $qty_hour_work_shift = 0;

                if ($User_leave_permission[$i]['user']) {
                    if ($User_leave_permission[$i]['user']['work_shift_id']) {
                        $Work_shift_time =  Work_shift_time::where('work_shift_id', $User_leave_permission[$i]['user']['work_shift_id'])
                            ->first();
                        if ($Work_shift_time) {
                            $time_1 = $Work_shift_time->time_in;
                            $time_2 = $Work_shift_time->time_out;

                            $qty_hour_work_shift += ($this->TimeDiff($time_1, $time_2) - 1);
                        }
                    }
                }

                // get Leave Permission
                //$User_leave_permission[$i]['leave_permission_balance'] = $this->checkLeavePermissionUser($loginBy->id, $User_leave_permission[$i]['qty'], $User_leave_permission[$i]['leave_type']['id']);
                // get Leave Permission
                $User_leave_permission[$i]['leave_permission_balance'] = $this->qtyLeavePermissionUser($loginBy->id, $User_leave_permission[$i]['leave_type']['id'], $User_leave_permission[$i]['qty'], $qty_hour_work_shift);
            }
        }

        return $User_leave_permission;
    }

    public function index(Request $request)
    {
        $leave_type_id = $request->leave_type_id;
        $user_id = $request->user_id;
        $year = $request->year;

        $u = User_leave_permission::with('user')
            ->with('leave_type');

        if ($leave_type_id) $u->where('leave_type_id', $leave_type_id);
        if ($user_id) $u->where('user_id', $user_id);
        if ($year) $u->where('year', $year);

        $User_leave_permission = $u->get()->toarray();

        if (!empty($User_leave_permission)) {

            for ($i = 0; $i < count($User_leave_permission); $i++) {
                $User_leave_permission[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $User_leave_permission);
    }

    public function Page(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $perPage = $request->query('perPage', 10);
        $page = $request->query('page', 1);
        $search = $request->query('search', "");

        $leave_type_id = $request->leave_type_id;
        $user_id = $request->user_id;
        $year = $request->year;

        $searchable = (new User_leave_permission())->getTableColumns();

        $query = User_leave_permission::select($searchable)
            ->with('user')
            ->with('leave_type');

        if (isset($year)) {
            $query->orWhere('year', $year);
        }

        if (isset($leave_type_id)) {
            $query->orWhere('leave_type_id', $leave_type_id);
        }

        if (isset($user_id)) {
            $query->orWhere('user_id', $user_id);
        }

        $query->where(
            function ($query) use ($search, $searchable) {
                if ($search) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search . '%');
                    }
                }
            }
        );

        $query = $query->orderby('id')->paginate($perPage, ['*'], 'page', $page);

        if ($query->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $perPage);

            for ($i = 0; $i < count($query); $i++) {

                $No = $No + 1;
                $query[$i]->No = $No;

                // get Leave Permission
                $query[$i]->leave_permission_balance = $this->qtyLeavePermissionUser($loginBy->id, $query[$i]->leave_type->id, $query[$i]->qty);
            }
        }

        return $this->returnSuccess('Successful', $query);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $year = $request->year;
        $loginBy = $request->login_by;

        if (!isset($year)) {
            return $this->returnErrorData('กรุณาระบุปี', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $User = User::where('status', 1)->get();
            $LeaveType =  LeaveType::get();

            for ($i = 0; $i < count($User); $i++) {

                for ($j = 0; $j < count($LeaveType); $j++) {

                    $checkName = User_leave_permission::where('year', $year)
                        ->where('leave_type_id', $LeaveType[$j]->id)
                        ->where('user_id', $User[$i]->id)
                        ->first();

                    if (!$checkName) {
                        // return $this->returnError('มีข้อมูลในระบบแล้ว');

                        $User_leave_permission = new User_leave_permission();

                        $User_leave_permission->year = $year;
                        $User_leave_permission->user_id = $User[$i]->id;
                        $User_leave_permission->leave_type_id = $LeaveType[$j]->id;

                        //qty

                        $work_day = $this->dateDiff(date('Y-m-d'), ($User[$i]->register_date ? $User[$i]->register_date : date('Y-m-d')));
                        $work_year = intval($work_day / 365);

                        $work_year  = $this->yearDiff($User[$i]->register_date, date('Y-m-d'));
                        // $work_year = 0;

                        $Leave_permission = Leave_permission::where('leave_type_id', $User_leave_permission->leave_type_id)
                            ->where('year', '<=',  $work_year)
                            ->orderby('year', 'desc')
                            ->first();

                        $User_leave_permission->qty = ($Leave_permission ? $Leave_permission->qty : 0);

                        $User_leave_permission->status = true;
                        $User_leave_permission->create_by = $loginBy->user_id;
                        $User_leave_permission->updated_at = Carbon::now()->toDateTimeString();

                        $User_leave_permission->save();
                    }
                }
            }

            //log
            $userId = $loginBy->user_id;
            $type = 'เพิ่มจำนวนสิทธิ์การลาของพนักงาน';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $year;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $User_leave_permission = User_leave_permission::with('user')
            ->with('leave_type')
            ->find($id);
        return $this->returnSuccess('Successful', $User_leave_permission);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        //
    }

    public function updateUserLeavePermission(Request $request)
    {

        $user_id =  $request->user_id;
        $leave_permissions = $request->leave_permissions;


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }


        $year = date('Y');

        DB::beginTransaction();

        try {

            if (!empty($leave_permissions)) {

                for ($j = 0; $j < count($leave_permissions); $j++) {

                    $checkName = User_leave_permission::where('year', $year)
                        ->where('leave_type_id', $leave_permissions[$j]['leave_type_id']) //leave_type_id
                        ->where('user_id', $user_id)
                        ->first();

                    if (!$checkName) {
                        //new
                        $User_leave_permission = new User_leave_permission();

                        $User_leave_permission->year = $year;

                        $User_leave_permission->user_id = $user_id;
                        $User_leave_permission->leave_type_id = $leave_permissions[$j]['leave_type_id'];
                        $User_leave_permission->qty =  $leave_permissions[$j]['qty'];

                        $User_leave_permission->status = true;
                        $User_leave_permission->create_by = $loginBy->user_id;
                        $User_leave_permission->updated_at = Carbon::now()->toDateTimeString();

                        $User_leave_permission->save();
                    } else {

                        //update
                        $checkName->year = $year;

                        $checkName->user_id = $user_id;
                        $checkName->leave_type_id = $leave_permissions[$j]['leave_type_id'];
                        $checkName->qty =  $leave_permissions[$j]['qty'];

                        $checkName->status = true;
                        $checkName->create_by = $loginBy->user_id;
                        $checkName->updated_at = Carbon::now()->toDateTimeString();

                        $checkName->save();
                    }
                }
            }


            DB::commit();

            return $this->returnSuccess('Successful', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }

    public function cronAddLeavePermission()
    {
        DB::beginTransaction();

        try {

            $year = date('Y');

            $User = User::where('status', 1)->get();
            $LeaveType =  LeaveType::get();
            for ($i = 0; $i < count($User); $i++) {

                for ($j = 0; $j < count($LeaveType); $j++) {

                    $checkName = User_leave_permission::where('year', $year)
                        ->where('leave_type_id', $LeaveType[$j]->id)
                        ->where('user_id', $User[$i]->id)
                        ->first();

                    if (!$checkName) {
                        // return $this->returnError('มีข้อมูลในระบบแล้ว');

                        $User_leave_permission = new User_leave_permission();

                        $User_leave_permission->year = $year;
                        $User_leave_permission->user_id = $User[$i]->id;
                        $User_leave_permission->leave_type_id = $LeaveType[$j]->id;

                        //qty

                        $work_day = $this->dateDiff(date('Y-m-d'), ($User[$i]->register_date ? $User[$i]->register_date : date('Y-m-d')));
                        $work_year = intval($work_day / 365);

                        $work_year  = $this->yearDiff($User[$i]->register_date, date('Y-m-d'));
                        // $work_year = 0;

                        $Leave_permission = Leave_permission::where('leave_type_id', $User_leave_permission->leave_type_id)
                            ->where('year', '<=',  $work_year)
                            ->orderby('year', 'desc')
                            ->first();

                        $User_leave_permission->qty = ($Leave_permission ? $Leave_permission->qty : 0);

                        $User_leave_permission->status = true;
                        $User_leave_permission->updated_at = Carbon::now()->toDateTimeString();

                        $User_leave_permission->save();
                    }
                }
            }


            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
        }
    }
}
