<?php

namespace App\Http\Controllers;

use App\Models\UserDeduct;
use App\Models\DeductType;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserDeductController extends Controller
{
    public function getList($id)
    {
        $Item = UserDeduct::where('user_id', $id)
            ->with('user')
            ->with('deduct_type')
            ->get();

        if (!empty($Item)) {

            for ($i = 0; $i < count($Item); $i++) {
                $Item[$i]['No'] = $i + 1;
                $Item[$i]['deduct_type'] = DeductType::find($Item[$i]['deduct_type_id']);
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    public function getPage(Request $request)
    {
        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $user_id = $request->user_id;

        $Status = $request->status;

        $col = array('id', 'user_id', 'deduct_type_id', 'price', 'created_at', 'updated_at');

        $orderby = array('', 'user_id', 'deduct_type_id', 'price');

        $D = UserDeduct::select($col)
            ->with('user')
            ->with('deduct_type');

        if ($user_id) {
            $D->where('user_id', $user_id);
        }

        if (isset($Status)) {
            $D->where('status', $Status);
        }

        if ($orderby[$order[0]['column']]) {
            $D->orderby($orderby[$order[0]['column']], $order[0]['dir']);
        }

        if ($search['value'] != '' && $search['value'] != null) {

            $D->Where(function ($query) use ($search, $col) {

                //search datatable
                $query->orWhere(function ($query) use ($search, $col) {
                    foreach ($col as &$c) {
                        $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                    }
                });

                //search with
                // //$query = $this->withPermission($query, $search);
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
                $d[$i]->income_type = DeductType::where('id', $d[$i]->deduct_type_id)->get();
                $d[$i]->user = User::where('id', $d[$i]->user_id)->get();
                $d[$i]->create = User::where('user_id', $d[$i]->create_by)->first();
            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $d);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาระบุ user_id ให้เรียบร้อย', 404);
        } else if (!isset($request->deduct_type_id)) {
            return $this->returnErrorData('กรุณาระบุ deduct_type_id ให้เรียบร้อย', 404);
        }

        DB::beginTransaction();

        try {
            $Item = new UserDeduct();
            $Item->user_id = $request->user_id;
            $Item->deduct_type_id = $request->deduct_type_id;
            $Item->price = $request->price;

            $Item->save();
            //

            //log
            $userId = "admin";
            $type = 'เพิ่มรายการเงินหัก';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $Item->user_id;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', $Item);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 404);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\UserDeduct  $UserDeduct
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Item = UserDeduct::where('id', $id)
            ->first();

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Item);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\UserDeduct  $UserDeduct
     * @return \Illuminate\Http\Response
     */
    public function edit(UserDeduct $UserDeduct)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\UserDeduct  $UserDeduct
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($id)) {
            return $this->returnErrorData('ไม่พบข้อมูล id', 404);
        } else
        //

        {
            DB::beginTransaction();
        }

        try {

            $Item = UserDeduct::find($id);
            if (!$Item) {
                return $this->returnErrorData('ไม่พบข้อมูลในระบบ', 404);
            }

            $Item->user_id = $request->user_id;
            $Item->deduct_type_id = $request->deduct_type_id;
            $Item->price = $request->price;
            $Item->updated_at = Carbon::now()->toDateTimeString();
            $Item->save();

            //log
            $userId = "admin";
            $type = 'แก้ไขเงินหัก';
            $description = 'ผู้ใช้งาน ' . $userId . ' ได้ทำการ ' . $type . ' ' . $Item->user_id;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnUpdate('ดำเนินการสำเร็จ');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ', 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\UserDeduct  $UserDeduct
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Item = UserDeduct::with('deduct_type')->find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Item';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $Item->deduct_type->name;
            $this->Log($userId, $description, $type);
            //

            $Item->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
    public function getDeductwithUser(Request $request, $id)
    {

        DB::beginTransaction();
        try {

            $Item = UserDeduct::where('user_id', $id)
                ->with('user')
                ->with('deduct_type')
                ->get();


            DB::commit();
            return $this->returnSuccess('Successful operation', $Item);
        } catch (\Throwable $e) {
            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
}
