<?php

namespace App\Http\Controllers;

use App\Models\LoanPolicy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LoanPolicyController extends Controller
{
    public function index()
    {
        $policy = LoanPolicy::where('is_active', true)->first();

        if (!$policy) {
            return $this->returnErrorData('ไม่พบนโยบาย', 404);
        }

        return $this->returnSuccess('สำเร็จ', $policy);
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'interest_rate_pa' => 'nullable|numeric|min:0',
            'dti_default_pct' => 'nullable|numeric|min:0',
            'net_min_amount' => 'nullable|numeric|min:0',
            'open_new_after_paid_pct' => 'nullable|numeric|min:0',
            'allow_multi_active' => 'nullable|boolean',
            'max_total_amount' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        $policy = LoanPolicy::where('is_active', true)->first();

        if (!$policy) {
            return $this->returnErrorData('ไม่พบนโยบาย', 404);
        }

        $policy->fill($request->only([
            'interest_rate_pa',
            'dti_default_pct',
            'net_min_amount',
            'open_new_after_paid_pct',
            'allow_multi_active',
            'max_total_amount',
        ]));

        $policy->update_by = $request->login_by->user_id;

        $policy->save();

        return $this->returnSuccess('สำเร็จ', $policy);
    }
}
