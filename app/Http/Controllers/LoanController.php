<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Employee_salary;
use App\Models\LoanPolicy;
use App\Models\User;
use App\Models\Loan;
use App\Models\LoanPolicyPersonalLimit;
use App\Models\LoanSchedule;
use App\Models\PayrollRound;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class LoanController extends Controller
{
    public function show($id)
    {
        $loan = Loan::with('user')
            ->with('loan_schedules')
            ->find($id);

        if (!$loan) {
            return $this->returnErrorData('ไม่พบสัญญาเงินกู้', 404);
        }

        return $this->returnSuccess('สำเร็จ', $loan);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id'               => 'required|integer',
            'approved_amount'       => 'required|numeric|min:0.01',
            'term_months'           => 'required|integer|min:1',
            'interest_mode'         => 'in:flat_amortized,flat_balloon',
            'interest_rate_pa'      => 'numeric|min:0',
            'start_cycle'           => 'nullable|date',
            'first_due_date'        => 'nullable|date',
            'comfirm'               => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors()->toArray(), 400);
        }

        $confirm = $request->input('comfirm', false);
        $userId = (int) $request->input('user_id');
        $P = (float) $request->input('approved_amount');
        $m = (int) $request->input('term_months');
        $paymentsPerMonth = PayrollRound::max('round');

        $user = User::find($userId);
        if (!$user) {
            return $this->returnErrorData('ไม่พบผู้ใช้งาน', 404);
        }

        $policy = LoanPolicy::where('is_active', true)->first();
        if (!$policy) {
            return $this->returnErrorData('ไม่พบนโยบายที่กำลังใช้งาน', 400);
        }

        $mode = $request->input('interest_mode', $policy->interest_mode);
        $ratePa = (float) $request->input('interest_rate_pa', $policy->interest_rate_pa);

        // ตรวจสอบอายุงาน: ถ้าน้อยกว่า 12 เดือน ห้ามสร้างสัญญาโดยตรง ต้องผ่าน Loan Application ก่อน
        // $hireDate = null;
        // if (!empty($user->hire_date)) {
        //     $hireDate = Carbon::parse($user->hire_date);
        // } else {
        //     $empSnap = Employee_salary::where('user_id', $userId)
        //                 ->orderByDesc('hire_date')
        //                 ->first();
        //     if ($empSnap && !empty($empSnap->hire_date)) {
        //         $hireDate = Carbon::parse($empSnap->hire_date);
        //     }
        // }
        // if ($hireDate && $hireDate->diffInMonths(Carbon::now()) < 12) {
        //     return $this->returnErrorData('อายุงานน้อยกว่า 12 เดือน — โปรดสร้างคำขอกู้ (Loan Application) และส่งขออนุมัติผู้บริหาร', 422);
        // }

        // --- Validation: เพดานหนี้รวมสูงสุด ---
        if (isset($policy->max_total_amount) && $policy->max_total_amount !== null) {
            $currentOutstanding = Loan::whereIn('status', ['active', 'overdue'])
                ->sum('outstanding_principal');
            $projectedTotal = (float)$currentOutstanding + (float)$P;
            if ($projectedTotal > (float)$policy->max_total_amount) {
                return $this->returnErrorData('ยอดหนี้รวมหลังขอกู้เกินเพดานสูงสุดตามนโยบาย (max_total_amount)', 422);
            }
        }

        // --- Validation: มีสัญญาเก่าที่ยังไม่จ่ายเกินครึ่ง ห้ามสร้างกู้ใหม่ ---
        $activeLoan = Loan::where('user_id', $userId)
            ->where('status', 'active')
            ->first();
        if ($activeLoan) {
            $approved = (float)$activeLoan->approved_amount;
            $outstanding = (float)$activeLoan->outstanding_principal;
            if ($approved > 0 && $outstanding > ($approved * 0.5) && !$confirm) {
                return response()->json([
                    'code'      => 422,
                    'status'    => false,
                    'message'   => 'มีสัญญาเดิมยังชำระเงินต้นไม่ถึง 50% จึงยังไม่สามารถกู้เพิ่มได้',
                    'confirm'   => true,
                ], 422);
            }
        }

        $startCycle = $request->input('start_cycle');
        if (!$startCycle) {
            $startCycle = Carbon::now()->format('Y-m-d');
        }
        $firstDue = $request->input('first_due_date');

        // periods & rates
        $periods = $m * $paymentsPerMonth;
        $iMonth = $ratePa / 100.0 / 12.0;          // monthly rate
        $iPerPeriod = $iMonth / $paymentsPerMonth; // per installment rate

        // Build schedule (amounts only)
        if ($mode === 'flat_amortized') {
            [$rows, $totals, $installment] = $this->_buildFlatAmortizedSchedule($P, $periods, $iPerPeriod, $iMonth);
        } elseif ($mode === 'flat_balloon') {
            [$rows, $totals] = $this->_buildFlatBalloonSchedule($P, $periods, $iMonth);
            $installment = null;
        } else {
            return $this->returnErrorData('ไม่รองรับรูปแบบการคิดดอก (interest_mode): ' . $mode, 422);
        }

        // Due dates
        $dueDates = $this->_generateDueDates($startCycle, $periods, $paymentsPerMonth, $firstDue);

        // Persist in transaction
        $loan = null;
        DB::beginTransaction();
        try {
            $loan = Loan::create([
                'user_id' => $userId,
                'approved_amount' => round($P, 2),
                'term_months' => $m,
                'interest_rate_pa' => $ratePa,
                'start_cycle' => $startCycle,
                'interest_mode' => $mode,
                'status' => 'active',
                'outstanding_principal' => round($P, 2),
            ]);

            foreach ($rows as $idx => $r) {
                LoanSchedule::create([
                    'loan_id' => $loan->id,
                    'installment_no' => $r['no'],
                    'due_date' => $dueDates[$idx],
                    'principal_due' => $r['principal'],
                    'interest_due' => $r['interest'],
                    'total_due' => $r['total'],
                    'status' => 'due',
                ]);
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            return $this->returnErrorData('สร้างสัญญาเงินกู้ล้มเหลว: ' . $e->getMessage(), 500);
        }

        return $this->returnSuccess('สร้างสัญญาเงินกู้เรียบร้อย', [
            'loan' => [
                'id' => $loan->id,
                'user_id' => $loan->user_id,
                'approved_amount' => (float)$loan->approved_amount,
                'term_months' => $loan->term_months,
                'interest_rate_pa' => (float)$loan->interest_rate_pa,
                'interest_mode' => $loan->interest_mode,
                'status' => $loan->status,
                'outstanding_principal' => (float)$loan->outstanding_principal,
                'start_cycle' => $loan->start_cycle,
                'payments_per_month' => $paymentsPerMonth,
                'monthly_rate' => round($iMonth, 6),
                'installment_amount' => $installment,
                'totals' => $totals,
            ],
            'schedule' => array_map(function ($r, $d) {
                return [
                    'no' => $r['no'],
                    'due_date' => $d,
                    'principal' => $r['principal'],
                    'interest' => $r['interest'],
                    'total' => $r['total'],
                    'status' => 'due'
                ];
            }, $rows, $dueDates),
        ]);
    }

    public function checkEligibility(Request $request)
    {
        $userId = $request->input('user_id');
        $requestedAmount = (float) $request->input('requested_amount');
        $termMonths = (int) $request->input('term_months', 12);
        // $paymentsPerMonth = (int) $request->input('payments_per_month', 2); // 1 เดือน 2 งวด (ค่าเริ่มต้นใหม่)

        $user = User::find($userId);
        if (!$user) {
            return $this->returnErrorData('ไม่พบผู้ใช้งาน', 404);
        }

        $snapshot = Employee_salary::where('user_id', $userId)
            ->orderByDesc('hire_date')
            ->first();

        if (!$snapshot) {
            return $this->returnErrorData('ไม่พบข้อมูลสแนปช็อตเงินเดือน', 400);
        }

        $policy = LoanPolicy::where('is_active', true)->first();
        if (!$policy) {
            return $this->returnErrorData('ไม่พบนโยบายที่กำลังใช้งาน', 400);
        }

        // โหลดเงื่อนไขเฉพาะบุคคล (LoanPolicyPersonalLimit) ถ้ามี ให้ override ค่า policy กลาง
        $personalLimit = LoanPolicyPersonalLimit::where('user_id', $userId)
            ->first();

        $maxDtiPct = $personalLimit && $personalLimit->max_dti_pct !== null
            ? (float) $personalLimit->max_dti_pct
            : (float) $policy->dti_default_pct; // ค่าเริ่มต้นจาก Policy

        // เพดานวงเงินเท่าของเงินเดือน: ถ้าไม่ระบุใน personal ให้ใช้ค่าเริ่มต้น 2.0 เท่า (ตาม business rule)
        $maxMultiplier = $personalLimit && $personalLimit->max_multiplier !== null
            ? (float) $personalLimit->max_multiplier
            : 2.0;

        // เงินที่หักเกินที่ตั้งค่าไว้
        $maxDeductAmount = $personalLimit && $personalLimit->net_min_amount !== null
            ? (float) $personalLimit->net_min_amount
            : (float) $policy->net_min_amount; // ค่าเริ่มต้นจาก Policy

        // รายได้ต่อเดือน
        $grossIncome = (float) $snapshot->salary;

        // ตรวจสอบอายุงาน (tenure)
        $hireDate = null;
        // ใช้วันบรรจุจาก User ถ้ามี ไม่งั้น fallback ไปที่ snapshot.hire_date
        if (!empty($user->register_date)) {
            $hireDate = Carbon::parse($user->register_date);
        } elseif (!empty($snapshot->hire_date)) {
            $hireDate = Carbon::parse($snapshot->hire_date);
        }

        $tenureMonths = $hireDate ? $hireDate->diffInMonths(Carbon::now()) : null;
        $needsManagerApproval = ($tenureMonths !== null && $tenureMonths < 12);

        // อัตราดอกต่อเดือน และภาระรายเดือน (สมมติใช้ flat-amortized สำหรับการตรวจสิทธิ์)
        $iMonth = ((float)$policy->interest_rate_pa) / 100.0 / 12.0;
        $totalInterest = $requestedAmount * ($iMonth * max($termMonths, 1));
        $monthlyBurden = ($requestedAmount + $totalInterest) / max($termMonths, 1); // ยอดที่ต้องกันไว้ต่อเดือน

        $reasons = [];
        $eligible = true;

        if ($needsManagerApproval) {
            $reasons[] = 'อายุงานน้อยกว่า 12 เดือน: ต้องสร้างคำขอกู้และขออนุมัติผู้บริหาร';
        }

        // DTI ใช้ภาระรายเดือนจริง
        $dti = $monthlyBurden / max($grossIncome, 1);
        if ($dti > ($maxDtiPct / 100)) {
            $eligible = false;
            $reasons[] = 'ค่า DTI เกินเพดานตามนโยบาย/เพดานรายบุคคล';
        }

        // เพดานวงเงินเท่าของเงินเดือน จาก Personal Limit (ถ้ามี) หรือ default 2 เท่า
        if ($requestedAmount > ($grossIncome * $maxMultiplier)) {
            $eligible = false;
            $reasons[] = 'ยอดที่ขอกู้เกินเพดานวงเงินเท่าของเงินเดือน (ตามนโยบาย/รายบุคคล)';
        }

        // // เงินสุทธิต้องเหลือขั้นต่ำหลังหักภาระรายเดือน
        // if (($grossIncome - $monthlyBurden) < $policy->net_min_amount) {
        //     $eligible = false;
        //     $reasons[] = 'เงินสุทธิหลังหักต่ำกว่าเกณฑ์ขั้นต่ำที่กำหนด';
        // }

        if ($monthlyBurden > $maxDeductAmount) {
            $eligible = false;
            $reasons[] = 'ยอดผ่อนชำระ ' . number_format($monthlyBurden, 2) . ' บาท มากกว่าเกณฑ์ที่กำหนด ' . number_format($maxDeductAmount, 2) . ' บาท';
        }

        // --- ตรวจเพดานหนี้รวมสูงสุด (policy.max_total_amount) ---
        if (isset($policy->max_total_amount) && $policy->max_total_amount !== null) {
            $currentOutstanding = Loan::where('user_id', $userId)
                ->whereIn('status', ['active', 'overdue'])
                ->sum('outstanding_principal');
            $projectedTotal = (float)$currentOutstanding + (float)$requestedAmount;
            if ($projectedTotal > (float)$policy->max_total_amount) {
                $eligible = false;
                $reasons[] = 'ยอดหนี้รวมหลังขอกู้เกินเพดานสูงสุดตามนโยบาย (max_total_amount)';
            }
        }

        // --- ตรวจสัญญาเดิม: หากมีสัญญา Active ต้องจ่ายเกินครึ่งจึงจะกู้เพิ่มได้ ---
        $activeLoan = Loan::where('user_id', $userId)
            ->where('status', 'active')
            ->first();
        if ($activeLoan) {
            $approved = (float)$activeLoan->approved_amount;
            $outstanding = (float)$activeLoan->outstanding_principal;
            $paidPct = $approved > 0 ? (1 - ($outstanding / $approved)) * 100.0 : 0.0;
            if ($outstanding > ($approved * 0.5)) {
                $eligible = false;
                $reasons[] = 'มีสัญญาเดิมยังชำระเงินต้นไม่ถึง 50% จึงยังไม่สามารถกู้เพิ่มได้';
            }
        }

        $paymentsPerMonth = PayrollRound::max('round');

        return $this->returnSuccess('สำเร็จ', [
            'eligible' => $eligible,
            'reasons' => $reasons,
            'dti' => round($dti, 6),
            'gross_income' => round($grossIncome, 2),
            'monthly_burden' => round($monthlyBurden, 2),
            'payments_per_month' => $paymentsPerMonth,
            'assumed_interest_mode' => 'flat_amortized',
            'assumed_rate_pa' => (float)$policy->interest_rate_pa,
            'tenure_months' => $tenureMonths,
            'needs_manager_approval' => $needsManagerApproval,
            'applied_limits' => [
                'dti_limit_pct' => $maxDtiPct,
                'max_multiplier' => $maxMultiplier,
                'policy_id' => $policy->id,
                'personal_override' => $personalLimit ? true : false,
            ],
        ]);
    }

    public function calculateInstallment(Request $request)
    {
        $P = (float) $request->input('requested_amount'); //จำนวนเงินที่กู้
        $m = (int) $request->input('term_months', 12); //จำนวนเดือนที่ผ่อน

        $policy = LoanPolicy::where('is_active', true)->first();
        if (!$policy) {
            return $this->returnErrorData('ไม่พบนโยบายที่กำลังใช้งาน', 400);
        }

        $mode = $request->input('interest_mode', $policy->interest_mode);
        $ratePa = (float) $request->input('interest_rate_pa', $policy->interest_rate_pa);

        $paymentsPerMonth = PayrollRound::max('round');

        if ($P <= 0 || $m <= 0 || $ratePa < 0) {
            return $this->returnErrorData('ข้อมูลไม่ถูกต้อง: จำนวนเงิน/จำนวนงวด/อัตราดอก ต้องเป็นค่ามากกว่า 0', 422);
        }

        $periods = $m * $paymentsPerMonth;             // จำนวนงวดรวมทั้งหมด
        $iMonth = $ratePa / 100.0 / 12.0;              // อัตราดอกต่อเดือน
        $iPerPeriod = $iMonth / $paymentsPerMonth;     // อัตราดอกต่อ 1 งวด (ครึ่งเดือน)

        if ($mode === 'flat_amortized') {
            [$schedule, $totals, $installment] = $this->_buildFlatAmortizedSchedule($P, $periods, $iPerPeriod, $iMonth);
            return $this->returnSuccess('สำเร็จ', [
                'mode' => $mode,
                'rate_pa' => $ratePa,
                'principal' => round($P, 2),
                'term_months' => $m,
                'payments_per_month' => $paymentsPerMonth,
                'periods' => $periods,
                'installment_amount' => $installment,
                'totals' => $totals,
                'schedule' => $schedule,
            ]);
        } elseif ($mode === 'flat_balloon') {
            [$schedule, $totals] = $this->_buildFlatBalloonSchedule($P, $periods, $iMonth);
            return $this->returnSuccess('สำเร็จ', [
                'mode' => $mode,
                'rate_pa' => $ratePa,
                'principal' => round($P, 2),
                'term_months' => $m,
                'payments_per_month' => $paymentsPerMonth,
                'periods' => $periods,
                'installment_amount' => null,
                'totals' => $totals,
                'schedule' => $schedule,
            ]);
        } else {
            return $this->returnErrorData('ไม่รองรับรูปแบบการคิดดอก (interest_mode): ' . $mode, 422);
        }
    }

    //ใช้คำนวณดอกแบบรวมเฉลี่ยทุกงวด + คืนตารางผ่อนและค่างวดต่อเดือน
    private function _buildFlatAmortizedSchedule(float $P, int $periods, float $iPerPeriod, float $iMonth): array
    {
        // ดอกเบี้ยรวม: P * i_per_period * periods  ==  P * i_month * months
        $totalInterest = $P * ($iPerPeriod * $periods);
        $grand = $P + $totalInterest;
        $installment = round($grand / $periods, 2);

        $schedule = [];
        $sumPrincipal = 0.0;
        $sumInterest  = 0.0;

        for ($k = 1; $k <= $periods; $k++) {
            if ($k < $periods) {
                $principal = round($P / $periods, 2);
                $interest  = round($totalInterest / $periods, 2);
                $total     = round($principal + $interest, 2);
            } else {
                $principal = round($P - $sumPrincipal, 2);
                $interest  = round($totalInterest - $sumInterest, 2);
                $total     = round($principal + $interest, 2);
                if (abs($total - $installment) <= 0.02) {
                    $total = $installment;
                }
            }

            $schedule[] = ['no' => $k, 'principal' => $principal, 'interest' => $interest, 'total' => $total];
            $sumPrincipal += $principal;
            $sumInterest  += $interest;
        }

        $totals = [
            'principal' => round($sumPrincipal, 2),
            'interest'  => round($sumInterest, 2),
            'grand'     => round($sumPrincipal + $sumInterest, 2),
        ];

        return [$schedule, $totals, $installment];
    }

    //ใช้จ่ายต้นเท่ากันในงวด 1..m-1 และจ่ายดอก 1 เดือนงวดสุดท้าย
    private function _buildFlatBalloonSchedule(float $P, int $periods, float $iMonth): array
    {
        $schedule = [];

        if ($periods <= 1) {
            $interest = round($P * $iMonth, 2);
            $schedule[] = ['no' => 1, 'principal' => round($P, 2), 'interest' => $interest, 'total' => round($P + $interest, 2)];
            $totals = ['principal' => round($P, 2), 'interest' => $interest, 'grand' => round($P + $interest, 2)];
            return [$schedule, $totals];
        }

        $equalPrincipal = round($P / ($periods - 1), 2);
        $sumPrincipal = 0.0;
        for ($k = 1; $k < $periods; $k++) {
            $principal = ($k < $periods - 1) ? $equalPrincipal : round($P - $sumPrincipal, 2);
            $schedule[] = ['no' => $k, 'principal' => $principal, 'interest' => 0.00, 'total' => $principal];
            $sumPrincipal += $principal;
        }

        $interestLast = round($P * $iMonth, 2); // ดอก 1 เดือน หลังปิดต้นครบ
        $schedule[] = ['no' => $periods, 'principal' => 0.00, 'interest' => $interestLast, 'total' => $interestLast];

        $totals = ['principal' => round($sumPrincipal, 2), 'interest' => $interestLast, 'grand' => round($sumPrincipal + $interestLast, 2)];
        return [$schedule, $totals];
    }

    private function _generateDueDates(string $startCycle, int $periods, int $paymentsPerMonth, ?string $firstDueDate = null): array
    {
        $dates = [];

        if ($firstDueDate) {
            $current = Carbon::parse($firstDueDate)->startOfDay();
            for ($k = 1; $k <= $periods; $k++) {
                $dates[] = $current->format('Y-m-d');
                if ($paymentsPerMonth === 2) {
                    // alternate 15th and EOM
                    $day = (int)$current->day;
                    if ($day <= 15) {
                        // next is EOM same month
                        $current = $current->copy()->endOfMonth();
                    } else {
                        // next is 15th next month
                        $current = $current->copy()->addMonthNoOverflow()->day(15);
                    }
                } else {
                    // monthly EOM
                    $current = $current->copy()->endOfMonth()->addMonthNoOverflow()->endOfMonth();
                }
            }
            return $dates;
        }

        // derive first due from startCycle
        $cursor = Carbon::parse($startCycle)->startOfDay();
        if ($paymentsPerMonth === 2) {
            // first due: if day <= 15 => 15th this month, else => EOM this month
            if ((int)$cursor->day <= 15) {
                $current = $cursor->copy()->day(15);
            } else {
                $current = $cursor->copy()->endOfMonth();
            }
            for ($k = 1; $k <= $periods; $k++) {
                $dates[] = $current->format('Y-m-d');
                $day = (int)$current->day;
                if ($day <= 15) {
                    $current = $current->copy()->endOfMonth();
                } else {
                    $current = $current->copy()->addMonthNoOverflow()->day(15);
                }
            }
        } else {
            // monthly EOM starting from startCycle's month
            $current = $cursor->copy()->endOfMonth();
            for ($k = 1; $k <= $periods; $k++) {
                $dates[] = $current->format('Y-m-d');
                $current = $current->copy()->addMonthNoOverflow()->endOfMonth();
            }
        }

        return $dates;
    }

    public function datatables(Request $request)
    {
        $loginBy = $request->login_by;

        $input = $request->all();
        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);
        $filter = $request->input('filter', "");

        $searchable = [
            'users.first_name',
            'users.last_name',
        ];

        $dataset = DB::table('loans')->select([
            'loans.id',
            'users.first_name',
            'users.last_name',
            'loans.approved_amount',
            'loans.term_months',
            'loans.interest_rate_pa',
            'loans.interest_mode',
            'loans.status',
            'loans.outstanding_principal',
            'loans.start_cycle',
            'loans.created_at',
            'loans.updated_at',
        ])
            ->join('users', 'loans.user_id', '=', 'users.id')
            ->where(function ($query) use ($search, $searchable, $filter) {
                if ($search['value']) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                }

                if (isset($filter['status'])) {
                    $query->where('loan_applications.status', $filter['status']);
                }
            })
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);


        return $dataset;
    }
}
