<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use Illuminate\Http\Request;

class MenuController extends Controller
{
    public function getMenu()
    {
        $Menu = Menu::get()->toarray();

        if (!empty($Menu)) {

            for ($i = 0; $i < count($Menu); $i++) {
                $Menu[$i]['No'] = $i + 1;

            }
        }

        return $this->returnSuccess('เรียกดูข้อมูลสำเร็จ', $Menu);
    }

}
