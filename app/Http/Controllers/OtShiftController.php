<?php

namespace App\Http\Controllers;

use App\Imports\OtShiftImport;
use App\Models\Ot_shift;
use App\Models\OtShift;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class OtShiftController extends Controller
{

    public function getOtShift(Request $request)
    {

        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $q = Ot_shift::where('status', 1)
            ->with('user_create');

        if ($login_branch_id) {
            $q->where('branch_id', $login_branch_id);
        }

        $OtShift = $q->get()->toArray();


        if (!empty($OtShift)) {

            for ($i = 0; $i < count($OtShift); $i++) {
                $OtShift[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $OtShift);
    }

    public function OtShiftPage(Request $request)
    {

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $col = array('id', 'branch_id', 'name', 'time_in', 'time_out', 'type', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = Ot_shift::select($col)->with('user_create');

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        $d->orderBy($col[$order[0]['column']], $order[0]['dir']);

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        if (!isset($request->name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อประเภท OT ด้วย', 404);
        } else if (!isset($request->time_in)) {
            return $this->returnErrorData('กรุณาระบุเวลาเริ่มต้นโอที', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        } else if (!isset($request->time_out)) {
            return $this->returnErrorData('กรุณาระบุเวลาสิ้นสุดโอที', 404);
        } else if (!isset($request->type)) {
            return $this->returnErrorData('กรุณาระบุประเภทการคิด OT', 404);
        }

        $name = $request->name;

        $checkName = Ot_shift::where('name', $name)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $OtShift = new Ot_shift();
                $OtShift->branch_id = $login_branch_id;
                $OtShift->name = $name;
                $OtShift->time_in = $request->time_in;
                $OtShift->time_out = $request->time_out;
                $OtShift->type = $request->type ?? 'work';
                $OtShift->status = 1;

                $OtShift->create_by = $loginBy->user_id;

                $OtShift->save();

                //log
                $userId = $loginBy->user_id;
                $type = 'Add Ot Shift';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $OtShift = Ot_shift::find($id);
        return $this->returnSuccess('Successful', $OtShift);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $name = $request->name;

        $checkName = Ot_shift::where('id', '!=', $id)
            ->where('branch_id', $login_branch_id)
            ->where('name', $name)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this name in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $OtShift = Ot_shift::find($id);

                $OtShift->name = $name;
                $OtShift->time_in = $request->time_in;
                $OtShift->time_out = $request->time_out;
                $OtShift->type = $request->type ?? 'work';

                $OtShift->update_by = $loginBy->user_id;
                $OtShift->updated_at = Carbon::now()->toDateTimeString();

                $OtShift->save();
                //log
                $userId = $loginBy->user_id;
                $type = 'Edit Ot Shift';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $OtShift->name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnUpdate('Successful operation');
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $OtShift = Ot_shift::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete Ot Shift';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $OtShift->name;
            $this->Log($userId, $description, $type);
            //

            $OtShift->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    // public function ImportOtShift(Request $request)
    // {

    //     $loginBy = $request->login_by;

    //     if (!isset($loginBy)) {
    //         return $this->returnErrorData('User information not found. Please login again', 404);
    //     }

    //     $file = request()->file('file');
    //     $fileName = $file->getClientOriginalName();

    //     $Data = Excel::toArray(new OtShiftImport(), $file);
    //     $data = $Data[0];

    //     if (count($data) > 0) {

    //         $insert_data = [];

    //         for ($i = 0; $i < count($data); $i++) {

    //             $name = trim($data[$i]['name']);

    //             $row = $i + 2;

    //             if ($name == '') {
    //                 return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
    //             }

    //             //check row sample
    //             if ($name == 'SIMPLE-000') {
    //                 //
    //             } else {

    //                 // //check name
    //                 // $OtShift = Ot_shift::where('name', $name)->first();
    //                 // if ($OtShift) {
    //                 //     return $this->returnErrorData('OtShift ' . $name . ' was information information is already in the system', 404);
    //                 // }

    //                 //check dupicate data form file import
    //                 for ($j = 0; $j < count($insert_data); $j++) {

    //                     if ($name == $insert_data[$j]['name']) {
    //                         return $this->returnErrorData('OtShift ' . $name . ' There is duplicate data in the import file', 404);
    //                     }
    //                 }
    //                 ///

    //                 $insert_data[] = array(
    //                     'name' => $name,
    //                     'status' => 1,
    //                     'created_at' => date('Y-m-d H:i:s'),
    //                     'updated_at' => date('Y-m-d H:i:s'),
    //                 );
    //             }
    //         }

    //         if (!empty($insert_data)) {

    //             DB::beginTransaction();

    //             try {

    //                 //updateOrInsert
    //                 for ($i = 0; $i < count($insert_data); $i++) {

    //                     DB::table('Ot Shift')
    //                         ->updateOrInsert(
    //                             [
    //                                 'id' => trim($data[$i]['id']), //id
    //                             ],
    //                             $insert_data[$i]
    //                         );
    //                 }
    //                 //

    //                 DB::commit();

    //                 //log
    //                 $userId = $loginBy->user_id;
    //                 $type = 'Import Ot Shift';
    //                 $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
    //                 $this->Log($userId, $description, $type);
    //                 //

    //                 DB::commit();

    //                 return $this->returnSuccess('Successful operation', []);
    //             } catch (\Throwable $e) {

    //                 DB::rollback();

    //                 return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
    //             }
    //         } else {
    //             return $this->returnErrorData('Data Not Found', 404);
    //         }
    //     } else {
    //         return $this->returnErrorData('Data Not Found', 404);
    //     }
    // }
}
