<?php

namespace App\Http\Controllers;

use App\Models\LoanApplication;
use App\Models\PayrollRound;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class LoanApplicationController extends Controller
{
    public function store(Request $request)
    {
        $loginBy = $request->login_by;

        $validator = Validator::make($request->all(), [
            'user_id'               => 'required|integer',
            'request_amount'        => 'required|numeric|min:0.01',
            'term_months'           => 'required|integer|min:1',
            'interest_mode'         => 'in:flat_amortized,flat_balloon',
            'purpose'               => 'nullable|string',
            'approver_id'           => 'required|integer',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        $paymentsPerMonth = PayrollRound::max('round');

        $loanApplication = LoanApplication::create([
            'user_id'               => $request->user_id,
            'request_amount'        => $request->request_amount,
            'term_months'           => $request->term_months,
            'interest_mode'         => $request->interest_mode,
            'payments_per_month'    => $paymentsPerMonth,
            'purpose'               => $request->purpose,
            'status'                => 'pending',
            'create_by'             => $loginBy->user_id,
            'approver_id'           => $request->approver_id,
        ]);

        return $this->returnSuccess('Loan application created', $loanApplication);
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'request_amount'        => 'nullable|numeric|min:0.01',
            'term_months'           => 'nullable|integer|min:1',
            'interest_mode'         => 'nullable|in:flat_amortized,flat_balloon',
            'payments_per_month'    => 'nullable|integer|in:1,2',
            'purpose'               => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        DB::beginTransaction();

        try {
            $loanApplication = LoanApplication::find($id);

            if (!$loanApplication) {
                return $this->returnErrorData('Loan application not found', 404);
            }

            $loanApplication->fill($request->only([
                'request_amount',
                'term_months',
                'interest_mode',
                'payments_per_month',
                'purpose'
            ]));

            $loanApplication->save();

            // log
            $userId = $request->login_id;
            $type = 'Update Loan Application';
            $description = 'User ' . $userId . ' has ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Loan application updated', $loanApplication);
        } catch (\Throwable $e) {
            DB::rollback();
            Log::error($e);
            return $this->returnErrorData('Something went wrong Please try again ' . $e->getMessage(), 500);
        }
    }

    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();

        try {

            $loanApplication = LoanApplication::find($id);
            if (!$loanApplication) {
                return $this->returnErrorData('Loan application not found', 404);
            }

            $loanApplication->delete();

            //log
            $userId = $request->login_id;
            $type = 'Delete Loan Application';
            $description = 'User ' . $userId . ' has ' . $type;
            $this->Log($userId, $description, $type);
            //
            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            Log::error($e);

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function approve(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:approved,rejected',
            'reason' => 'required_if:status,rejected|string|nullable',
        ]);

        if ($validator->fails()) {
            return $this->errorData('ข้อมูลไม่ถูกต้อง', $validator->errors(), 422);
        }

        DB::beginTransaction();

        try {
            $loanApplication = LoanApplication::find($id);

            if (!$loanApplication) {
                return $this->returnErrorData('Loan application not found', 404);
            }

            $loanApplication->status = $request->status;
            $loanApplication->reason = $request->reason;
            $loanApplication->save();

            //log
            $userId = $request->login_id;
            $type = 'Approve Loan Application';
            $description = 'User ' . $userId . ' has ' . $type;
            $this->Log($userId, $description, $type);
            //

            DB::commit();

            return $this->returnSuccess('Loan application approved successfully', $loanApplication);
        } catch (\Throwable $e) {
            DB::rollback();

            Log::error($e);

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    public function show(Request $request, $id)
    {
        $loanApplication = LoanApplication::with('user')
            ->with('loan')
            ->find($id);

        if (!$loanApplication) {
            return $this->returnErrorData('Loan application not found', 404);
        }

        return $this->returnSuccess('Successful', $loanApplication);
    }

    public function index(Request $request)
    {
        //
    }

    public function datatables(Request $request)
    {
        $loginBy = $request->login_by;

        $input = $request->all();
        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);
        $filter = $request->input('filter', "");

        $searchable = [
            'users.first_name',
            'users.last_name',
        ];

        $dataset = DB::table('loan_applications')->select([
            'loan_applications.id',
            'loan_applications.user_id',
            'users.first_name',
            'users.last_name',
            'loan_applications.request_amount',
            'loan_applications.term_months',
            'loan_applications.interest_mode',
            'loan_applications.payments_per_month',
            'loan_applications.purpose',
            'loan_applications.status',
            'loan_applications.reason',
            'loan_applications.created_at',
            'loan_applications.updated_at',
        ])
            ->join('users', 'loan_applications.user_id', '=', 'users.id')
            ->where(function ($query) use ($search, $searchable, $filter) {
                if ($search['value']) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                    }
                }

                if ($filter['status']) {
                    $query->where('loan_applications.status', $filter['status']);
                }
            })
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);


        return $dataset;
    }
}
