<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Imports\OtImport;
use App\Models\Ot;
use App\Models\User;
use App\Models\UserFiles;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class UserFilesController extends Controller
{
    public function getFiles(Request $request)
    {

        $user_id = $request->user_id;

        $userfile = UserFiles::with('user')
            ->with('user');
        if ($user_id) {
            $userfile->where('user_id', $user_id);
        }

        $UserFile =  $userfile->get()->toarray();

        if (!empty($UserFile)) {

            for ($i = 0; $i < count($UserFile); $i++) {
                $UserFile[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $UserFile);
    }
    public function UserFilePage(Request $request) {}


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาเลือกพนักงาน', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        } else if (!isset($request->data)) {
            return $this->returnErrorData('กรุณาเลือก Path', 404);
        }

        DB::beginTransaction();

        try {
            if (!empty($request->data)) {

                for ($i = 0; $i < count($request->data); $i++) {
                    $UserFile = new UserFiles();
                    $UserFile->user_id = $request->user_id;
                    $UserFile->name = $request->data[$i]['name'];
                    $UserFile->data = $request->data[$i]['path'];
                    $UserFile->created_at = $loginBy->user_id;

                    $UserFile->save();
                }
            }

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $UserFile = UserFiles::where("user_id", $id)->get();
        return $this->returnSuccess('Successful', $UserFile);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }



    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }



        DB::beginTransaction();

        try {

            $UserFile = UserFiles::find($id);

            $UserFile->user_id = $request->user_id;
            $UserFile->data = $request->data;

            $UserFile->updated_at = Carbon::now()->toDateTimeString();
            $UserFile->save();


            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $UserFile = UserFiles::find($id);

            $UserFile->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
    public function addFiles(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $id = $request->user_id;

            $files = $request->files;

            if ($request->has('files')) {
                foreach ($request->input('files') as $index => $fileMeta) {
                    // ดึงชื่อไฟล์จาก input
                    $name = $fileMeta['name'] ?? null;

                    // ดึงไฟล์จาก form-data
                    $uploadedFile = $request->file("files.$index.data");

                    if ($uploadedFile && $name) {
                        $UserFile = new UserFiles();
                        $UserFile->user_id = $id;
                        $UserFile->name = $name;
                        $UserFile->data = $this->uploadFile($uploadedFile, '/files/user/');
                        $UserFile->created_at = Carbon::now()->toDateTimeString();
                        $UserFile->save();
                    }
                }
            }

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }
}
