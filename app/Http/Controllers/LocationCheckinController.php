<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use App\Models\LocationCheckin;
use Illuminate\Support\Facades\DB;

class LocationCheckinController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'required|integer|min:1',
            'active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->returnErrorData($validator->errors()->first(), 400);
        }

        $checkLocation = LocationCheckin::where('name', $request->name)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkLocation) {
            return $this->returnErrorData('มี location นี้ในระบบแล้ว', 400);
        }

        DB::beginTransaction();

        try {
            $location = new LocationCheckin();
            $location->branch_id = $login_branch_id;
            $location->name = $request->name;
            $location->latitude = $request->latitude;
            $location->longitude = $request->longitude;
            $location->radius = $request->radius;
            $location->active = 1;

            $location->save();
            DB::commit();

            return $this->returnSuccess('ดำเนินการสำเร็จ', $location);
        } catch (\Throwable $e) {
            DB::rollback();
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 500);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $location = LocationCheckin::findOrFail($id);
            return $this->returnSuccess('ดำเนินการสำเร็จ', $location);
        } catch (\Throwable) {
            return $this->returnErrorData('ไม่พบข้อมูล', 404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $validator = Validator::make($request->all(), [
            'name' => 'string',
            'latitude' => 'numeric',
            'longitude' => 'numeric',
            'radius' => 'integer|min:1',
            'active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->returnErrorData($validator->errors()->first(), 400);
        }

        // เช็คว่ามี location นี้แล้วรึป่าว ยกเว้นตัวเอง
        $checkLocation = LocationCheckin::where('name', $request->name)
            ->where('branch_id', $login_branch_id)
            ->where('id', '!=', $id)
            ->exists();

        if ($checkLocation) {
            return $this->returnErrorData('มี location ชื่อนี้ในระบบแล้ว', 400);
        }

        DB::beginTransaction();

        try {
            $location = LocationCheckin::find($id);

            if (!$location) {
                return $this->returnErrorData('ไม่พบ Location ที่ระบุ', 404);
            }

            $location->name = $request->name;
            $location->latitude = $request->latitude;
            $location->longitude = $request->longitude;
            $location->radius = $request->radius;
            $location->active = $request->active;

            $location->save();
            DB::commit();

            return $this->returnSuccess('อัปเดตข้อมูลสำเร็จ', $location);
        } catch (\Throwable $e) {
            DB::rollback();
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 500);
        }
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        DB::beginTransaction();

        try {
            $location = LocationCheckin::find($id);

            if (!$location) {
                return $this->returnErrorData('ไม่พบ Location ที่ระบุ', 404);
            }

            $location->delete();
            DB::commit();

            return $this->returnSuccess('ลบข้อมูลสำเร็จ', $location);
        } catch (\Throwable $e) {
            DB::rollback();
            return $this->returnErrorData('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง ' . $e, 500);
        }
    }


    public function datatable(Request $request)
    {
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $searchable = [
            'name',
        ];

        $d = LocationCheckin::select()
            ->where(function ($query) use ($searchable, $search) {
                if (isset($search['value']) && !empty($search['value'])) {
                    foreach ($searchable as $field) {
                        $query->orWhere($field, 'LIKE', '%' . $search['value'] . '%');
                    }
                }
            });

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        $dataset =  $d->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);

        return $this->returnSuccess('สำเร็จ', $dataset);
    }


    //
    public function canCheckIn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return $this->returnErrorData($validator->errors()->first(), 400);
        }

        $userLatitude = $request->latitude;
        $userLongitude = $request->longitude;

        // location ที่ active อยู่ทั้งหมด
        $locations = LocationCheckin::where('active', true)->get();

        foreach ($locations as $location) {
            // สูตร Haversine
            $userLat = deg2rad($userLatitude);
            $userLon = deg2rad($userLongitude);
            $locLat = deg2rad($location->latitude);
            $locLon = deg2rad($location->longitude);

            $deltaLat = $locLat - $userLat;
            $deltaLon = $locLon - $userLon;

            $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
                cos($userLat) * cos($locLat) *
                sin($deltaLon / 2) * sin($deltaLon / 2);

            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
            $distance = 6371 * $c;

            // เช็คว่าอยู่ในรัศมีรึป่าว
            if (($distance * 1000) <= $location->radius) {
                return $this->returnSuccess('ดำเนินการสำเร็จ', [
                    "location_name" => $location->name,
                    'can_check_in' => true
                ]);
            }
        }

        return $this->returnSuccess('ดำเนินการสำเร็จ', [
            "location_name" => null,
            'can_check_in' => false
        ]);
    }
}
