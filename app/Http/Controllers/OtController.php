<?php

namespace App\Http\Controllers;

use App\Imports\OtImport;
use App\Models\Holiday;
use App\Models\Leave_table_date;
use App\Models\Ot;
use App\Models\Ot_shift;
use App\Models\Ot_time_log;
use App\Models\User;
use App\Models\Work_shift;
use App\Models\Work_shift_time;
use App\Models\Zk_time;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class OtController extends Controller
{

    public function cronjobCalculateOtWithTimeStamp(Request $request)
    {

        $date = $request->date;
        // $date = date('Y-m-d');
        // $date = '2025-07-05';

        $user_id = $request->user_id;


        $User = User::query();
        if ($user_id) {
            $User->Where('id', $user_id);
        }

        $User = $User->where('status', 1)->get();

        for ($i = 0; $i < count($User); $i++) {

            $userId = $User[$i]->id;

            $updateCalculateOtWithTimeStamp = $this->updateCalculateOtWithTimeStamp($date, $userId);
            // dd($updateCalculateOtWithTimeStamp);
        }
        return $this->returnSuccess('Successful', null);
    }

    public function updateCalculateOtWithTimeStamp($date, $user_id)
    {
        $User = User::find($user_id);
        $Ot_shifts = Ot_shift::where('status', 1)->get();



        // return $Ot_shifts;

        foreach ($Ot_shifts as $Ot_shift) {
            $ot_shift_type = $Ot_shift->type;

            $ot_type_id = null;
            if ($ot_shift_type  == 'work') {
            } else {
            }

            $requested_time_in = date('Y-m-d H:i:s', strtotime($date . ' ' . $Ot_shift->time_in));
            $requested_time_out = date('Y-m-d H:i:s', strtotime($date . ' ' . $Ot_shift->time_out));

            // ถ้า OT ข้ามวัน เช่น 16:00 → 04:00
            if (strtotime($requested_time_out) <= strtotime($requested_time_in)) {
                $requested_time_out = date('Y-m-d H:i:s', strtotime($requested_time_out . ' +1 day'));
            }

            // รวมผลจากหลาย shift
            $timePeriods = [];

            $work_shift_id = $User->work_shift_id;

            $calculated = $this->calculateOtWithTimeStamp(
                $user_id,
                $ot_shift_type,
                $work_shift_id,
                $date,
                $Ot_shift->time_in,
                $Ot_shift->time_out,
                null
            );

            // dd($calculated);

            if ($calculated['status']) {
                $timePeriods[] = [
                    'start' => $calculated['time_start'],
                    'end' => $calculated['time_end'],
                    'scan_start' => $calculated['scan_time_start'],
                    'scan_end' => $calculated['scan_time_end'],
                    'qty_hour' => $calculated['qty_hour'],
                ];
            }


            if (count($timePeriods) > 0) {
                // หาช่วงเวลารวม
                $earliest = min(array_column($timePeriods, 'start'));
                $latest = max(array_column($timePeriods, 'end'));
                $scan_start = min(array_column($timePeriods, 'scan_start'));
                $scan_end = max(array_column($timePeriods, 'scan_end'));
                $total_hours = array_sum(array_column($timePeriods, 'qty_hour'));


                // dd($scan_start);

                $dif_in = 0;
                $dif_out = 0;
                $remove_hour = 0;
                if ($scan_start > $earliest) {
                    $dif_in = abs($this->TimeDiff($earliest, $scan_start));
                    $remove_hour =  $dif_in;
                }

                if ($scan_end < $latest) {
                    $dif_out = abs($this->TimeDiff($latest, $scan_end));
                    $remove_hour = $dif_out;
                }

                $qty_hour = abs($this->TimeDiff($earliest, $latest)) - $remove_hour;

                $Ot = new Ot();
                $Ot->user_id = $user_id;
                $Ot->ot_type_id = 1;
                $Ot->date = $date;
                $Ot->time_start = $Ot_shift->time_in;
                $Ot->time_end = $Ot_shift->time_out;

                $Ot->actual_scan_time_start = $scan_start; //ค่าจริง
                $Ot->actual_scan_time_end = $scan_end; //ค่าจริง
                $Ot->scan_time_start = $scan_start;
                $Ot->scan_time_end = $scan_end;

                $Ot->qty_hour = $qty_hour;

                $Ot->status = 'approved';

                $Ot->create_by = null;
                $Ot->head_id = null; //id หัวหน้้า

                $Ot->save();
            }
        }
    }

    public function calculateOtWithTimeStamp($user_id, $ot_shift_type, $work_shift_id, $date, $timeIn, $timeOut, $otId = null)
    {

        $response = [
            'status' => false,
            'message' => 'ไม่พบข้อมูล',
            'time_start' => null,
            'time_end' => null,
            'scan_time_start' => null,
            'scan_time_end' => null
        ];
        //
        $User =  User::find($user_id);

        $work_shift_group_id = $User->work_shift_group_id;

        //กลุ่มลงเวลาปกติ
        if ($work_shift_group_id == 1) {

            $startTime = $timeIn;
            $endTime = $timeOut;

            $strTimeIn = date('Y-m-d', strtotime($date)) . ' ' . $startTime;
            $strTimeOut = date('Y-m-d', strtotime($date)) . ' ' . $endTime;


            $minutePlus = 0; //
            $minuteLate = 5;
            $operatorPlus = '+'; //

            $removeBrake = 0;

            $CurrentMinutePlusIn = null;
            $CurrentMinutePlusOut = null;

            $isHoliday = false;
            $isWork = false;
            $isLeave = false;

            //check leave date
            $Leave_table_date =  Leave_table_date::with('leave_table')
                ->where('date', $date)
                ->WhereHas('leave_table', function ($query) use ($user_id) {
                    $query->where('user_id', $user_id);
                    $query->where('status', 'approved');
                })
                ->first();

            if ($Leave_table_date) {
                //is leave date

                $isLeave = true;
                $response = [
                    'status' => false,
                    'message' => 'ไม่พบข้อมูล',
                    'time_start' => null,
                    'time_end' => null,
                    'scan_time_start' => null,
                    'scan_time_end' => null
                ];
                //
            } else {
                //not leave date

                //check holiday
                $Holiday =  Holiday::where('date', $date)->first();
                if ($Holiday) {
                    $isHoliday = true;
                }
                //

                //check working day
                $strDate = date('D', strtotime($date));

                $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                if ($work_shift_id) {
                    $workTime->where('work_shift_id', $work_shift_id);
                }
                $HrWorkShiftTime = $workTime->first();

                if ($HrWorkShiftTime) {
                    if ($HrWorkShiftTime->status == true) {
                        $isWork = true;
                    }
                }
                //

                //check HrWorkShift
                $Work_shift =  Work_shift::find($work_shift_id);



                $shiftStart = $startTime;
                $shiftEnd = $endTime;

                //in
                $check_in_start = date('Y-m-d', strtotime($date)) . ' ' . '00:00';

                if ((strtotime($shiftStart) < strtotime($shiftEnd)) && $shiftStart == '00:00') {
                    $dateYesterday = date('Y-m-d', strtotime($date . ' -1 day'));
                    $check_in_start = date('Y-m-d', strtotime($dateYesterday)) . ' ' . '00:00';
                }

                $check_in_end = date('Y-m-d H:i', strtotime($strTimeIn . ' +' . $minuteLate . ' minutes'));

                //end
                $check_out_in = $strTimeOut;
                $check_out_end = date('Y-m-d', strtotime($date)) . ' ' . '23:59';

                if ((strtotime($shiftStart) > strtotime($shiftEnd))) {
                    $dateTomorrow = date('Y-m-d', strtotime($date . ' +1 day'));
                    $check_out_end = date('Y-m-d', strtotime($dateTomorrow)) . ' ' . '23:59';
                }

                $timeIn = null;
                $TimeInStatus = false;

                $timeOut = null;
                $TimeOutStatus = false;

                /////////////////////////////////  Zk_time check time in ////////////////////////////////////////////
                $Zk_time_in =  Zk_time::where('personnel_id', $User->personnel_id)
                    ->whereBetween('time', [$check_in_start, $check_out_end])
                    ->orderby('time', 'asc')
                    ->first();


                if ($Zk_time_in) {

                    //check late
                    $timeIn = $Zk_time_in->time;

                    if (strtotime($timeIn) > strtotime($check_in_end)) {
                        //late

                        $response = [
                            'status' => false,
                            'message' => 'ไม่สามารถดำเนินการได้ เนื่องจากคุณเวลาการเข้างานของคุณไม่ตรงตามเงื่อนไข',
                            'time_start' => null,
                            'time_end' => null,
                            'scan_time_start' => null,
                            'scan_time_end' => null
                        ];
                    } else {
                        //not late


                        //check time in early
                        if (strtotime($strTimeIn) > strtotime($check_in_end)) {
                            $TimeInStatus = true;
                            $CurrentMinutePlusIn = date('Y-m-d H:i', strtotime($strTimeIn . ' +' . $minutePlus . ' minutes'));
                        } else {
                            $TimeInStatus = true;
                            $CurrentMinutePlusIn = $strTimeIn;
                        }
                    }
                }

                ///////////////////////////////////////////////////////////////////////////////////////////////////////

                /////////////////////////////////  Zk_time check time out /////////////////////////////////////////////

                $Zk_time_out =  Zk_time::where('personnel_id', $User->personnel_id)
                    ->whereBetween('time', [$check_in_start, $check_out_end])
                    ->orderby('time', 'desc')
                    ->first();

                if ($Zk_time_out) {

                    //check over time end
                    $timeOut = $Zk_time_out->time;

                    if (strtotime($timeOut) >= strtotime($check_out_in)) {
                        $TimeOutStatus = true;
                        $CurrentMinutePlusOut = $strTimeOut;

                        $earlyTimeType3Status = true;
                    } else if ((strtotime($timeOut) < strtotime($check_out_in) && (strtotime($timeOut) > strtotime($check_in_end)))) {
                        //check time out early
                        $TimeOutStatus = true;

                        $CurrentMinutePlusOut = $strTimeOut;
                    }
                }

                if ($CurrentMinutePlusIn && $CurrentMinutePlusOut) {
                    if (date('H:i', strtotime($CurrentMinutePlusOut)) === '00:00' && strtotime($CurrentMinutePlusOut) <= strtotime($CurrentMinutePlusIn)) {
                        $CurrentMinutePlusOut = date('Y-m-d H:i', strtotime($CurrentMinutePlusOut . ' +1 day'));
                    }

                    $startTime = strtotime($CurrentMinutePlusIn);
                    $endTime = strtotime($CurrentMinutePlusOut);

                    $totalMinutesIn = ($endTime - $startTime) / 60;
                    $hours_in = floor($totalMinutesIn / 60);



                    $hoursIn = $hours_in - $removeBrake; //remove brake
                    //$minutesIn = $totalMinutesIn % 60;

                    if ($TimeInStatus == true &&  $TimeOutStatus == true) {
                        $response = [
                            'status' => true,
                            'time_start' => $CurrentMinutePlusIn,
                            'time_end' => $CurrentMinutePlusOut,
                            'qty_hour' => $hoursIn,
                            'scan_time_start' =>  $timeIn,
                            'scan_time_end' => $timeOut
                        ];
                    }
                }
            }
        } else {
            $response = [
                'status' => true,
                'time_start' => null,
                'time_end' => null,
                'qty_hour' => null,
                'scan_time_start' =>  null,
                'scan_time_end' => null
            ];
        }

        return $response;
    }

    public function getQtyHourOtUser(Request $request)
    {
        $id = $request->id;

        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data NLeaveTable Found', 404);
        }
        $ot = Ot::with('user')
            ->with('ot_type')
            ->with('user_status.position')
            ->with('user_head.position')
            ->with('user_hr.position')
            ->find($id);

        if (!$ot) {
            return $this->returnErrorData('ไม่พบข้อมูล ot', 404);
        }

        if (!$ot->user->work_shift_id) {
            return $this->returnErrorData('กรุณาเลือกกลุ่มกะการทำงานก่อนดำเนินการ', 404);
        }

        $date_start = $ot->date . ' ' . $ot->time_start;
        $date_end = $ot->date . ' ' . $ot->time_end;

        $time_start = $ot->time_start;
        $time_end = $ot->time_end;

        //check diff date
        $dateInPeriod = $this->dateInPeriod($date_start, $date_end);

        $qty_hour = 0;
        for ($i = 0; $i < count($dateInPeriod); $i++) {

            $strDate = date('D', strtotime($dateInPeriod[$i]));

            //check working day
            $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
            if ($ot->user->work_shift_id) {
                $workTime->where('work_shift_id', $ot->user->work_shift_id);
            }
            $WorkTime =  $workTime->first();


            if ($WorkTime) {

                //check holiday
                $Holiday =  Holiday::where('date', $dateInPeriod[$i])->first();
                if (!$Holiday) {

                    if ($WorkTime->status == true) {

                        //เวลา ot
                        if ($time_end == '00:00') {
                            $time_start = $ot->time_start;
                            $time_end = '24:00';
                        } else {
                            $time_start = $ot->time_start;
                            $time_end = $ot->time_end;
                        }

                        //เวลา break
                        $checkTimeIn = $WorkTime->time_brake_in;
                        $checkTimeOut = $WorkTime->time_brake_out;

                        $timeStartTimestamp = strtotime($time_start);
                        $timeEndTimestamp = strtotime($time_end);
                        $checkTimeTimestamp = strtotime($checkTimeIn);

                        // ตรวจสอบว่าเวลา time_brake_in อยู่ในช่วงเวลาเริ่มต้นและสิ้นสุดหรือไม่
                        if (($checkTimeTimestamp > $timeStartTimestamp && $checkTimeTimestamp < $timeEndTimestamp)) {

                            $qty_diff_brake_hour = $this->TimeDiff($checkTimeIn, $checkTimeOut);
                            $qty_hour += ($this->TimeDiff($time_start, $time_end) - $qty_diff_brake_hour);
                        } else {
                            $qty_hour += ($this->TimeDiff($time_start, $time_end));
                        }
                    }
                }
            }
        }



        return $this->returnSuccess('Successful', $qty_hour);
    }

    public function getOt(Request $request)
    {

        $user_id = $request->user_id;
        $status = $request->status;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $ot = Ot::with('user')
            ->with('ot_type')
            ->with('user_status.position')
            ->with('user_head.position')
            ->with('user_hr.position');

        if ($user_id) {
            $ot->where('user_id', $user_id);
        }

        if ($login_branch_id) {
            $ot->where('branch_id', $login_branch_id);
        }

        if ($status) {
            $ot->where('status', $status);
        }
        $Ot =  $ot->get()->toarray();

        if (!empty($Ot)) {

            for ($i = 0; $i < count($Ot); $i++) {
                $Ot[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $Ot);
    }


    public function OtPage(Request $request)
    {
        $loginBy = $request->login_by;


        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->Page($request);
    }

    public function OtPageLine(Request $request)
    {

        $line_id = $request->line_id;
        $line_head_id = $request->line_head_id;

        if ($line_id) {
            $loginBy = User::where('line_id', $line_id)->first();
        } else if ($line_head_id) {
            $loginBy = User::where('line_id', $line_head_id)->first();
        }

        if (!$loginBy) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $getPermissionViewData = $this->getPermissionViewData($loginBy);

        $request->request->add([
            'login_id' => $getPermissionViewData['login_id'],
            'login_by' => $getPermissionViewData['login_by'],
            'login_permission_view' => $getPermissionViewData['login_permission_view'],
            'login_user_id' => $getPermissionViewData['login_user_id'],
            'login_branch_id' => $getPermissionViewData['login_branch_id'],
            'login_company_id' => $getPermissionViewData['login_company_id'],
        ]);


        return $this->Page($request);
    }

    public function Page(Request $request)
    {

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $user_id = $request->user_id;
        $status = $request->status;
        $head_id = $request->head_id;

        $line_id = $request->line_id;
        $line_head_id = $request->line_head_id;

        $date = $request->date;
        $date_start = $request->date_start;
        $date_end = $request->date_end;

        $login_permission_view = $request->login_permission_view;
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $col = array(
            'id',
            'user_id',
            'ot_type_id',
            'date',
            'time_start',
            'time_end',
            'qty',
            'actual_scan_time_start',
            'actual_scan_time_end',
            'scan_time_start',
            'scan_time_end',
            'status',
            'status_by',
            'status_at',
            'remark',
            'create_by',
            'update_by',
            'created_at',
            'updated_at'
        );

        $d = Ot::select($col)->with('user')
            ->with('ot_type')
            ->with('user_status.position')
            ->with('user_head.position')
            ->with('user_hr.position');

        // if ($login_company_id) {
        //     $d->WhereHas('user', function ($query) use ($login_company_id) {
        //         $query->WhereHas('branch', function ($query) use ($login_company_id) {
        //             $query->where('company_id', $login_company_id);
        //         });
        //     });
        // }


        if ($line_id) {
            $d->WhereHas('user', function ($query) use ($line_id) {
                $query->where('line_id', $line_id);
            });
        }

        if ($line_head_id) {
            $d->WhereHas('user', function ($query) use ($line_head_id) {
                $query->where('line_id', $line_head_id);
            });
        }

        if ($user_id) {
            $d->where('user_id', $user_id);
        }


        if ($login_branch_id) {
            $d->whereHas('user', function ($query) use ($login_branch_id) {
                $query->where('branch_id', $login_branch_id);
            });
        }

        if ($status) {
            $d->where('status', $status);
        }

        if ($head_id) {
            $d->where('head_id', $head_id);
        }

        if ($date_start && $date_end) {
            $d->whereBetween('date', [$date_start, $date_end]);
        }

        if ($date) {
            $d->where('created_at', 'like', '%' . $date . '%');
        }

        $d->orderby($col[$order[0]['column']], $order[0]['dir']);
        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        if (!isset($request->user_id)) {
            return $this->returnErrorData('กรุณาเลือกพนักงาน', 404);
        } else  if (!isset($request->ot_type_id)) {
            return $this->returnErrorData('กรุณาระบุประเภท OT', 404);
        } else  if (!isset($request->date)) {
            return $this->returnErrorData('กรุณาระบุวันที่ทำงาน', 404);
        } else  if (!isset($request->time_start)) {
            return $this->returnErrorData('กรุณาระบุเวลาเริ่มต้น', 404);
        } else  if (!isset($request->time_end)) {
            return $this->returnErrorData('กรุณาระบุเวลาสิ้นสุด', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        } else if (!isset($loginBy->head_id)) {
            return $this->returnErrorData('กรุณาแจ้งเจ้าหน้าที่ HR เพื่อระบุหัวหน้างานก่อนดำเนินการ', 404);
        }

        $user_id = $loginBy->id;
        return  $this->addOt($user_id, $request, $loginBy);
    }

    public function addLine(Request $request)
    {

        $line_id = $request->line_id;

        if (!isset($request->line_id)) {
            return $this->returnErrorData('กรุณาส่ง line_id', 404);
        } else  if (!isset($request->ot_type_id)) {
            return $this->returnErrorData('กรุณาระบุประเภท OT', 404);
        } else  if (!isset($request->date)) {
            return $this->returnErrorData('กรุณาระบุวันที่ทำงาน', 404);
        } else  if (!isset($request->time_start)) {
            return $this->returnErrorData('กรุณาระบุเวลาเริ่มต้น', 404);
        } else  if (!isset($request->time_end)) {
            return $this->returnErrorData('กรุณาระบุเวลาสิ้นสุด', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();

        if (!$loginBy) {
            return $this->returnErrorData('ไม่พบข้อมูลเจ้าหน้าที่', 404);
        } else if (!isset($loginBy->head_id)) {
            return $this->returnErrorData('กรุณาแจ้งเจ้าหน้าที่ HR เพื่อระบุหัวหน้างานก่อนดำเนินการ', 404);
        } else if (!isset($loginBy->work_shift_id)) {
            return $this->returnErrorData('กรุณาแจ้งเจ้าหน้าที่ HR เพื่อระบุกลุ่มกะการทำงานก่อนดำเนินการ', 404);
        }

        $user_id = $loginBy->id;
        return  $this->addOt($user_id, $request, $loginBy);
    }

    public function addOt($user_id, $request, $loginBy)
    {

        DB::beginTransaction();

        try {

            $Ot = new Ot();
            $Ot->user_id = $loginBy->id;
            $Ot->ot_type_id = $request->ot_type_id;
            $Ot->date = $request->date;
            $Ot->time_start = $request->time_start;
            $Ot->time_end = $request->time_end;

            $Ot->status = 'open';

            $Ot->create_by = $loginBy->user_id;
            $Ot->head_id = $loginBy->head_id; //id หัวหน้้า

            $Ot->save();

            DB::commit();

            return $this->returnSuccess('Successful operation', []);
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $Ot = Ot::with('user')
            ->with('ot_type')
            ->with('user_status.position')
            ->with('user_head.position')
            ->with('user_hr.position')
            ->with('ot_time_logs.user.position')
            ->find($id);
        return $this->returnSuccess('Successful', $Ot);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }



        DB::beginTransaction();

        try {

            $Ot = Ot::find($id);

            $Ot->ot_type_id = $request->ot_type_id;
            $Ot->date = $request->date;
            $Ot->time_start = $request->time_start;
            $Ot->time_end = $request->time_end;

            $Ot->update_by = $loginBy->user_id;
            $Ot->updated_at = Carbon::now()->toDateTimeString();
            $Ot->save();


            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $Ot = Ot::find($id);

            $Ot->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }


    public function ApprovedOt(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->approveOt($id, $request, $loginBy);
    }

    public function ApprovedOtLine(Request $request, $id)
    {
        $line_id = $request->line_id;

        if (!isset($line_id)) {
            return $this->returnErrorData('[line_id] Data Not Found', 404);
        }

        $loginBy = User::where('line_id', $line_id)->first();

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        return $this->approveOt($id, $request, $loginBy);
    }

    public function approveOt($id, $request, $loginBy)
    {

        DB::beginTransaction();

        try {

            $Ot = Ot::with('user.position')
                ->with('ot_type')
                ->with('user_status.position')
                ->with('user_head.position')
                ->with('user_hr.position')
                ->find($id);

            $Ot->status = $request->status;

            if ($Ot->status == 'process') {
                $Ot->head_by = $loginBy->user_id;
                $Ot->head_at = Carbon::now()->toDateTimeString();
            }

            if ($Ot->status == 'approved') {
                $Ot->qty_hour = $request->qty_hour;
                $Ot->ot_type->type = $request->type;
                if ($request->type == 'rate') {
                    $Ot->ot_type->rate = $request->rate;
                    $Ot->qty = $this->calculateOtUser($Ot->user, $Ot->ot_type, $Ot->qty_hour, $Ot->ot_type->type);
                } else if ($request->type == 'amount') {
                    $Ot->ot_type->amount = $request->amount;
                    $Ot->qty = $this->calculateOtUser($Ot->user, $Ot->ot_type, $Ot->qty_hour, $Ot->ot_type->type);
                }

                $Ot->hr_id = $loginBy->id;
                $Ot->status_by = $loginBy->user_id;
                $Ot->status_at = Carbon::now()->toDateTimeString();
            }

            //หัวหน้า cancel
            if ($request->status == 'head_cancel') {
                $Ot->head_remark = $request->remark;

                $Ot->status_by = $loginBy->user_id;
                $Ot->status_at = Carbon::now()->toDateTimeString();
            }

            if ($Ot->status == 'cancel') {
                $Ot->remark = $request->remark;

                $Ot->status_by = $loginBy->user_id;
                $Ot->status_at = Carbon::now()->toDateTimeString();
            }


            $Ot->save();


            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }


    public function updateTimeOt(Request $request, $id)
    {


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }



        DB::beginTransaction();

        try {

            $Ot = Ot::find($id);

            $Ot->scan_time_start = $request->scan_time_start;
            $Ot->scan_time_end = $request->scan_time_end;

            $Ot->time_start = $request->time_start;
            $Ot->time_end = $request->time_end;

            $Ot->qty_hour = $request->qty_hour;
            $Ot->ot_type->type = $request->type;
            if ($request->type == 'rate') {
                $Ot->ot_type->rate = $request->rate;
                $Ot->qty = $this->calculateOtUser($Ot->user, $Ot->ot_type, $Ot->qty_hour, $Ot->ot_type->type);
            } else if ($request->type == 'amount') {
                $Ot->ot_type->amount = $request->amount;
                $Ot->qty = $this->calculateOtUser($Ot->user, $Ot->ot_type, $Ot->qty_hour, $Ot->ot_type->type);
            }

            $Ot->update_by = $loginBy->user_id;
            $Ot->updated_at = Carbon::now()->toDateTimeString();
            $Ot->save();

            //add log update time
            $Ot_time_log = new Ot_time_log();
            $Ot_time_log->ot_id = $Ot->id;
            $Ot_time_log->user_id = $loginBy->id;
            $Ot_time_log->date = $Ot->date;
            $Ot_time_log->time_start = $Ot->time_start;
            $Ot_time_log->time_end = $Ot->time_end;
            $Ot_time_log->qty = $Ot->qty;
            $Ot_time_log->qty_hour = $Ot->qty_hour;
            $Ot_time_log->save();
            //

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }
}
