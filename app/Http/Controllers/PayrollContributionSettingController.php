<?php

namespace App\Http\Controllers;

use App\Imports\PayrollContributionSettingImport;
use App\Models\PayrollContribution;
use App\Models\PayrollContributionSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class PayrollContributionSettingController extends Controller
{

    public function getPayrollContributionSetting(Request $request)
    {

        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $q = PayrollContributionSetting::where('status', 1)
            ->with('user_create');

        if ($login_branch_id) {
            $q->where('branch_id', $login_branch_id);
        }

        $PayrollContributionSetting = $q->get()->toArray();


        if (!empty($PayrollContributionSetting)) {

            for ($i = 0; $i < count($PayrollContributionSetting); $i++) {
                $PayrollContributionSetting[$i]['No'] = $i + 1;
            }
        }

        return $this->returnSuccess('Successful', $PayrollContributionSetting);
    }

    public function PayrollContributionSettingPage(Request $request)
    {

        $columns = $request->columns;
        $length = $request->length;
        $order = $request->order;
        $search = $request->search;
        $start = $request->start;
        $page = $start / $length + 1;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;

        $col = array('id', 'branch_id', 'code', 'name', 'employer_rate', 'employee_rate', 'max_wage_calculate_status', 'min_wage_base', 'max_wage_base', 'status', 'create_by', 'update_by', 'created_at', 'updated_at');

        $d = PayrollContributionSetting::select($col)->with('user_create');

        if ($login_branch_id) {
            $d->where('branch_id', $login_branch_id);
        }

        $d->orderBy($col[$order[0]['column']], $order[0]['dir']);

        if ($search['value'] != '' && $search['value'] != null) {

            //search datatable
            $d->where(function ($query) use ($search, $col) {
                foreach ($col as &$c) {
                    $query->orWhere($c, 'like', '%' . $search['value'] . '%');
                }
            });
        }

        $d = $d->orderby('id', 'desc')->paginate($length, ['*'], 'page', $page);

        if ($d->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $length);

            for ($i = 0; $i < count($d); $i++) {

                $No = $No + 1;
                $d[$i]->No = $No;
            }
        }

        return $this->returnSuccess('Successful', $d);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $loginBy = $request->login_by;

        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        if (!isset($request->name)) {
            return $this->returnErrorData('กรุณาใส่ชื่อเงินสบทบ', 404);
        } else if (!isset($request->code)) {
            return $this->returnErrorData('กรุณาระบุรหัสเงินสบทบ', 404);
        } else if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        } else if (!isset($request->employer_rate)) {
            return $this->returnErrorData('กรุณาระบุอัตราเงินสบทบนายจ้าง', 404);
        } else if (!isset($request->employee_rate)) {
            return $this->returnErrorData('กรุณาระบุอัตราเงินสบทบลูกจ้าง', 404);
        } else if (!isset($request->max_wage_calculate_status)) {
            return $this->returnErrorData('สถานะการคิดฐานเงินสมทบ', 404);
        } else if (!isset($request->min_wage_base)) {
            return $this->returnErrorData('ฐานเงินขั้นต่ำการคิดเงินสมทบ', 404);
        } else if (!isset($request->max_wage_base)) {
            return $this->returnErrorData('ฐานเงินสูงสุดการคิดเงินสมทบ', 404);
        }

        $code = $request->code;
        $name = $request->name;

        $checkName = PayrollContributionSetting::where('code', $code)
            ->where('branch_id', $login_branch_id)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this code in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $PayrollContributionSetting = new PayrollContributionSetting();
                $PayrollContributionSetting->branch_id = $login_branch_id;
                $PayrollContributionSetting->code = $code;
                $PayrollContributionSetting->name = $name;
                $PayrollContributionSetting->employer_rate = $request->employer_rate;
                $PayrollContributionSetting->employee_rate = $request->employee_rate;
                $PayrollContributionSetting->max_wage_calculate_status = $request->max_wage_calculate_status;
                $PayrollContributionSetting->min_wage_base = $request->min_wage_base;
                $PayrollContributionSetting->max_wage_base = $request->max_wage_base;

                $PayrollContributionSetting->status = 1;

                $PayrollContributionSetting->create_by = $loginBy->user_id;

                $PayrollContributionSetting->save();

                //log
                $userId = $loginBy->user_id;
                $type = 'Add ประเภทเงินสบทบ';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnSuccess('Successful operation', []);
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again' . $e, 404);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $PayrollContributionSetting = PayrollContributionSetting::find($id);
        return $this->returnSuccess('Successful', $PayrollContributionSetting);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $login_user_id = $request->login_user_id;
        $login_branch_id = $request->login_branch_id;
        $login_company_id = $request->login_company_id;


        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        $code = $request->code;
        $name = $request->name;

        $checkName = PayrollContributionSetting::where('id', '!=', $id)
            ->where('branch_id', $login_branch_id)
            ->where('code', $code)
            ->first();

        if ($checkName) {
            return $this->returnErrorData('There is already this code in the system', 404);
        } else {

            DB::beginTransaction();

            try {

                $PayrollContributionSetting = PayrollContributionSetting::find($id);
                $PayrollContributionSetting->code = $code;
                $PayrollContributionSetting->name = $name;
                $PayrollContributionSetting->employer_rate = $request->employer_rate;
                $PayrollContributionSetting->employee_rate = $request->employee_rate;
                $PayrollContributionSetting->max_wage_calculate_status = $request->max_wage_calculate_status;
                $PayrollContributionSetting->min_wage_base = $request->min_wage_base;
                $PayrollContributionSetting->max_wage_base = $request->max_wage_base;

                $PayrollContributionSetting->update_by = $loginBy->user_id;
                $PayrollContributionSetting->updated_at = Carbon::now()->toDateTimeString();

                $PayrollContributionSetting->save();
                //log
                $userId = $loginBy->user_id;
                $type = 'Edit ประเภทเงินสบทบ';
                $description = 'User ' . $userId . ' has ' . $type . ' ' . $PayrollContributionSetting->name;
                $this->Log($userId, $description, $type);
                //

                DB::commit();

                return $this->returnUpdate('Successful operation');
            } catch (\Throwable $e) {

                DB::rollback();

                return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $loginBy = $request->login_by;

        if (!isset($loginBy)) {
            return $this->returnErrorData('[login_by] Data Not Found', 404);
        }

        DB::beginTransaction();

        try {

            $PayrollContributionSetting = PayrollContributionSetting::find($id);

            //log
            $userId = $loginBy->user_id;
            $type = 'Delete ประเภทเงินสบทบ';
            $description = 'User ' . $userId . ' has ' . $type . ' ' . $PayrollContributionSetting->name;
            $this->Log($userId, $description, $type);
            //

            $PayrollContributionSetting->delete();

            DB::commit();

            return $this->returnUpdate('Successful operation');
        } catch (\Throwable $e) {

            DB::rollback();

            return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
        }
    }

    // public function ImportPayrollContributionSetting(Request $request)
    // {

    //     $loginBy = $request->login_by;

    //     if (!isset($loginBy)) {
    //         return $this->returnErrorData('User information not found. Please login again', 404);
    //     }

    //     $file = request()->file('file');
    //     $fileName = $file->getClientOriginalName();

    //     $Data = Excel::toArray(new PayrollContributionSettingImport(), $file);
    //     $data = $Data[0];

    //     if (count($data) > 0) {

    //         $insert_data = [];

    //         for ($i = 0; $i < count($data); $i++) {

    //             $name = trim($data[$i]['name']);

    //             $row = $i + 2;

    //             if ($name == '') {
    //                 return $this->returnErrorData('Row excel data ' . $row . 'please enter name', 404);
    //             }

    //             //check row sample
    //             if ($name == 'SIMPLE-000') {
    //                 //
    //             } else {

    //                 // //check name
    //                 // $PayrollContributionSetting = PayrollContributionSetting::where('name', $name)->first();
    //                 // if ($PayrollContributionSetting) {
    //                 //     return $this->returnErrorData('PayrollContributionSetting ' . $name . ' was information information is already in the system', 404);
    //                 // }

    //                 //check dupicate data form file import
    //                 for ($j = 0; $j < count($insert_data); $j++) {

    //                     if ($name == $insert_data[$j]['name']) {
    //                         return $this->returnErrorData('PayrollContributionSetting ' . $name . ' There is duplicate data in the import file', 404);
    //                     }
    //                 }
    //                 ///

    //                 $insert_data[] = array(
    //                     'name' => $name,
    //                     'status' => 1,
    //                     'created_at' => date('Y-m-d H:i:s'),
    //                     'updated_at' => date('Y-m-d H:i:s'),
    //                 );
    //             }
    //         }

    //         if (!empty($insert_data)) {

    //             DB::beginTransaction();

    //             try {

    //                 //updateOrInsert
    //                 for ($i = 0; $i < count($insert_data); $i++) {

    //                     DB::table('ประเภทเงินสบทบ')
    //                         ->updateOrInsert(
    //                             [
    //                                 'id' => trim($data[$i]['id']), //id
    //                             ],
    //                             $insert_data[$i]
    //                         );
    //                 }
    //                 //

    //                 DB::commit();

    //                 //log
    //                 $userId = $loginBy->user_id;
    //                 $type = 'Import ประเภทเงินสบทบ';
    //                 $description = 'User ' . $userId . ' has ' . $type . ' ' . $name;
    //                 $this->Log($userId, $description, $type);
    //                 //

    //                 DB::commit();

    //                 return $this->returnSuccess('Successful operation', []);
    //             } catch (\Throwable $e) {

    //                 DB::rollback();

    //                 return $this->returnErrorData('Something went wrong Please try again ' . $e, 404);
    //             }
    //         } else {
    //             return $this->returnErrorData('Data Not Found', 404);
    //         }
    //     } else {
    //         return $this->returnErrorData('Data Not Found', 404);
    //     }
    // }
}
