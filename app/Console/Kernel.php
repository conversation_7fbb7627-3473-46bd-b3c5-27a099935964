<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        commands\UserAttendance::class,
        commands\UserLeavePermission::class,
        commands\GenerateWarnings::class,
        commands\DeleImageZkWhenEndDate::class,
        commands\CalculateOtWithTimeStamp::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('database:backup')->daily();
        $schedule->command('userAttendance:cron')->dailyAt('01:00');
        $schedule->command('userLeavePermission:cron')->dailyAt('16:27'); //userLeavePermission
        $schedule->command('generateWarnings:cron')->monthly();
        $schedule->command('deleImageZkWhenEndDate:cron')->dailyAt('02:00');

        $schedule->command('calculateOtWithTimeStamp:cron')->dailyAt('02:00');
        // $schedule->command('generateWarnings:cron')->everyMinute();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
