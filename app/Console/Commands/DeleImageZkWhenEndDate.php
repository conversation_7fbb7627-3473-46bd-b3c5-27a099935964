<?php

namespace App\Console\Commands;

use App\Http\Controllers\ZkController;
use App\Models\Holiday;
use App\Models\Leave_table_date;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\Work_shift_time;
use App\Models\WorkTime;
use App\Models\Zk_time;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DeleImageZkWhenEndDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deleImageZkWhenEndDate:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();

        try {

            $dateNow = date('Y-m-d');

            $User = User::with('position')->where('status', 0)
                ->where('end_date',  $dateNow)
                ->get();

            for ($i = 0; $i < count($User); $i++) {

                //////////////// zk ////////////////////////////////
                $data = [
                    "userNo" => $User[$i]->user_id,
                    "sns" => [$User[$i]->zk_sn],
                ];

                $zkController = new ZkController();
                $x = $zkController->delete($data);

                /////////////////////////////////////////////////////
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
        }
    }
}
