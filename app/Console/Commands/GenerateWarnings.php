<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\Warning;
use App\Models\Work_shift_time;
use Carbon\Carbon;

class GenerateWarnings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generateWarnings:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command generate warnings';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
       
        info("generateWarnings:cron");

        $User = User::where('status', 1)->get();
        // $User = User::where('id', 28)->get();;

                
        for ($i = 0; $i < count($User); $i++) {

            // จำนวนเขข้าสาย
            $lateCount = User_attendance::where('user_id', $User[$i]->id)
                ->whereMonth('date', now()->subMonth()->month)
                ->whereYear('date', now()->subMonth()->year)
                ->where('type', 'late') 
                ->count();
            
            // จำนวนเวลาสาย
            $attendances  = User_attendance::with('zk_time') 
                ->where('user_id', $User[$i]->id)
                ->whereMonth('date', now()->subMonth()->month)
                ->whereYear('date', now()->subMonth()->year)
                ->whereNotNull('zkt_time_id')
                ->where('type', 'late')
                ->get();


            // ---------- คำนวนเวลาสาย ---------------
            $totalLateTime = 0;
            foreach ($attendances as $att) {

                // วันที่ทำงาน
                $workDate = Carbon::parse($att->date)->format('Y-m-d');
                $WorkTime = Work_shift_time::where('work_shift_id', $att->zk_time->id)->first()?->time_in_end;  // เวลาที่กำหนดให้เข้างาน

                // สร้าง datetime ที่รวมวัน + เวลา
                $checkIn = Carbon::parse($att->zk_time->time);
                $workTime = Carbon::parse($workDate . ' ' . $WorkTime);


                 info('workTime: ' . $workTime . ' minutes' . ' checkIn: ' . $checkIn . ' minutes');

                // คำนวนเวลาสาย
                if($checkIn > $workTime ){
                    $lateMinutes = $checkIn->diffInMinutes($workTime); // หาเวลาต่างกัน
                    $totalLateTime += $lateMinutes;

                    info('totalLateTime: ' . $totalLateTime . ' minutes');

                }
            }

            info("user_id: " . $User[$i]->id . " lateCount: " . $lateCount . " totalLateTime: " . $totalLateTime);

            if ($lateCount > 10 || $totalLateTime >= 50){

                // สร้างใบเตือน
                $warning = new Warning();
                $warning->branch_id = $User[$i]->branch_id;
                $warning->user_id = $User[$i]->id;
                $warning->date = now()->format('Y-m-d');
                $warning->title = "การฝ่าฝืนคำสั่ง ระเบียบ หรือข้อบังคับเกี่ยวกับการทำงาน";

                $date = Carbon::now()->subMonth(); // เดือนที่แล้ว

                // แปลงเดือนเป็นไทย
                $thaiMonths = [
                    1 => 'มกราคม', 2 => 'กุมภาพันธ์', 3 => 'มีนาคม',
                    4 => 'เมษายน', 5 => 'พฤษภาคม', 6 => 'มิถุนายน',
                    7 => 'กรกฎาคม', 8 => 'สิงหาคม', 9 => 'กันยายน',
                    10 => 'ตุลาคม', 11 => 'พฤศจิกายน', 12 => 'ธันวาคม'
                ];

                $monthName = $thaiMonths[(int)$date->format('n')];
                $year = (int)$date->format('Y') + 543;

                $thaiDate = "$monthName $year";

                if($lateCount > 10){
                    $warning->description = "สายเกิน 10 ครั้ง ภายใน 1 เดือน ($thaiDate)";
                }
                else if($totalLateTime >= 50){
                    $warning->description = "สายรวมเกิน 50 นาที ภายใน 1 เดือน ($thaiDate)";

                }else if ( $lateCount > 10 && $totalLateTime >= 50){
                    $warning->description = "สายเกิน 10 ครั้งและสายรวมเกิน 50 นาที ภายใน 1 เดือน ($thaiDate)";
                }

                $warning->punishment = "ตักเตือน";
                $warning->save();

                info("update success");

            }



        }
    }
}
