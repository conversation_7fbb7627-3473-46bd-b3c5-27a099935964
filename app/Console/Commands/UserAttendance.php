<?php

namespace App\Console\Commands;

use App\Models\Holiday;
use App\Models\Leave_table_date;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\Work_shift_time;
use App\Models\WorkTime;
use App\Models\Zk_time;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UserAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'userAttendance:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();

        try {

            $dateNow = date('Y-m-d');
            $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

            $User = User::with('position')->where('status', 1)->get();

            for ($i = 0; $i < count($User); $i++) {

                //check duplicate Attendance
                $check = User_attendance::where('user_id', $User[$i]->id)
                    ->where('date', $dateYesterday)
                    ->first();

                if (!$check) {

                    // เช็คว่าอยู่กลุ่ม ผู้บริหาร ป่าว
                    if($User[$i]->work_shift_group_id == 2){

                        //normal
                        $User_attendance =  new User_attendance();
                        $User_attendance->date =  $dateYesterday;
                        $User_attendance->user_id =  $User[$i]->id;
                        $User_attendance->zkt_time_id = null;
                        $User_attendance->type = 'normal';
                        $User_attendance->leave_table_id = null;
                        $User_attendance->status = true;
                        $User_attendance->save();

                        continue; // ข้ามไป user อื่น

                    }

                    $strDate = date('D', strtotime($dateYesterday));

                    //check working day
                    $workTime = Work_shift_time::with('work_shift')->where('day', $strDate);
                    if ($User[$i]->work_shift_id) {
                        $workTime->where('work_shift_id', $User[$i]->work_shift_id);
                    }
                    $WorkTime =  $workTime->first();

                    if ($WorkTime) {

                        //check holiday
                        $Holiday =  Holiday::where('date', $dateYesterday)->first();
                        if (!$Holiday) {

                            if ($WorkTime->status == true) {
                                //
                                $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
                                    ->where('time', 'like', '%' . $dateYesterday . '%')
                                    ->orderby('time')
                                    ->first();

                                //check leave
                                $userID =  $User[$i]->id;
                                $Leave_table_date =  Leave_table_date::with('leave_table')
                                    ->where('date', $dateYesterday)
                                    ->WhereHas('leave_table', function ($query) use ($userID) {
                                        $query->where('user_id', $userID);
                                        $query->where('status', 'approved');
                                    })
                                    ->first();

                                if ($Leave_table_date) {
                                    //leave
                                    $User_attendance =  new User_attendance();
                                    $User_attendance->date =  $dateYesterday;
                                    $User_attendance->user_id =  $User[$i]->id;
                                    $User_attendance->zkt_time_id = null;
                                    $User_attendance->type = 'leave';
                                    $User_attendance->leave_table_id = $Leave_table_date->leave_table_id;
                                    $User_attendance->status = true;
                                    $User_attendance->save();
                                } else if ($Zk_time) {

                                    if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in_end) {
                                        //late
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User[$i]->id;
                                        $User_attendance->zkt_time_id = $Zk_time->id;
                                        $User_attendance->type = 'late';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    } else {
                                        //normal
                                        $User_attendance =  new User_attendance();
                                        $User_attendance->date =  $dateYesterday;
                                        $User_attendance->user_id =  $User[$i]->id;
                                        $User_attendance->zkt_time_id = $Zk_time->id;
                                        $User_attendance->type = 'normal';
                                        $User_attendance->leave_table_id = null;
                                        $User_attendance->status = true;
                                        $User_attendance->save();
                                    }
                                } else {

                                    //miss
                                    $User_attendance =  new User_attendance();
                                    $User_attendance->date =  $dateYesterday;
                                    $User_attendance->user_id =  $User[$i]->id;
                                    $User_attendance->zkt_time_id = null;
                                    $User_attendance->type = 'miss';
                                    $User_attendance->leave_table_id = null;
                                    $User_attendance->status = true;
                                    $User_attendance->save();
                                }
                            } else {
                                //off
                                $User_attendance =  new User_attendance();
                                $User_attendance->date =  $dateYesterday;
                                $User_attendance->user_id =  $User[$i]->id;
                                $User_attendance->zkt_time_id = null;
                                $User_attendance->type = 'off';
                                $User_attendance->leave_table_id = null;
                                $User_attendance->status = true;
                                $User_attendance->save();
                            }
                        } else {
                            //off
                            $User_attendance =  new User_attendance();
                            $User_attendance->date =  $dateYesterday;
                            $User_attendance->user_id =  $User[$i]->id;
                            $User_attendance->zkt_time_id = null;
                            $User_attendance->type = 'off';
                            $User_attendance->leave_table_id = null;
                            $User_attendance->status = true;
                            $User_attendance->save();
                        }
                    }
                }
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
        }
    }




    // public function handle()
    // {
    //     DB::beginTransaction();

    //     try {

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User = User::with('position')->where('status', 1)->get();

    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime) {
    //                     if ($WorkTime->type == 'Work') {

    //                         //
    //                         $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                             ->where('time', 'like', '%' . $dateYesterday . '%')
    //                             ->orderby('time')
    //                             ->first();

    //                         if ($Zk_time) {

    //                             if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                                 //late
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'late';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             } else {
    //                                 //normal
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'normal';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             }
    //                         } else {

    //                             //miss
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = null;
    //                             $User_attendance->type = 'miss';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {
    //                         //off
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'off';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();
    //                     }
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }
    // public function handle()
    // {
    //     DB::beginTransaction();

    //     try {

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User = User::with('position')->where('status', 1)->get();

    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime->type == 'Work') {

    //                     //
    //                     $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                         ->where('first_in_time', 'like', '%' . $dateYesterday . '%')
    //                         ->first();

    //                     if ($Zk_time) {

    //                         if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                             //late
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'late';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         } else {
    //                             //normal
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'normal';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {

    //                         //miss
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'miss';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();
    //                     }
    //                 } else {
    //                     //off
    //                     $User_attendance =  new User_attendance();
    //                     $User_attendance->date =  $dateYesterday;
    //                     $User_attendance->user_id =  $User[$i]->id;
    //                     $User_attendance->zkt_time_id = null;
    //                     $User_attendance->type = 'off';
    //                     $User_attendance->leave_table_id = null;
    //                     $User_attendance->status = true;
    //                     $User_attendance->save();
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }
}
