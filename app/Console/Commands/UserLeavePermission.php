<?php

namespace App\Console\Commands;

use App\Http\Controllers\Controller;
use App\Models\Holiday;
use App\Models\Leave_permission;
use App\Models\Leave_table_date;
use App\Models\LeaveType;
use App\Models\Log;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\User_leave_permission;
use App\Models\Work_shift_time;
use App\Models\WorkTime;
use App\Models\Zk_time;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UserLeavePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'userLeavePermission:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();

        try {

            $controller = new  Controller();

            $year = date('Y');

            $User = User::where('status', 1)->get();

            for ($i = 0; $i < count($User); $i++) {

                $sex = ['all', $User[$i]->sex];
                $LeaveType = LeaveType::whereIn('sex', $sex)
                    ->where('branch_id', $User[$i]->branch_id)
                    ->get();

                for ($j = 0; $j < count($LeaveType); $j++) {

                    $checkName = User_leave_permission::where('year', $year)
                        ->where('leave_type_id', $LeaveType[$j]->id)
                        ->where('user_id', $User[$i]->id)
                        ->first();

                    if (!$checkName) {
                        // return $this->returnError('มีข้อมูลในระบบแล้ว');

                        $User_leave_permission = new User_leave_permission();

                        $User_leave_permission->year = $year;
                        $User_leave_permission->user_id = $User[$i]->id;
                        $User_leave_permission->leave_type_id = $LeaveType[$j]->id;

                        //qty


                        $work_day =  $controller->dateDiff(date('Y-m-d'), ($User[$i]->register_date ? $User[$i]->register_date : date('Y-m-d')));
                        $work_year = intval($work_day / 365);

                        $work_year  =  $controller->yearDiff($User[$i]->register_date, date('Y-m-d'));
                        // $work_year = 0;

                        $Leave_permission = Leave_permission::where('leave_type_id', $User_leave_permission->leave_type_id)
                            ->where('year', '<=',  $work_year)
                            ->orderby('year', 'desc')
                            ->first();

                        $User_leave_permission->qty = ($Leave_permission ? $Leave_permission->qty : 0);

                        $User_leave_permission->status = true;
                        $User_leave_permission->updated_at = Carbon::now()->toDateTimeString();

                        $User_leave_permission->save();
                    }
                }
            }


            DB::commit();

            $Log = new Log();
            $Log->user_id = 'admin';
            $Log->description = 'leave permission cron finish';
            $Log->type = 'cron';
            $Log->save();
        } catch (\Throwable $e) {
            DB::rollback();

            $Log = new Log();
            $Log->user_id = 'admin';
            $Log->description = 'leave permission cron error';
            $Log->type = 'cron';
            $Log->save();
        }
    }




    // public function handle()
    // {
    //     DB::beginTransaction();

    //     try {

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User = User::with('position')->where('status', 1)->get();

    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime) {
    //                     if ($WorkTime->type == 'Work') {

    //                         //
    //                         $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                             ->where('time', 'like', '%' . $dateYesterday . '%')
    //                             ->orderby('time')
    //                             ->first();

    //                         if ($Zk_time) {

    //                             if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                                 //late
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'late';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             } else {
    //                                 //normal
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'normal';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             }
    //                         } else {

    //                             //miss
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = null;
    //                             $User_attendance->type = 'miss';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {
    //                         //off
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'off';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();
    //                     }
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }
    // public function handle()
    // {
    //     DB::beginTransaction();

    //     try {

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User = User::with('position')->where('status', 1)->get();

    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime->type == 'Work') {

    //                     //
    //                     $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                         ->where('first_in_time', 'like', '%' . $dateYesterday . '%')
    //                         ->first();

    //                     if ($Zk_time) {

    //                         if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                             //late
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'late';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         } else {
    //                             //normal
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'normal';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {

    //                         //miss
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'miss';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();
    //                     }
    //                 } else {
    //                     //off
    //                     $User_attendance =  new User_attendance();
    //                     $User_attendance->date =  $dateYesterday;
    //                     $User_attendance->user_id =  $User[$i]->id;
    //                     $User_attendance->zkt_time_id = null;
    //                     $User_attendance->type = 'off';
    //                     $User_attendance->leave_table_id = null;
    //                     $User_attendance->status = true;
    //                     $User_attendance->save();
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }
}
