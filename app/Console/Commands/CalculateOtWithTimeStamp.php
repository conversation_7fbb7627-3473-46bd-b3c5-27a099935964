<?php

namespace App\Console\Commands;

use App\Http\Controllers\Controller;
use App\Http\Controllers\OtController;
use App\Models\Company;
use App\Models\Holiday;
use App\Models\Leave_permission;
use App\Models\Leave_table_date;
use App\Models\LeaveType;
use App\Models\Log;
use App\Models\User;
use App\Models\User_attendance;
use App\Models\User_leave_permission;
use App\Models\Work_shift_time;
use App\Models\WorkTime;
use App\Models\Zk_time;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculateOtWithTimeStamp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculateOtWithTimeStamp:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();

        try {

            $date = date('Y-m-d');

            $controller = new Controller();

            $User = User::with('branch.company')->query();
            $User = $User->where('status', 1)->get();

            for ($i = 0; $i < count($User); $i++) {

                $userId = $User[$i]->id;

                $Company = Company::where('id', $$User[$i]->branch->company_id)->first();

                if ($Company) {
                    if ($Company->auto_ot == true) {
                        $OtController = new OtController();
                        $updateCalculateOtWithTimeStamp = $OtController->updateCalculateOtWithTimeStamp($date, $userId);
                    }
                }
            }

            $Log = new Log();
            $Log->user_id = 'admin';
            $Log->description = 'calculate ot with time stamp cron finish';
            $Log->type = 'cron';
            $Log->save();

            DB::commit();
        } catch (\Throwable $e) {

            $Log = new Log();
            $Log->user_id = 'admin';
            $Log->description = 'calculate ot with time stamp cron error';
            $Log->type = 'cron';
            $Log->save();

            DB::rollback();
        }
    }




    // public function handle()
    // {
    //     DB::beginTransaction();

    //     try {

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User = User::with('position')->where('status', 1)->get();

    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime) {
    //                     if ($WorkTime->type == 'Work') {

    //                         //
    //                         $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                             ->where('time', 'like', '%' . $dateYesterday . '%')
    //                             ->orderby('time')
    //                             ->first();

    //                         if ($Zk_time) {

    //                             if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                                 //late
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'late';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             } else {
    //                                 //normal
    //                                 $User_attendance =  new User_attendance();
    //                                 $User_attendance->date =  $dateYesterday;
    //                                 $User_attendance->user_id =  $User[$i]->id;
    //                                 $User_attendance->zkt_time_id = $Zk_time->id;
    //                                 $User_attendance->type = 'normal';
    //                                 $User_attendance->leave_table_id = null;
    //                                 $User_attendance->status = true;
    //                                 $User_attendance->save();
    //                             }
    //                         } else {

    //                             //miss
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = null;
    //                             $User_attendance->type = 'miss';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {
    //                         //off
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'off';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();
    //                     }
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }
    // public function handle()
    // {
    //     DB::beginTransaction();

    //     try {

    //         $dateNow = date('Y-m-d');
    //         $dateYesterday = date('Y-m-d', strtotime($dateNow . ' -1 day'));

    //         $User = User::with('position')->where('status', 1)->get();

    //         for ($i = 0; $i < count($User); $i++) {

    //             //check duplicate Attendance
    //             $check = User_attendance::where('user_id', $User[$i]->id)
    //                 ->where('date', $dateYesterday)
    //                 ->first();

    //             if (!$check) {

    //                 //check working day
    //                 $workTime = WorkTime::where('date', $dateYesterday);

    //                 if ($User[$i]->position) {
    //                     if ($User[$i]->position->set_by_emp == true) {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                         $workTime->where('user_id', $User[$i]->id);
    //                     } else {
    //                         $workTime->where('position_id', $User[$i]->position_id);
    //                     }
    //                 }

    //                 $WorkTime =  $workTime->first();

    //                 if ($WorkTime->type == 'Work') {

    //                     //
    //                     $Zk_time =  Zk_time::where('personnel_id', $User[$i]->personnel_id)
    //                         ->where('first_in_time', 'like', '%' . $dateYesterday . '%')
    //                         ->first();

    //                     if ($Zk_time) {

    //                         if (date('H:i', strtotime($Zk_time->time)) > $WorkTime->time_in) {
    //                             //late
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'late';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         } else {
    //                             //normal
    //                             $User_attendance =  new User_attendance();
    //                             $User_attendance->date =  $dateYesterday;
    //                             $User_attendance->user_id =  $User[$i]->id;
    //                             $User_attendance->zkt_time_id = $Zk_time->id;
    //                             $User_attendance->type = 'normal';
    //                             $User_attendance->leave_table_id = null;
    //                             $User_attendance->status = true;
    //                             $User_attendance->save();
    //                         }
    //                     } else {

    //                         //miss
    //                         $User_attendance =  new User_attendance();
    //                         $User_attendance->date =  $dateYesterday;
    //                         $User_attendance->user_id =  $User[$i]->id;
    //                         $User_attendance->zkt_time_id = null;
    //                         $User_attendance->type = 'miss';
    //                         $User_attendance->leave_table_id = null;
    //                         $User_attendance->status = true;
    //                         $User_attendance->save();
    //                     }
    //                 } else {
    //                     //off
    //                     $User_attendance =  new User_attendance();
    //                     $User_attendance->date =  $dateYesterday;
    //                     $User_attendance->user_id =  $User[$i]->id;
    //                     $User_attendance->zkt_time_id = null;
    //                     $User_attendance->type = 'off';
    //                     $User_attendance->leave_table_id = null;
    //                     $User_attendance->status = true;
    //                     $User_attendance->save();
    //                 }
    //             }
    //         }

    //         DB::commit();
    //     } catch (\Throwable $e) {
    //         DB::rollback();
    //     }
    // }
}
