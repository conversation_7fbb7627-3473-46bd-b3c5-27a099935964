<?php

namespace App\Listeners\Message;

use App\Http\Controllers\LineWebhookController;
use Illuminate\Support\Facades\Log;
use LINE\LINEBot\Event\MessageEvent\TextMessage;
use Revolution\Line\Facades\Bot;

class TextMessageListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  TextMessage  $event
     * @return void
     */
    public function handle(TextMessage $event)
    {
        $flex = LineWebhookController::handleTextMessage($event);

        $this->SendLineFlex($event->getReplyToken(), $flex);
        // $response = Bot::replyMessage($event->getReplyToken(), $flex);

        // if (! $response->isSucceeded()) {
        //     logger()->error(static::class . $response->getHTTPStatus(), $response->getJSONDecodedBody());
        // }

    }

    public function SendLineFlex($replyToken, $data)
    {
        $accessToken = config('line.bot.channel_token'); //ENV LINE_BOT_CHANNEL_TOKEN

        Log::info('SendLineFlex', [
            'replyToken' => $replyToken,
            'data' => $data
        ]);
        $postData = [
            'replyToken' => $replyToken,
            'messages'   =>  [$data]
        ];

        $ch = curl_init('https://api.line.me/v2/bot/message/reply');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Debug
        echo "HTTP Status: $httpCode\n";
        echo "Response: $result\n";
    }
}
