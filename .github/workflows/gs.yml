name: Gs

on:
  push:
    branches: ["dev"]
#   pull_request:
#     branches: [ "develop" ]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      # - name: Install SSH Key
      #   uses: shimataro/ssh-key-action@v2
      #   with:
      #     key: ${{ secrets.SSH_PRIVATE_KEY }}
      #     known_hosts: unnecessary

      # - name: Adding Known Hosts
      #   run: ssh-keyscan -p 22 -H ${{ secrets.SSH_HOST }}  >> ~/.ssh/known_hosts

      # - name: Deploy with rsync
      #   run: rsync -avz -e "ssh -p 22" ./ ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/home/<USER>/dev-asha.com/powertech-control-api

      # - uses: appleboy/ssh-action@master
      #   with:
      #     host: ${{ secrets.SSH_HOST }}
      #     username: ${{ secrets.SSH_USER }}
      #     password: ${{ secrets.SSH_PASSWORD }}
      #     script: |
      #       cd /home/<USER>/dev-asha.com/powertech-control-api
      #       sudo bash ./deploy.sh

      - uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            cd /home/<USER>/dev-asha.com/gs-payroll-api
            git fetch
            git pull
            sudo bash ./deploy.sh
