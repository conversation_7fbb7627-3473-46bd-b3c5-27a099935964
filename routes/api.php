<?php

use App\Console\Commands\UserLeavePermission;
use App\Http\Controllers\AddressController;
use App\Http\Controllers\BankController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\ChannelController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CommissionController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ConfigController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CustomerVipController;
use App\Http\Controllers\DeductPaidController;
use App\Http\Controllers\DeductTypeController;
use App\Http\Controllers\Delivered_byController;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\DocController;
use App\Http\Controllers\EmployeeSalaryController;
use App\Http\Controllers\GradeYellowCardController;
use App\Http\Controllers\HolidayController;
use App\Http\Controllers\IncomePaidController;
use App\Http\Controllers\IncomeTypeController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\ItemLineController;
use App\Http\Controllers\ItemLotController;
use App\Http\Controllers\ItemReturnController;
use App\Http\Controllers\ItemTransController;
use App\Http\Controllers\ItemTypeController;
use App\Http\Controllers\KerryController;
use App\Http\Controllers\LeavePermissionController;
use App\Http\Controllers\LeaveTableController;
use App\Http\Controllers\LeaveTypeController;
use App\Http\Controllers\LineController;
use App\Http\Controllers\LineWebhookController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\LogController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\LotTransController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\OlafController;
use App\Http\Controllers\OtController;
use App\Http\Controllers\OtTypeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\PoController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ReportStockController;
use App\Http\Controllers\SaleOrderController;
use App\Http\Controllers\SalePageContentController;
use App\Http\Controllers\SalePageController;
use App\Http\Controllers\SalePageOrderController;
use App\Http\Controllers\ThaiPostController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\UserAttendanceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserDeductController;
use App\Http\Controllers\UserFilesController;
use App\Http\Controllers\UserIncomeController;
use App\Http\Controllers\UserLeavePermissionController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\WithDrawSalaryController;
use App\Http\Controllers\WorkAdminController;
use App\Http\Controllers\WorkAdsController;
use App\Http\Controllers\WorkingTimeController;
use App\Http\Controllers\WorkShiftController;
use App\Http\Controllers\WorkTelesaleController;
use App\Http\Controllers\WorkTimeController;
use App\Http\Controllers\LocationCheckinController;
use App\Http\Controllers\PayrollController;
use App\Http\Controllers\BonusStepController;
use App\Http\Controllers\WarningController;
use App\Http\Controllers\EmployeeDepositController;
use App\Http\Controllers\LoanApplicationController;
use App\Http\Controllers\LoanController;
use App\Http\Controllers\LoanMoratoriumController;
use App\Http\Controllers\LoanPolicyController;
use App\Http\Controllers\OtShiftController;
use App\Http\Controllers\PayrollContributionSettingController;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\WarningPunishmentTypeController;
use App\Http\Controllers\WarningTypeController;
use App\Http\Controllers\ZkController;
use App\Models\IncomePaid;
use App\Models\LeavePermission;
use App\Models\LocationCheckin;
use App\Models\Payroll;
use App\Models\Sale_page;
use App\Models\Sale_page_order;
use App\Models\Warning;
use Illuminate\Support\Facades\Route;
use Revolution\Line\Messaging\Http\Controllers\WebhookController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });

//////////////////////////////////////////web no route group/////////////////////////////////////////////////////
//Login Admin
Route::post('/login', [LoginController::class, 'login']);

Route::post('/check_login', [LoginController::class, 'checkLogin']);

//user
Route::post('/create_admin', [UserController::class, 'createUserAdmin']);
Route::get('/get_userID', [UserController::class, 'showUser']);


//format import
Route::get('/download_format_import/{params}', [Controller::class, 'getDownloadFomatImport']);


Route::post('/forgot_password_user', [UserController::class, 'ForgotPasswordUser']);

Route::post('upload_images', [Controller::class, 'uploadImage1']);
Route::post('upload_file', [Controller::class, 'uploadFile2']);


//Route::resource('checkout_product',CheckoutController::class);
// Route::post('/checkout_product', [CheckoutController::class, 'Pushstore']);

// Route::put('/update_check/{id}', [CheckoutController::class, 'UpdateCheckout']);

// Route::get('/getcheckout_id/{id}', [CheckoutController::class, 'ShowDetail']);

//dropdown
Route::get('/get_user_line', [UserController::class, 'getUserLine']);


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////


// Route::get('/getcheckout', [CheckoutController::class, 'getcheckout']);

// //salePage_Order
// Route::resource('sale_pages_order', SalePageOrderController::class);
// Route::post('/sale_pages_order', [SalePageOrderController::class, 'SalePageOrder']);

Route::get('/cut_text_order', [Controller::class, 'cutTextOrder']);

//zk time
Route::post('/add_zk_time', [ZkController::class, 'addZkTime']);
Route::post('/put_zk_time', [ZkController::class, 'putZkTime']);
Route::post('/put_zk_time_camera', [ZkController::class, 'putZkTimeCamera']);



//Address
Route::get('/get_province', [AddressController::class, 'getProvince']);
Route::post('/get_district', [AddressController::class, 'getDistrict']);
Route::post('/get_sub_district', [AddressController::class, 'getSubDistrict']);
Route::post('/get_address', [AddressController::class, 'getAddress']);


// //OtType
// Route::get('get_ot_type', [OtTypeController::class, 'getOtType']);

//ot
Route::post('/ot_page_line', [OtController::class, 'OtPageLine']);

//leave
Route::post('/leave_table_page_line', [LeaveTableController::class, 'LeaveTablePageLine']);

//Warning
Route::post('/warning_page_line', [WarningController::class, 'WarningPageLine']);

//WithDrawSalary
Route::post('/withdraw_salary_page_line', [WithDrawSalaryController::class, 'WithDrawSalaryPageLine']);

//api for line no auth
Route::post('/leave_line', [LeaveTableController::class, 'addLine']);
Route::post('/ot_line', [OtController::class, 'addLine']);
Route::post('/with_draw_salary_line', [WithDrawSalaryController::class, 'addLine']);
Route::get('/get_user_leave_permission_line', [UserLeavePermissionController::class, 'getUserLeavePermissionForLine']);

Route::put('/approved_ot_line/{id}', [OtController::class, 'ApprovedOtLine']);
Route::put('/approved_leave_table_line/{id}', [LeaveTableController::class, 'ApprovedLeaveTableLine']);
Route::put('/update_warning_line/{id}', [WarningController::class, 'UpdateWarningLine']);


Route::get('/user_profile_line', [UserController::class, 'getProfileUserLine']);

// cancheckin ไม่ต้อง login
Route::get('/can_check_in', [LocationCheckinController::class, 'canCheckIn']);


//OtType
Route::get('get_ot_type_line', [OtTypeController::class, 'getOtTypeLine']);

//WarningType
Route::get('get_warning_type_line', [WarningTypeController::class, 'getWarningTypeLine']);

//WarningPunishmentType
Route::get('get_warning_punishment_type_line', [WarningPunishmentTypeController::class, 'getWarningPunishmentTypeLine']);



Route::group(['middleware' => 'checkjwt'], function () {


    Route::post('/zk_time_page', [ZkController::class, 'ZkTimePage']);


    Route::post('/login_change_branch', [LoginController::class, 'loginChangeBranch']);

    Route::get('preview_doc', [PdfController::class, 'previewDoc']);

    //OtType
    Route::get('get_ot_type', [OtTypeController::class, 'getOtTypeWeb']);

    //WarningType
    Route::get('get_warning_type', [WarningTypeController::class, 'getWarningTypeWeb']);

    //WarningPunishment
    Route::get('get_warning_punishment_type', [WarningPunishmentTypeController::class, 'getWarningPunishmentTypeWeb']);


    // Location Check-In
    Route::resource('location-checkin', LocationCheckinController::class);
    Route::post('/location-checkin/datatable', [LocationCheckinController::class, 'datatable']);


    Route::get('/cronAddLeavePermission', [UserLeavePermissionController::class, 'cronAddLeavePermission']);

    //olaf api
    Route::get('/get_group', [OlafController::class, 'getGroup']);
    Route::get('/get_role', [OlafController::class, 'getRole']);
    Route::get('/get_departments', [OlafController::class, 'getDepartment']);
    // Route::get('/punchlog', [OlafController::class, 'punchlog']);


    //Zk
    Route::get('/punchlog', [ZkController::class, 'punchlog']);



    //Config
    Route::resource('config', ConfigController::class);


    //Sale_page_content

    // Route::resource('sale_pages_contents', SalePageContentController::class);

    //salary
    Route::resource('salary', EmployeeSalaryController::class);
    Route::get('/get_salary', [EmployeeSalaryController::class, 'getsalary']);
    Route::post('/salary_page', [EmployeeSalaryController::class, 'salaryPage']);

    //Bank
    Route::resource('bank', BankController::class);
    Route::get('/get_bank', [BankController::class, 'getBank']);
    Route::post('/bankPage_page', [BankController::class, 'BankPage']);
    Route::post('/reset_bank', [BankController::class, 'Bankupdate']);


    //company
    Route::get('get_company', [CompanyController::class, 'getCompany']);
    Route::resource('company', CompanyController::class);
    Route::post('/company_page', [CompanyController::class, 'CompanyPage']);
    Route::post('/update_company', [CompanyController::class, 'update']);
    // Route::post('/import_company', [CompanyController::class, 'ImportCompany']);

    //postition
    Route::resource('position', PositionController::class);
    Route::post('/position_page', [PositionController::class, 'PositionPage']);
    Route::post('/import_position', [PositionController::class, 'ImportPosition']);
    Route::get('/get_position', [PositionController::class, 'getPosition']);


    //department
    Route::get('/get_department', [DepartmentController::class, 'getDepartment']);
    Route::resource('department', DepartmentController::class);
    Route::post('/department_page', [DepartmentController::class, 'DepartmentPage']);
    Route::post('/import_department', [DepartmentController::class, 'ImportDepartment']);

    //branch
    Route::get('get_branch', [BranchController::class, 'getBranch']);
    Route::resource('branch', BranchController::class);
    Route::post('/branch_page', [BranchController::class, 'BranchPage']);
    Route::post('/import_branch', [BranchController::class, 'ImportBranch']);

    //Holiday
    Route::get('get_holiday', [HolidayController::class, 'getHoliday']);
    Route::resource('holiday', HolidayController::class);
    Route::post('/holiday_page', [HolidayController::class, 'HolidayPage']);
    Route::post('/import_holiday', [HolidayController::class, 'ImportHoliday']);

    //WorkShift
    Route::get('get_work_shift', [WorkShiftController::class, 'getWorkShift']);
    Route::resource('work_shift', WorkShiftController::class);
    Route::post('/work_shift_page', [WorkShiftController::class, 'WorkShiftPage']);
    Route::get('get_work_shift_calendar', [WorkShiftController::class, 'getWorkShiftCalendar']);

    //  Route::post('/import_work_shift', [WorkShiftController::class, 'ImportWorkShift']);


    //select
    Route::post('/register', [UserController::class, 'registerUser']);
    //permission
    Route::get('/get_permission', [PermissionController::class, 'getPermission']);
    //
    Route::post('/users/line-generate/{id}', [UserController::class, 'lineGenerate']);
    Route::post('/users/{id}/update-policy-limit', [UserController::class, 'updatePolicyLimit']);


    //report
    Route::post('report_time_attendance', [ReportController::class, 'ReportTimeAttendance']);
    Route::post('report_sale_order_item_delivery', [ReportController::class, 'ReportSaleOrderItemDelivery']);

    Route::post('report_sale_telesale_calls', [ReportController::class, 'ReportSalesTeleSaleCall']);
    Route::post('report_sales_telesale', [ReportController::class, 'ReportSalesTeleSale']);
    Route::post('report_sales_admin', [ReportController::class, 'ReportSalesAdmin']);
    Route::post('report_sales_admin_new', [ReportController::class, 'ReportSalesAdminNew']);

    Route::get('/get_dashboard', [ReportController::class, 'getDashboard']);
    Route::get('/parseAddress', [ReportController::class, 'parseAddress']);


    Route::post('/report_stock_item', [ReportController::class, 'ReportStockItem']);
    Route::post('/report_tans_item', [ReportController::class, 'ReportTansItem']);
    Route::post('/report_item_type_stock', [ReportController::class, 'ReportIemTypeStock']);
    Route::post('/report_stockFG', [ReportController::class, 'ReportStockFG']);
    Route::post('/report_item_lot', [ReportController::class, 'ReportItemLot']);
    Route::post('/report_forcash', [ReportController::class, 'ReportForcash']);

    Route::post('/report_sale_order', [ReportController::class, 'ReportSaleOrder']);
    Route::post('/report_delevery_order', [ReportController::class, 'ReportDeleveryOrder']);

    Route::post('/report_stock_slow', [ReportController::class, 'ReportStockSlow']);
    Route::post('/report_stock_dead', [ReportController::class, 'ReportStockDead']);

    Route::post('/report_job', [ReportController::class, 'ReportJob']);

    Route::post('/report_machine', [ReportController::class, 'ReportMachine']);
    Route::post('/report_mantenance', [ReportController::class, 'ReportMantenance']);
    Route::post('/report_result', [ReportController::class, 'ReportResult']);

    Route::post('/report_ng', [ReportController::class, 'ReportNG']);

    Route::post('/report_maintenance_plan_component', [ReportController::class, 'ReportMaintenanceplanComponent']);

    Route::post('/report_item_machine', [ReportController::class, 'ReportItemMachine']);

    Route::post('/report_stock_on_due', [ReportController::class, 'ReportStockOnDue']);

    Route::post('/report_item_ng', [ReportController::class, 'ReportItemNG']);

    Route::post('/report_item_top_sale', [ReportController::class, 'ReportItemTopSale']);
    Route::post('/report_item_dead_stock', [ReportController::class, 'ReportItemDeadStock']);

    Route::post('/report_user_yellow_card', [ReportController::class, 'ReportUserYellowCard']);
    Route::post('/report_summary_sale', [ReportController::class, 'reportSummarySale']);
    Route::post('/report_ad_team_summary_sale', [ReportController::class, 'ReportAdTeamSummarySale']);

    Route::post('/report_bonus_step', [ReportController::class, 'reportBonusStep']);






    //

    Route::get('/get_warehouse', [WarehouseController::class, 'getWarehouse']);
    Route::resource('warehouse', WarehouseController::class);
    Route::post('/warehouse_page', [WarehouseController::class, 'WarehousePage']);


    // Permission
    Route::resource('permission', PermissionController::class);
    Route::post('/permission_page', [PermissionController::class, 'PermissionPage']);
    Route::get('/get_permisson_user', [PermissionController::class, 'getPermissonUser']);
    Route::post('/get_permisson_menu', [PermissionController::class, 'getPermissonMenu']);

    //menu
    // Route::resource('menu', MenuController::class);
    Route::get('/get_menu', [MenuController::class, 'getMenu']);

    //userfile
    Route::get('/get_user_file', [UserFilesController::class, 'getFiles']);
    Route::post('/addFiles', [UserFilesController::class, 'addFiles']);
    Route::resource('user_file', UserFilesController::class);

    //user
    Route::resource('user', UserController::class);
    Route::get('/user_profile', [UserController::class, 'getProfileUser']);
    Route::get('/get_user', [UserController::class, 'getUser']);

    Route::post('/activate_user_page', [UserController::class, 'ActivateUserPage']);
    Route::post('/update_user', [UserController::class, 'updateUser']);
    Route::post('/user_page', [UserController::class, 'UserPage']);
    Route::put('/reset_password_user/{id}', [UserController::class, 'ResetPasswordUser']);
    Route::post('/update_profile_user', [UserController::class, 'updateProfileUser']);
    Route::delete('delete_user/{id}', [UserController::class, 'deleteUser']);
    Route::put('/activate_user/{id}', [UserController::class, 'ActivateUser']);
    Route::put('/update_password_user/{id}', [UserController::class, 'updatePasswordUser']);
    Route::post('/import_user', [UserController::class, 'ImportUser']);
    Route::get('/get_last_user_id', [UserController::class, 'getLastUserID']);
    Route::post('/get_user_payroll_page', [UserController::class, 'getUserPayroll']);
    Route::post('/get_slip_salary', [UserController::class, 'getSlipSalary']);
    Route::put('/update_salary_user/{id}', [UserController::class, 'updateSalaryUser']);
    Route::post('/import_user_salary', [UserController::class, 'ImportUserSalary']);
    Route::post('/import_user_zk_time', [UserController::class, 'ImportUserZkTime']);
    Route::post('/import_user_salary_social', [UserController::class, 'ImportUserSalarySocial']);
    Route::post('/import_user_bond_required', [UserController::class, 'ImportUserBondRequired']);



    //UserAttendance
    Route::post('/user_attendance_page', [UserAttendanceController::class, 'getPage']);
    Route::get('/attendance', [UserAttendanceController::class, 'attendance']);
    Route::post('/put_attendance', [UserAttendanceController::class, 'putAttendance']);



    //Leave Type
    Route::resource('leave_type', LeaveTypeController::class);
    Route::post('/leave_type_page', [LeaveTypeController::class, 'getPage']);
    Route::get('/get_leave_type', [LeaveTypeController::class, 'getList']);


    //User leave permission
    Route::resource('user_leave_permission', UserLeavePermissionController::class);
    Route::get('/get_user_leave_permission', [UserLeavePermissionController::class, 'getUserLeavePermission']);
    Route::post('/user_leave_permission_page', [UserLeavePermissionController::class, 'Page']);
    Route::post('/update_user_leave_permission', [UserLeavePermissionController::class, 'updateUserLeavePermission']);

    //grade yellow card
    Route::resource('grade_yellow_card', GradeYellowCardController::class);
    Route::post('/grade_yellow_card_page', [GradeYellowCardController::class, 'GradeYellowCardPage']);
    Route::get('/get_grade_yellow_card', [GradeYellowCardController::class, 'getGradeYellowCard']);


    //Doc
    Route::resource('doc', DocController::class);
    Route::post('/doc_page', [DocController::class, 'DocPage']);


    //Income Type
    Route::resource('income_type', IncomeTypeController::class);
    Route::post('/income_type_page', [IncomeTypeController::class, 'getPage']);
    Route::get('/get_income_type', [IncomeTypeController::class, 'getList']);

    //Deduct Type
    Route::resource('deduct_type', DeductTypeController::class);
    Route::post('/deduct_type_page', [DeductTypeController::class, 'getPage']);
    Route::get('/get_deduct_type', [DeductTypeController::class, 'getList']);

    //Leave Table
    Route::resource('leave', LeaveTableController::class);
    Route::post('/leave_page', [LeaveTableController::class, 'getPage']);
    Route::post('/leave_table_page', [LeaveTableController::class, 'LeaveTablePage']);
    Route::get('/get_leave', [LeaveTableController::class, 'getList']);

    //User Deduct
    Route::resource('user_deduct', UserDeductController::class);
    Route::get('/getDeductwithUser/{id}', [UserDeductController::class, 'getDeductwithUser']);
    Route::post('/user_deduct_page', [UserDeductController::class, 'getPage']);
    Route::get('/get_user_deduct/{id}', [UserDeductController::class, 'getList']);

    //User Income
    Route::resource('user_income', UserIncomeController::class);
    Route::get('/getIncomewithUser/{id}', [UserIncomeController::class, 'getIncomewithUser']);
    Route::post('/user_income_page', [UserIncomeController::class, 'getPage']);
    Route::get('/get_user_income/{id}', [UserIncomeController::class, 'getList']);

    //Income
    Route::resource('income_paid', IncomePaidController::class);
    Route::post('/income_paid_page', [IncomePaidController::class, 'getPage']);
    Route::get('/get_income_paid/{id}', [IncomePaidController::class, 'getList']);

    //Deduct
    Route::resource('deduct_paid', DeductPaidController::class);
    Route::post('/deduct_paid_page', [DeductPaidController::class, 'getPage']);
    Route::get('/get_deduct_paid/{id}', [DeductPaidController::class, 'getList']);


    //Commission
    Route::get('get_commission', [CommissionController::class, 'getCommission']);
    Route::resource('commission', CommissionController::class);
    Route::post('/commission_page', [CommissionController::class, 'CommissionPage']);

    //ot type
    Route::resource('ot_type', OtTypeController::class);
    Route::post('/ot_type_page', [OtTypeController::class, 'OtTypePage']);

    //Warning type
    Route::resource('warning_type', WarningTypeController::class);
    Route::post('/warning_type_page', [WarningTypeController::class, 'WarningTypePage']);


    //Warning Punishment type
    Route::resource('warning_punishment_type', WarningPunishmentTypeController::class);
    Route::post('/warning_punishment_type_page', [WarningPunishmentTypeController::class, 'WarningPunishmentTypePage']);

    //ot_shift
    Route::get('get_ot_shift', [OtShiftController::class, 'getOtShift']);
    Route::resource('ot_shift', OtShiftController::class);
    Route::post('/ot_shift_page', [OtShiftController::class, 'OtShiftPage']);


    //ot
    Route::get('get_ot', [OtController::class, 'getOt']);
    Route::resource('ot', OtController::class);
    Route::post('/ot_page', [OtController::class, 'OtPage']);
    Route::put('/approved_ot/{id}', [OtController::class, 'ApprovedOt']);
    Route::put('/update_time_ot/{id}', [OtController::class, 'updateTimeOt']);
    Route::get('get_qty_hour_ot_user', [OtController::class, 'getQtyHourOtUser']);
    Route::get('update_time_ot', [OtController::class, 'updateTimeOt']);


    Route::get('cronjobCalculateOtWithTimeStamp', [OtController::class, 'cronjobCalculateOtWithTimeStamp']);


    //Leave Permission
    Route::get('get_leave_permission', [LeavePermissionController::class, 'getLeavePermission']);
    Route::resource('leave_permission', LeavePermissionController::class);
    Route::post('/leave_permission_page', [LeavePermissionController::class, 'LeavePermissionPage']);

    //LeaveTable
    Route::get('get_leave_table', [LeaveTableController::class, 'getLeaveTable']);
    Route::resource('leave_table', LeaveTableController::class);
    Route::put('/approved_leave_table/{id}', [LeaveTableController::class, 'ApprovedLeaveTable']);

    //

    //withdraw salaray
    Route::get('get_withdraw_salary', [WithDrawSalaryController::class, 'get']);
    Route::post('/withdraw_salary_page', [WithDrawSalaryController::class, 'Page']);
    Route::resource('withdraw_salary', WithDrawSalaryController::class);
    Route::put('/approved_withdraw_salary/{id}', [WithDrawSalaryController::class, 'Approved']);



    Route::post('/update-line', [LineController::class, 'updateLine']);

    //log
    Route::post('/log_page', [LogController::class, 'LogPage']);
    Route::get('/get_log_type', [LogController::class, 'getLogType']);


    //PayrollContributionSetting
    Route::get('get_payroll_contribution_setting', [PayrollContributionSettingController::class, 'getPayrollContributionSetting']);
    Route::resource('payroll_contribution_setting', PayrollContributionSettingController::class);
    Route::post('/payroll_contribution_setting_page', [PayrollContributionSettingController::class, 'PayrollContributionSettingPage']);



    //Payroll
    Route::resource('payroll', PayrollController::class);
    Route::post('/payroll_page', [PayrollController::class, 'PayrollPage']);
    Route::get('/get_payroll', [PayrollController::class, 'getPayroll']);
    Route::put('/process_payroll/{id}', [PayrollController::class, 'process']);
    Route::post('/process_payroll_all', [PayrollController::class, 'processAll']);
    Route::post('/calculate_process', [PayrollController::class, 'calculateProcess']);

    Route::get('/get_payroll_round', [PayrollController::class, 'getPayrollRound']);

    // ปรับแก้สถานะ/ยอดคงเหลือ เงินประกัน user
    Route::put('/users/{user_id}/bond', [PayrollController::class, 'updateBondStatus']);



    // เบี้ยขยัน
    Route::resource('bonus_steps', BonusStepController::class);
    Route::post('/bonus_steps_page', [BonusStepController::class, 'getPageBonusStep']);



    // ใบเตือนพนักงาน
    Route::resource('warning', WarningController::class);
    Route::post('/warning_page', [WarningController::class, 'WarningPage']);
    Route::post('/report_warnings', [WarningController::class, 'reportWarnings']);


    // เงินฝากพนักงงาน
    Route::resource('employee_deposit', EmployeeDepositController::class);
    Route::post('/employee_deposit_page', [EmployeeDepositController::class, 'getPage']);
    Route::post('/employee_withdraw', [EmployeeDepositController::class, 'withdrawEmployee']);

    // เงินกู้
    Route::get('loans/{id}', [LoanController::class, 'show']);
    Route::post('loans', [LoanController::class, 'store']);
    Route::post('loans/page', [LoanController::class, 'datatables']);
    Route::post('loans/check-eligibility', [LoanController::class, 'checkEligibility']);
    Route::post('loans/calc-installment', [LoanController::class, 'calculateInstallment']);

    // ใบขอกู้
    Route::post('loan-applications/page', [LoanApplicationController::class, 'datatables']);
    Route::post('loan-applications/{id}/approve', [LoanApplicationController::class, 'approve']);
    Route::resource('loan-applications', LoanApplicationController::class);

    // คำขอพักชำระ
    Route::post('loan-moratoriums/page', [LoanMoratoriumController::class, 'datatables']);
    Route::post('loan-moratoriums/{id}/approve', [LoanMoratoriumController::class, 'approve']);
    Route::resource('loan-moratoriums', LoanMoratoriumController::class);

    // ตั้งค่าเกินกู้
    Route::get('loan-policies', [LoanPolicyController::class, 'index']);
    Route::put('loan-policies', [LoanPolicyController::class, 'update']);
});


//export
Route::get('/export_user', [UserController::class, 'ExportUser']);

Route::get('/export_time_in_out', [UserAttendanceController::class, 'ExportTimeInOut']);
Route::get('/report_salary_user', [ReportController::class, 'ReportSalaryUser']);
Route::get('/exportExcel_tiktokshopsales', [ReportController::class, 'ExportExcel_tiktokshopsales']);




Route::get('/export_log', [LogController::class, 'ExportLog']);



// Route::get('/mercari', [WorkTelesaleController::class, 'mercari']);

//Boom_Natthaphon
Route::get('/report_DailyReport', [ReportController::class, 'Report_DailyReport']);
Route::get('/Export_DailyReport_Excel', [ReportController::class, 'Export_DailyReport_Excel']);
Route::get('/report_MonthlyReport', [ReportController::class, 'Report_MonthlyReport']);
Route::get('/Export_MonthlyReport_Excel', [ReportController::class, 'Export_MonthlyReport_Excel']);
Route::get('/report_MonthlyReportJSON', [ReportController::class, 'Report_MonthlyReportJSON']);
Route::get('/report_IndividualReport', [ReportController::class, 'Report_IndividualReport']);
Route::get('/Export_IndividualReport_Excel', [ReportController::class, 'Export_IndividualReport_Excel']);
Route::get('/report_Report_WorkTimeReport', [ReportController::class, 'Report_WorkTimeReport']);
Route::get('/exportExcel_WorkTimeReport_Excel', [ReportController::class, 'Export_WorkTimeReport_Excel']);
Route::get('/report/getIncomeAndDeduct', [ReportController::class, 'getUserDetails']);
Route::get('/report/getPayrollReport', [ReportController::class, 'getPayrollReport']);
Route::get('/Export/Export_getPayrool_Excel', [ReportController::class, 'Export_getPayrool_Excel']);
// Route::post('/Import/Import_Payrool_Excel', [ReportController::class, 'Import_Payrool_Excel']);
Route::post('/Import/Import_Slip_Zip', [ReportController::class, 'Import_Slip_Zip']);



Route::get('/export_bank', [PayrollController::class, 'exportBank']);
Route::get('/export_social', [PayrollController::class, 'exportSocial']);


Route::get('/TestLine', [LineWebhookController::class, 'TestLine']);


//pdf
Route::get('/tax_book_fifty', [PdfController::class, 'tax_book_fifty']);
Route::get('/attached_pnk', [PdfController::class, 'attached_pnk']);

// Equipment Management Routes
Route::prefix('equipment')->group(function () {
    // Equipment Categories
    Route::apiResource('categories', App\Http\Controllers\EquipmentCategoryController::class);

    // Equipment CRUD
    Route::apiResource('equipments', App\Http\Controllers\EquipmentController::class);

    // Equipment Borrowing
    Route::apiResource('borrowings', App\Http\Controllers\EquipmentBorrowingController::class);

    // Additional borrowing actions
    Route::post('borrowings/{id}/approve', [App\Http\Controllers\EquipmentBorrowingController::class, 'approve']);
    Route::post('borrowings/{id}/mark-borrowed', [App\Http\Controllers\EquipmentBorrowingController::class, 'markAsBorrowed']);
    Route::post('borrowings/{id}/return', [App\Http\Controllers\EquipmentBorrowingController::class, 'returnEquipment']);
    Route::post('borrowings/{id}/cancel', [App\Http\Controllers\EquipmentBorrowingController::class, 'cancel']);

    // Equipment Reports
    Route::prefix('reports')->group(function () {
        Route::get('equipment-status-summary', [App\Http\Controllers\EquipmentReportController::class, 'equipmentStatusSummary']);
        Route::get('borrowing-statistics', [App\Http\Controllers\EquipmentReportController::class, 'borrowingStatistics']);
        Route::get('overdue-equipment', [App\Http\Controllers\EquipmentReportController::class, 'overdueEquipment']);
        Route::get('equipment-utilization', [App\Http\Controllers\EquipmentReportController::class, 'equipmentUtilization']);
        Route::get('user-borrowing-history', [App\Http\Controllers\EquipmentReportController::class, 'userBorrowingHistory']);
        Route::get('equipment-maintenance', [App\Http\Controllers\EquipmentReportController::class, 'equipmentMaintenance']);
        Route::get('dashboard-summary', [App\Http\Controllers\EquipmentReportController::class, 'dashboardSummary']);
    });
});
