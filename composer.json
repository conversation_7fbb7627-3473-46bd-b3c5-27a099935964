{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "berkayk/onesignal-laravel": "^1.0", "fideloper/proxy": "^4.4", "firebase/php-jwt": "^5.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.5", "laravel/framework": "^8.12", "laravel/socialite": "^5.1", "laravel/tinker": "^2.5", "maatwebsite/excel": "^3.1", "mpdf/mpdf": "^8.0", "nesbot/carbon": "^2.68", "phpoffice/phpspreadsheet": "^1.29", "phpoffice/phpword": "^0.18.2", "revolution/laravel-line-sdk": "^1.3"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}